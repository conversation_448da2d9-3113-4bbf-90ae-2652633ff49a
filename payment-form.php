<?php
require_once "includes/payment-gateway-manager.php";
include "header.php";

$payment_type = $_GET["type"] ?? "service";
$is_donation = ($payment_type === "donation");
$preset_amount = $_GET["amount"] ?? "";

$gatewayManager = new Payment_Gateway_Manager();
$availableGateways = $gatewayManager->getAvailableGateways();
?>

<main>
  <section class="hero">
    <h1><?php echo $is_donation ? "Make a Cryptocurrency Donation" : "Pay with Cryptocurrency"; ?></h1>
  </section>

  <section class="section" id="payment-form">
    <h2>Choose Your Payment Method</h2>

    <?php
    // Display error messages if any
    if (isset($_GET['error'])) {
        $error = $_GET['error'];
        $details = $_GET['details'] ?? '';

        echo '<div style="background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin-bottom: 20px;">';
        echo '<h3>❌ Error</h3>';
        echo '<p>' . htmlspecialchars($error) . '</p>';
        if ($details) echo '<p><strong>Details:</strong> ' . htmlspecialchars($details) . '</p>';
        echo '</div>';
    }
    ?>

    <div style="background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin-bottom: 30px;">
        <h3>🚀 Multiple Payment Options Available</h3>
        <p>Choose from Bitcoin-only payments via our self-hosted BTCPay Server, or select from 300+ cryptocurrencies via NowPayments.</p>
    </div>

    <form method="POST" action="multi-gateway-process-payment.php" style="max-width: 800px; margin: 0 auto;">
      <input type="hidden" name="payment_type" value="<?php echo htmlspecialchars($payment_type); ?>">

      <!-- Payment Gateway Selection -->
      <div style="margin-bottom: 30px;">
        <h3>Step 1: Choose Payment Gateway</h3>
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">

          <?php foreach ($availableGateways as $gatewayId => $gateway): ?>
          <div class="gateway-option" style="border: 2px solid #ddd; border-radius: 10px; padding: 20px; cursor: pointer; transition: all 0.3s;"
               onclick="selectGateway('<?php echo $gatewayId; ?>')">
            <input type="radio" name="gateway" value="<?php echo $gatewayId; ?>" id="gateway_<?php echo $gatewayId; ?>"
                   style="margin-bottom: 10px;" <?php echo $gatewayId === 'btcpay' ? 'checked' : ''; ?>>
            <label for="gateway_<?php echo $gatewayId; ?>" style="cursor: pointer;">
              <h4><?php echo $gateway['icon']; ?> <?php echo $gateway['name']; ?></h4>
              <p style="margin: 10px 0; color: #666;"><?php echo $gateway['description']; ?></p>
              <div style="margin: 10px 0;">
                <strong>Features:</strong>
                <ul style="margin: 5px 0; padding-left: 20px; font-size: 0.9em;">
                  <?php foreach ($gateway['features'] as $feature): ?>
                  <li><?php echo $feature; ?></li>
                  <?php endforeach; ?>
                </ul>
              </div>
              <div style="margin: 10px 0;">
                <strong>Currencies:</strong>
                <span style="font-size: 0.9em; color: #666;">
                  <?php echo implode(', ', array_slice($gateway['supported_currencies'], 0, 5)); ?>
                  <?php if (count($gateway['supported_currencies']) > 5): ?>
                  + <?php echo count($gateway['supported_currencies']) - 5; ?> more
                  <?php endif; ?>
                </span>
              </div>
            </label>
          </div>
          <?php endforeach; ?>

        </div>
      </div>

      <!-- Currency Selection -->
      <div style="margin-bottom: 20px;">
        <h3>Step 2: Choose Cryptocurrency</h3>
        <select id="currency" name="currency" required
                style="width: 100%; padding: 10px; border: 1px solid #ccc; border-radius: 5px;">
          <option value="">Select cryptocurrency...</option>
          <!-- Options will be populated by JavaScript based on gateway selection -->
        </select>
        <small style="color: #666;">Available currencies depend on selected payment gateway</small>
      </div>

      <!-- Payment Details -->
      <div style="margin-bottom: 20px;">
        <h3>Step 3: Payment Details</h3>

        <div style="margin-bottom: 20px;">
          <label for="amount" style="display: block; margin-bottom: 5px; font-weight: bold;">Amount (USD):</label>
          <input type="number" id="amount" name="amount" min="1" step="0.01" required
                 value="<?php echo htmlspecialchars($preset_amount); ?>"
                 style="width: 100%; padding: 10px; border: 1px solid #ccc; border-radius: 5px;">
          <small style="color: #666;">Minimum: $1.00 USD</small>
        </div>

        <div style="margin-bottom: 20px;">
          <label for="email" style="display: block; margin-bottom: 5px; font-weight: bold;">Email Address:</label>
          <input type="email" id="email" name="email" required
                 style="width: 100%; padding: 10px; border: 1px solid #ccc; border-radius: 5px;"
                 placeholder="<EMAIL>">
          <small style="color: #666;">For payment confirmation and receipt</small>
        </div>

        <?php if (!$is_donation): ?>
        <div style="margin-bottom: 20px;">
          <label for="service" style="display: block; margin-bottom: 5px; font-weight: bold;">Service Type:</label>
          <select id="service" name="service" required
                  style="width: 100%; padding: 10px; border: 1px solid #ccc; border-radius: 5px;">
            <option value="consulting">Crypto & Forex Consulting ($150/hour)</option>
            <option value="mining-pool">Mining Pool Membership ($200/year)</option>
            <option value="mining-services">Mining Services ($500/month)</option>
            <option value="analysis">Market Analysis Report ($99/report)</option>
            <option value="other">Other Services</option>
          </select>
        </div>
        <?php endif; ?>

        <div style="margin-bottom: 20px;">
          <label for="description" style="display: block; margin-bottom: 5px; font-weight: bold;">Description/Notes:</label>
          <textarea id="description" name="description" rows="3"
                    style="width: 100%; padding: 10px; border: 1px solid #ccc; border-radius: 5px;"
                    placeholder="<?php echo $is_donation ? "Optional message with your donation" : "Describe the service you are paying for"; ?>"></textarea>
        </div>
      </div>

      <div style="background: #fff3cd; padding: 15px; border-radius: 5px; margin-bottom: 20px;">
        <h4>💡 Payment Gateway Comparison:</h4>
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; font-size: 0.9em;">
          <div>
            <strong>⚡ BTCPay Server:</strong>
            <ul style="margin: 5px 0; padding-left: 15px;">
              <li>No transaction fees</li>
              <li>Lightning Network (instant)</li>
              <li>Self-hosted & private</li>
              <li>Bitcoin only</li>
            </ul>
          </div>
          <div>
            <strong>🌐 NowPayments:</strong>
            <ul style="margin: 5px 0; padding-left: 15px;">
              <li>300+ cryptocurrencies</li>
              <li>Low fees (0.5-1.5%)</li>
              <li>Global support</li>
              <li>Easy integration</li>
            </ul>
          </div>
        </div>
      </div>

      <div style="text-align: center; margin: 30px 0;">
        <button type="submit" class="cta-button" style="padding: 15px 30px; font-size: 1.1em;">
          🚀 Proceed to Payment
        </button>
      </div>
    </form>

    <div style="text-align: center; margin-top: 30px;">
      <p><a href="index.php">← Back to Homepage</a></p>
    </div>
  </section>
</main>

<script>
// Gateway and currency data
const gatewayCurrencies = {
  'btcpay': [
    {value: 'BTC', text: '₿ Bitcoin (BTC) - Lightning & On-chain'}
  ],
  'nowpayments': [
    {value: 'btc', text: '₿ Bitcoin (BTC)'},
    {value: 'eth', text: 'Ξ Ethereum (ETH)'},
    {value: 'usdt', text: '₮ Tether (USDT)'},
    {value: 'usdc', text: '$ USD Coin (USDC)'},
    {value: 'ltc', text: 'Ł Litecoin (LTC)'},
    {value: 'bch', text: '₿ Bitcoin Cash (BCH)'},
    {value: 'trx', text: 'T Tron (TRX)'},
    {value: 'bnb', text: 'B Binance Coin (BNB)'},
    {value: 'ada', text: '₳ Cardano (ADA)'},
    {value: 'dot', text: '● Polkadot (DOT)'},
    {value: 'xrp', text: 'X Ripple (XRP)'},
    {value: 'matic', text: 'M Polygon (MATIC)'}
  ]
};

function selectGateway(gatewayId) {
  // Update radio button
  document.getElementById('gateway_' + gatewayId).checked = true;

  // Update visual selection
  document.querySelectorAll('.gateway-option').forEach(option => {
    option.style.borderColor = '#ddd';
    option.style.backgroundColor = 'transparent';
  });

  event.currentTarget.style.borderColor = '#0077cc';
  event.currentTarget.style.backgroundColor = '#f0f8ff';

  // Update currency options
  updateCurrencyOptions(gatewayId);
}

function updateCurrencyOptions(gatewayId) {
  const currencySelect = document.getElementById('currency');
  currencySelect.innerHTML = '<option value="">Select cryptocurrency...</option>';

  if (gatewayCurrencies[gatewayId]) {
    gatewayCurrencies[gatewayId].forEach(currency => {
      const option = document.createElement('option');
      option.value = currency.value;
      option.textContent = currency.text;
      currencySelect.appendChild(option);
    });
  }
}

// Initialize with default gateway
document.addEventListener('DOMContentLoaded', function() {
  const defaultGateway = document.querySelector('input[name="gateway"]:checked').value;
  updateCurrencyOptions(defaultGateway);

  // Add click handlers to gateway options
  document.querySelectorAll('input[name="gateway"]').forEach(radio => {
    radio.addEventListener('change', function() {
      updateCurrencyOptions(this.value);
    });
  });
});
</script>

<style>
.gateway-option:hover {
  border-color: #0077cc !important;
  background-color: #f8f9fa !important;
}

.gateway-option input[type="radio"]:checked + label {
  color: #0077cc;
}
</style>

<?php include "footer.php"; ?>
