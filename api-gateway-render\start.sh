#!/bin/bash

# ELOH Processing Payment Gateway - Start Script for Render

echo "🚀 Starting ELOH Processing Payment Gateway..."

# Set default port if not provided
PORT=${PORT:-8000}

echo "📊 Environment: $ENVIRONMENT"
echo "🌐 Port: $PORT"

# Initialize database if needed
echo "🗄️ Initializing database..."
python setup_database.py --sample-data

# Start the application
echo "🌐 Starting FastAPI server..."
exec uvicorn main:app --host 0.0.0.0 --port $PORT --log-level info
