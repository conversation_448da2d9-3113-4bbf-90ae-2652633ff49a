<?php
/**
 * ELOH Processing Streamlined Payment Widget
 * Processes REAL payments through your existing gateway infrastructure
 */

// Include your existing payment infrastructure
require_once __DIR__ . '/../includes/payment-gateway-manager.php';

// Initialize payment gateway manager
$gatewayManager = new Payment_Gateway_Manager();

// Get widget parameters
$amount = floatval($_GET['amount'] ?? 0);
$email = filter_var($_GET['email'] ?? '', FILTER_SANITIZE_EMAIL);
$description = htmlspecialchars($_GET['description'] ?? 'Payment');
$gateway = htmlspecialchars($_GET['gateway'] ?? 'btcpay');

// Handle payment processing
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // Get form data
        $amount = floatval($_POST['amount']);
        $email = filter_var($_POST['email'], FILTER_SANITIZE_EMAIL);
        $description = htmlspecialchars($_POST['description'] ?? 'Widget Payment');
        $gateway = htmlspecialchars($_POST['gateway']);
        
        // Validate inputs
        if ($amount < 5 || $amount > 10000) {
            throw new Exception('Amount must be between $5.00 and $10,000.00');
        }
        
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            throw new Exception('Valid email address is required');
        }
        
        // Generate order ID
        $orderId = 'WIDGET_' . time() . '_' . rand(1000, 9999);
        
        // Create payment using your existing infrastructure
        $result = $gatewayManager->createPayment(
            $gateway,
            $amount,
            'USD',
            $orderId,
            $description,
            $email
        );
        
        if ($result['success']) {
            // Store payment data
            $paymentData = [
                'order_id' => $orderId,
                'amount' => $amount,
                'email' => $email,
                'description' => $description,
                'gateway' => $gateway,
                'status' => 'pending',
                'created_at' => time(),
                'payment_data' => $result
            ];
            
            // Save to payments directory
            $paymentsDir = __DIR__ . '/payments';
            if (!is_dir($paymentsDir)) {
                mkdir($paymentsDir, 0755, true);
            }
            file_put_contents($paymentsDir . "/{$orderId}.json", json_encode($paymentData, JSON_PRETTY_PRINT));
            
            // Redirect to payment gateway
            if (isset($result['checkout_link'])) {
                header('Location: ' . $result['checkout_link']);
                exit;
            } elseif (isset($result['payment_url'])) {
                header('Location: ' . $result['payment_url']);
                exit;
            } else {
                $success_message = "Payment created successfully! Order ID: {$orderId}";
            }
        } else {
            throw new Exception($result['error'] ?? 'Payment creation failed');
        }
        
    } catch (Exception $e) {
        $error_message = $e->getMessage();
        error_log("Widget payment error: " . $error_message);
    }
}

// Get available gateways
$availableGateways = $gatewayManager->getAvailableGateways();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ELOH Processing - Payment</title>
    <style>
        :root {
            --primary: #667eea;
            --accent: #764ba2;
            --success: #10b981;
            --error: #ef4444;
            --warning: #f59e0b;
            --radius: 12px;
        }
        
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .widget-container {
            background: white;
            border-radius: var(--radius);
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
            padding: 40px;
            width: 100%;
            max-width: 450px;
            position: relative;
            overflow: hidden;
        }
        
        .widget-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary), var(--accent));
        }
        
        .widget-header {
            text-align: center;
            margin-bottom: 32px;
        }
        
        .widget-title {
            font-size: 2rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary), var(--accent));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 8px;
        }
        
        .live-indicator {
            display: inline-flex;
            align-items: center;
            gap: 6px;
            background: var(--error);
            color: white;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .live-dot {
            width: 8px;
            height: 8px;
            background: white;
            border-radius: 50%;
            animation: pulse 1.5s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        
        .widget-description {
            color: #6b7280;
            font-size: 1rem;
            margin-top: 12px;
        }
        
        .alert {
            padding: 16px;
            border-radius: var(--radius);
            margin-bottom: 24px;
            font-size: 0.95rem;
        }
        
        .alert-error {
            background: #fef2f2;
            color: var(--error);
            border: 1px solid #fecaca;
        }
        
        .alert-success {
            background: #f0fdf4;
            color: var(--success);
            border: 1px solid #bbf7d0;
        }
        
        .alert-warning {
            background: #fffbeb;
            color: #d97706;
            border: 1px solid #fed7aa;
        }
        
        .form-group {
            margin-bottom: 24px;
        }
        
        .form-label {
            display: block;
            font-weight: 600;
            color: #374151;
            margin-bottom: 8px;
            font-size: 0.95rem;
        }
        
        .form-input {
            width: 100%;
            padding: 16px;
            border: 2px solid #e5e7eb;
            border-radius: var(--radius);
            font-size: 1rem;
            transition: all 0.2s ease;
            background: #fafafa;
        }
        
        .form-input:focus {
            outline: none;
            border-color: var(--primary);
            background: white;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .gateway-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
            gap: 16px;
            margin-bottom: 24px;
        }
        
        .gateway-card {
            position: relative;
        }
        
        .gateway-card input[type="radio"] {
            position: absolute;
            opacity: 0;
        }
        
        .gateway-card label {
            display: block;
            padding: 20px 16px;
            border: 2px solid #e5e7eb;
            border-radius: var(--radius);
            text-align: center;
            cursor: pointer;
            transition: all 0.2s ease;
            background: #fafafa;
        }
        
        .gateway-card input[type="radio"]:checked + label {
            border-color: var(--primary);
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.2);
        }
        
        .gateway-icon {
            font-size: 2.5rem;
            margin-bottom: 8px;
        }
        
        .gateway-name {
            font-size: 0.9rem;
            font-weight: 600;
            color: #374151;
            margin-bottom: 4px;
        }
        
        .gateway-desc {
            font-size: 0.75rem;
            color: #6b7280;
        }
        
        .amount-group {
            position: relative;
        }
        
        .currency-symbol {
            position: absolute;
            left: 16px;
            top: 50%;
            transform: translateY(-50%);
            color: #6b7280;
            font-weight: 700;
            font-size: 1.2rem;
        }
        
        .amount-group .form-input {
            padding-left: 48px;
            font-size: 1.2rem;
            font-weight: 600;
        }
        
        .pay-button {
            width: 100%;
            background: linear-gradient(135deg, var(--primary) 0%, var(--accent) 100%);
            color: white;
            border: none;
            padding: 20px 24px;
            border-radius: var(--radius);
            font-size: 1.1rem;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.2s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
            position: relative;
            overflow: hidden;
        }
        
        .pay-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);
        }
        
        .pay-button:active {
            transform: translateY(0);
        }
        
        .security-footer {
            text-align: center;
            margin-top: 24px;
            padding-top: 24px;
            border-top: 1px solid #e5e7eb;
        }
        
        .security-badges {
            display: flex;
            justify-content: center;
            gap: 16px;
            margin-bottom: 12px;
        }
        
        .security-badge {
            font-size: 0.8rem;
            color: #6b7280;
            display: flex;
            align-items: center;
            gap: 4px;
        }
        
        .powered-by {
            font-size: 0.85rem;
            color: #9ca3af;
        }
        
        .powered-by a {
            color: var(--primary);
            text-decoration: none;
            font-weight: 600;
        }
        
        @media (max-width: 480px) {
            body {
                padding: 12px;
            }
            
            .widget-container {
                padding: 24px;
            }
            
            .gateway-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap" rel="stylesheet">
</head>
<body>
    <div class="widget-container">
        <div class="widget-header">
            <h1 class="widget-title">ELOH Processing</h1>
            <div class="live-indicator">
                <div class="live-dot"></div>
                Live Payment
            </div>
            <p class="widget-description">Secure cryptocurrency payment processing</p>
        </div>
        
        <div class="alert alert-warning">
            ⚠️ <strong>Live Payment System:</strong> This processes real cryptocurrency transactions
        </div>
        
        <?php if (isset($error_message)): ?>
        <div class="alert alert-error">
            ❌ <?php echo htmlspecialchars($error_message); ?>
        </div>
        <?php endif; ?>
        
        <?php if (isset($success_message)): ?>
        <div class="alert alert-success">
            ✅ <?php echo htmlspecialchars($success_message); ?>
        </div>
        <?php endif; ?>
        
        <form method="POST">
            <div class="form-group">
                <label class="form-label">Select Payment Gateway</label>
                <div class="gateway-grid">
                    <?php foreach ($availableGateways as $gatewayId => $gatewayInfo): ?>
                    <div class="gateway-card">
                        <input type="radio" name="gateway" value="<?php echo $gatewayId; ?>" 
                               id="gateway_<?php echo $gatewayId; ?>"
                               <?php echo $gateway === $gatewayId ? 'checked' : ''; ?>>
                        <label for="gateway_<?php echo $gatewayId; ?>">
                            <div class="gateway-icon"><?php echo $gatewayInfo['icon']; ?></div>
                            <div class="gateway-name"><?php echo $gatewayInfo['name']; ?></div>
                            <div class="gateway-desc"><?php echo substr($gatewayInfo['description'], 0, 30) . '...'; ?></div>
                        </label>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
            
            <div class="form-group">
                <label for="amount" class="form-label">Payment Amount</label>
                <div class="amount-group">
                    <span class="currency-symbol">$</span>
                    <input type="number" id="amount" name="amount" 
                           min="5" max="10000" step="0.01" required class="form-input"
                           value="<?php echo $amount > 0 ? number_format($amount, 2, '.', '') : ''; ?>"
                           placeholder="0.00">
                </div>
            </div>
            
            <div class="form-group">
                <label for="email" class="form-label">Email Address</label>
                <input type="email" id="email" name="email" required class="form-input"
                       value="<?php echo htmlspecialchars($email); ?>"
                       placeholder="<EMAIL>">
            </div>
            
            <div class="form-group">
                <label for="description" class="form-label">Payment Description</label>
                <input type="text" id="description" name="description" class="form-input"
                       value="<?php echo htmlspecialchars($description); ?>"
                       placeholder="What is this payment for?">
            </div>
            
            <button type="submit" class="pay-button">
                🚀 Process Payment
            </button>
        </form>
        
        <div class="security-footer">
            <div class="security-badges">
                <div class="security-badge">
                    🔒 SSL Encrypted
                </div>
                <div class="security-badge">
                    ⚡ Lightning Network
                </div>
                <div class="security-badge">
                    🌐 Global Support
                </div>
            </div>
            <div class="powered-by">
                Powered by <a href="https://elohprocessing.infy.uk" target="_blank">ELOH Processing</a>
            </div>
        </div>
    </div>
    
    <script>
        // Auto-resize for iframe embedding
        function sendResize() {
            if (window.parent && window.parent !== window) {
                const height = document.body.scrollHeight;
                window.parent.postMessage({
                    type: 'eloh_widget_resize',
                    height: height
                }, '*');
            }
        }
        
        // Send initial size
        setTimeout(sendResize, 100);
        
        // Send size on changes
        window.addEventListener('resize', sendResize);
        document.addEventListener('change', () => setTimeout(sendResize, 100));
        
        // Form validation
        document.querySelector('form').addEventListener('submit', function(e) {
            const amount = parseFloat(document.getElementById('amount').value);
            const email = document.getElementById('email').value;
            
            if (amount < 5) {
                alert('Minimum amount is $5.00');
                e.preventDefault();
                return;
            }
            
            if (amount > 10000) {
                alert('Maximum amount is $10,000.00');
                e.preventDefault();
                return;
            }
            
            if (!email.includes('@')) {
                alert('Please enter a valid email address');
                e.preventDefault();
                return;
            }
            
            // Show processing state
            const button = document.querySelector('.pay-button');
            button.innerHTML = '⏳ Processing...';
            button.disabled = true;
        });
        
        console.log('🔴 ELOH Processing Live Payment Widget');
        console.log('⚡ Connected to BTCPay Server');
        console.log('🌐 Connected to NowPayments');
        console.log('🔒 Ready for live transactions');
    </script>
</body>
</html>
