# ELOH Processing Payment Gateway - Minimal Dependencies for Testing/Deployment

# Core FastAPI stack
fastapi>=0.104.0,<0.105.0
uvicorn[standard]>=0.24.0,<0.25.0
pydantic>=2.5.0,<3.0.0
pydantic-settings>=2.1.0,<3.0.0

# HTTP clients
aiohttp>=3.9.0,<4.0.0
httpx>=0.25.0,<0.26.0

# Database
sqlalchemy>=2.0.20,<2.1.0
psycopg2-binary>=2.9.0,<3.0.0

# Security
cryptography>=41.0.0,<42.0.0
python-jose[cryptography]>=3.3.0,<4.0.0

# Utilities
python-dotenv>=1.0.0,<2.0.0

# Payment gateways (optional - install only if needed)
# stripe>=7.8.0,<8.0.0
# squareup>=30.0.0,<31.0.0
