<?php
/**
 * ELOH Processing Production Widget API
 * Stateless payment processing for production widgets
 */

// Disable session handling completely
ini_set('session.use_cookies', 0);
ini_set('session.use_only_cookies', 0);
ini_set('session.use_trans_sid', 0);

// Set headers for cross-platform compatibility
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'error' => 'Method not allowed']);
    exit;
}

// Utility functions
function sanitize_text_field($value) {
    return htmlspecialchars(strip_tags(trim($value ?? '')));
}

function sanitize_email($email) {
    return filter_var(trim($email ?? ''), FILTER_SANITIZE_EMAIL);
}

function load_widget_config($widget_id) {
    $config_file = __DIR__ . "/configs/{$widget_id}.json";
    
    if (file_exists($config_file)) {
        $config = json_decode(file_get_contents($config_file), true);
        if ($config) {
            return $config;
        }
    }
    
    // Return default config
    return [
        'enabled_gateways' => ['btcpay', 'nowpayments'],
        'min_amount' => 5.0,
        'max_amount' => 10000.0,
        'require_email' => true,
        'production_mode' => true
    ];
}

function store_payment_data($order_id, $data) {
    $payments_dir = __DIR__ . "/payments";
    if (!is_dir($payments_dir)) {
        mkdir($payments_dir, 0755, true);
    }
    
    $payment_file = $payments_dir . "/{$order_id}.json";
    return file_put_contents($payment_file, json_encode($data, JSON_PRETTY_PRINT));
}

function send_webhook($webhook_url, $data) {
    if (!$webhook_url) return;
    
    $context = stream_context_create([
        'http' => [
            'method' => 'POST',
            'header' => [
                'Content-Type: application/json',
                'User-Agent: ELOH-Widget/1.0'
            ],
            'content' => json_encode($data),
            'timeout' => 10
        ]
    ]);
    
    try {
        file_get_contents($webhook_url, false, $context);
        error_log("Webhook sent successfully to: " . $webhook_url);
    } catch (Exception $e) {
        error_log("Webhook failed: " . $e->getMessage());
    }
}

try {
    // Get and validate widget ID
    $widget_id = sanitize_text_field($_POST['widget_id'] ?? '');
    if (empty($widget_id)) {
        throw new Exception('Widget ID is required');
    }
    
    // Load widget configuration
    $config = load_widget_config($widget_id);
    
    // Get action
    $action = sanitize_text_field($_POST['action'] ?? '');
    
    switch ($action) {
        case 'create_payment':
            handleCreatePayment($config, $widget_id);
            break;
            
        case 'get_status':
            handleGetStatus($config);
            break;
            
        default:
            throw new Exception('Invalid action');
    }
    
} catch (Exception $e) {
    error_log("Production Widget API Error: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}

function handleCreatePayment($config, $widget_id) {
    // Validate and sanitize input
    $gateway = sanitize_text_field($_POST['gateway'] ?? '');
    $amount = floatval($_POST['amount'] ?? 0);
    $email = sanitize_email($_POST['email'] ?? '');
    $description = sanitize_text_field($_POST['description'] ?? '');
    
    // Validation
    $errors = [];
    
    // Validate gateway
    if (!in_array($gateway, $config['enabled_gateways'])) {
        $errors[] = 'Invalid payment gateway';
    }
    
    // Validate amount
    if ($amount < $config['min_amount']) {
        $errors[] = "Minimum amount is $" . number_format($config['min_amount'], 2);
    }
    if ($amount > $config['max_amount']) {
        $errors[] = "Maximum amount is $" . number_format($config['max_amount'], 2);
    }
    
    // Validate email if required
    if ($config['require_email'] && empty($email)) {
        $errors[] = 'Email address is required';
    }
    if (!empty($email) && !filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = 'Invalid email address';
    }
    
    if (!empty($errors)) {
        echo json_encode([
            'success' => false,
            'error' => implode(', ', $errors)
        ]);
        return;
    }
    
    // Generate order ID
    $order_id = 'WIDGET_' . strtoupper($widget_id) . '_' . time() . '_' . rand(1000, 9999);
    
    // Prepare payment description
    $payment_description = $description ?: 'Widget Payment';
    if (!empty($config['company_name'])) {
        $payment_description = $config['company_name'] . ' - ' . $payment_description;
    }
    
    try {
        // Create payment based on gateway
        $payment_result = createGatewayPayment($gateway, $amount, $order_id, $payment_description, $email);
        
        if ($payment_result['success']) {
            // Store payment data
            $payment_data = [
                'widget_id' => $widget_id,
                'order_id' => $order_id,
                'gateway' => $gateway,
                'amount' => $amount,
                'email' => $email,
                'description' => $payment_description,
                'created_at' => time(),
                'status' => 'pending',
                'payment_data' => $payment_result
            ];
            
            store_payment_data($order_id, $payment_data);
            
            // Send webhook notification if configured
            if (!empty($config['webhook_url'])) {
                send_webhook($config['webhook_url'], [
                    'event' => 'payment_created',
                    'order_id' => $order_id,
                    'amount' => $amount,
                    'gateway' => $gateway,
                    'email' => $email,
                    'description' => $payment_description,
                    'payment_data' => $payment_result
                ]);
            }
            
            echo json_encode([
                'success' => true,
                'order_id' => $order_id,
                'redirect_url' => $payment_result['redirect_url'] ?? '',
                'payment_data' => $payment_result,
                'message' => 'Payment created successfully'
            ]);
            
        } else {
            throw new Exception($payment_result['error'] ?? 'Failed to create payment');
        }
        
    } catch (Exception $e) {
        error_log("Payment creation error: " . $e->getMessage());
        echo json_encode([
            'success' => false,
            'error' => 'Payment processing failed: ' . $e->getMessage()
        ]);
    }
}

function createGatewayPayment($gateway, $amount, $order_id, $description, $email) {
    switch ($gateway) {
        case 'btcpay':
            return createBTCPayPayment($amount, $order_id, $description, $email);
            
        case 'nowpayments':
            return createNowPaymentsPayment($amount, $order_id, $description, $email);
            
        case 'square':
            return createSquarePayment($amount, $order_id, $description, $email);
            
        default:
            return [
                'success' => false,
                'error' => 'Unsupported payment gateway'
            ];
    }
}

function createBTCPayPayment($amount, $order_id, $description, $email) {
    // Load BTCPay configuration
    $btcpay_config_file = __DIR__ . '/../config/btcpay-config.json';
    if (!file_exists($btcpay_config_file)) {
        return [
            'success' => false,
            'error' => 'BTCPay Server not configured'
        ];
    }
    
    $btcpay_config = json_decode(file_get_contents($btcpay_config_file), true);
    
    // Create BTCPay invoice
    $invoice_data = [
        'amount' => $amount,
        'currency' => 'USD',
        'orderId' => $order_id,
        'notificationEmail' => $email,
        'notificationURL' => 'https://elohprocessing.infy.uk/webhook/btcpay.php',
        'redirectURL' => 'https://elohprocessing.infy.uk/payment-success.php?order=' . $order_id,
        'metadata' => [
            'orderId' => $order_id,
            'itemDesc' => $description
        ]
    ];
    
    $context = stream_context_create([
        'http' => [
            'method' => 'POST',
            'header' => [
                'Content-Type: application/json',
                'Authorization: token ' . $btcpay_config['api_key']
            ],
            'content' => json_encode($invoice_data)
        ]
    ]);
    
    try {
        $response = file_get_contents($btcpay_config['server_url'] . '/api/v1/invoices', false, $context);
        $invoice = json_decode($response, true);
        
        if ($invoice && isset($invoice['id'])) {
            return [
                'success' => true,
                'invoice_id' => $invoice['id'],
                'redirect_url' => $invoice['checkoutLink'],
                'payment_url' => $invoice['checkoutLink']
            ];
        } else {
            return [
                'success' => false,
                'error' => 'Failed to create BTCPay invoice'
            ];
        }
    } catch (Exception $e) {
        return [
            'success' => false,
            'error' => 'BTCPay Server error: ' . $e->getMessage()
        ];
    }
}

function createNowPaymentsPayment($amount, $order_id, $description, $email) {
    // Load NowPayments configuration
    $nowpayments_config_file = __DIR__ . '/../config/nowpayments-config.json';
    if (!file_exists($nowpayments_config_file)) {
        return [
            'success' => false,
            'error' => 'NowPayments not configured'
        ];
    }
    
    $nowpayments_config = json_decode(file_get_contents($nowpayments_config_file), true);
    
    // Create NowPayments payment
    $payment_data = [
        'price_amount' => $amount,
        'price_currency' => 'usd',
        'pay_currency' => 'btc',
        'order_id' => $order_id,
        'order_description' => $description,
        'ipn_callback_url' => 'https://elohprocessing.infy.uk/webhook/nowpayments.php',
        'success_url' => 'https://elohprocessing.infy.uk/payment-success.php?order=' . $order_id,
        'cancel_url' => 'https://elohprocessing.infy.uk/payment-cancel.php?order=' . $order_id
    ];
    
    $context = stream_context_create([
        'http' => [
            'method' => 'POST',
            'header' => [
                'Content-Type: application/json',
                'x-api-key: ' . $nowpayments_config['api_key']
            ],
            'content' => json_encode($payment_data)
        ]
    ]);
    
    try {
        $response = file_get_contents('https://api.nowpayments.io/v1/payment', false, $context);
        $payment = json_decode($response, true);
        
        if ($payment && isset($payment['payment_id'])) {
            return [
                'success' => true,
                'payment_id' => $payment['payment_id'],
                'redirect_url' => 'https://elohprocessing.infy.uk/nowpayments-checkout.php?payment_id=' . $payment['payment_id'],
                'payment_url' => $payment['invoice_url'] ?? ''
            ];
        } else {
            return [
                'success' => false,
                'error' => 'Failed to create NowPayments payment'
            ];
        }
    } catch (Exception $e) {
        return [
            'success' => false,
            'error' => 'NowPayments error: ' . $e->getMessage()
        ];
    }
}

function createSquarePayment($amount, $order_id, $description, $email) {
    // Square payments require different handling (card processing)
    return [
        'success' => false,
        'error' => 'Square payments require card processing interface'
    ];
}

function handleGetStatus($config) {
    $order_id = sanitize_text_field($_POST['order_id'] ?? '');
    
    if (empty($order_id)) {
        echo json_encode([
            'success' => false,
            'error' => 'Order ID is required'
        ]);
        return;
    }
    
    // Load payment data
    $payment_file = __DIR__ . "/payments/{$order_id}.json";
    
    if (file_exists($payment_file)) {
        $payment_data = json_decode(file_get_contents($payment_file), true);
        
        echo json_encode([
            'success' => true,
            'order_id' => $order_id,
            'status' => $payment_data['status'] ?? 'unknown',
            'payment_data' => $payment_data
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'error' => 'Payment not found'
        ]);
    }
}
?>
