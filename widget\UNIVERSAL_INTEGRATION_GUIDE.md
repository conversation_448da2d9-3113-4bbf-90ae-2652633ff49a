# 🌍 ELOH Processing Universal Payment Widget

## 🚀 **Framework-Agnostic Integration**

Pure JavaScript/HTML widget that works with **any framework** - Node.js, React, Flutter, Vue, Angular, and more!

## ✨ Key Features

- **🌐 Universal** - Works with any JavaScript framework
- **📱 Cross-Platform** - Web, mobile, desktop applications
- **🔴 Live Payments** - Real cryptocurrency transactions
- **⚡ Easy Integration** - Single script include
- **🎨 Responsive** - Auto-resizing and mobile-friendly
- **🔒 Secure** - No server dependencies required

## 🚀 Quick Start

### 1. Direct HTML Integration

```html
<!-- Include the SDK -->
<script src="https://elohprocessing.infy.uk/widget/eloh-payment-sdk.js"></script>

<!-- Auto-initialize with data attributes -->
<div data-eloh-widget 
     data-amount="100.00" 
     data-email="<EMAIL>"
     data-description="Product Purchase">
</div>
```

### 2. JavaScript API

```javascript
// Include SDK
import { ELOHPaymentSDK } from 'https://elohprocessing.infy.uk/widget/eloh-payment-sdk.js';

// Initialize SDK
const eloh = new ELOHPaymentSDK({
  debug: true
});

// Create widget
const widget = eloh.createWidget({
  container: '#payment-widget',
  amount: 100.00,
  email: '<EMAIL>',
  onSuccess: (widget, data) => {
    console.log('Payment successful!', data);
  }
});
```

## 🔧 Framework Integrations

### React Integration

```jsx
import React, { useEffect, useRef } from 'react';

const PaymentWidget = ({ amount, email, onSuccess }) => {
  const containerRef = useRef(null);
  const widgetRef = useRef(null);
  
  useEffect(() => {
    // Load SDK dynamically
    const script = document.createElement('script');
    script.src = 'https://elohprocessing.infy.uk/widget/eloh-payment-sdk.js';
    script.onload = () => {
      const eloh = new window.ELOHPaymentSDK();
      
      widgetRef.current = eloh.createWidget({
        container: containerRef.current,
        amount: amount,
        email: email,
        onSuccess: (widget, data) => {
          onSuccess(data);
        },
        onError: (widget, error) => {
          console.error('Payment error:', error);
        }
      });
    };
    
    document.head.appendChild(script);
    
    return () => {
      if (widgetRef.current) {
        widgetRef.current.destroy();
      }
    };
  }, [amount, email, onSuccess]);
  
  return <div ref={containerRef} style={{ width: '420px', height: '600px' }} />;
};

// Usage
<PaymentWidget 
  amount={100.00} 
  email="<EMAIL>"
  onSuccess={(data) => console.log('Payment completed:', data)}
/>
```

### Vue.js Integration

```vue
<template>
  <div ref="paymentContainer" class="payment-widget"></div>
</template>

<script>
export default {
  name: 'PaymentWidget',
  props: {
    amount: Number,
    email: String
  },
  mounted() {
    this.loadWidget();
  },
  beforeUnmount() {
    if (this.widget) {
      this.widget.destroy();
    }
  },
  methods: {
    async loadWidget() {
      // Load SDK
      await this.loadScript('https://elohprocessing.infy.uk/widget/eloh-payment-sdk.js');
      
      const eloh = new window.ELOHPaymentSDK();
      
      this.widget = eloh.createWidget({
        container: this.$refs.paymentContainer,
        amount: this.amount,
        email: this.email,
        onSuccess: (widget, data) => {
          this.$emit('payment-success', data);
        }
      });
    },
    loadScript(src) {
      return new Promise((resolve) => {
        const script = document.createElement('script');
        script.src = src;
        script.onload = resolve;
        document.head.appendChild(script);
      });
    }
  }
};
</script>
```

### Node.js/Express Integration

```javascript
const express = require('express');
const { ELOHPaymentSDK } = require('./eloh-payment-sdk.js');

const app = express();
const eloh = new ELOHPaymentSDK();

// Create payment endpoint
app.post('/create-payment', async (req, res) => {
  try {
    const { amount, email, gateway, description } = req.body;
    
    const payment = await eloh.createPayment({
      amount: amount,
      email: email,
      gateway: gateway,
      description: description
    });
    
    res.json(payment);
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
});

// Serve widget page
app.get('/payment', (req, res) => {
  res.send(`
    <!DOCTYPE html>
    <html>
    <head>
      <title>Payment</title>
      <script src="https://elohprocessing.infy.uk/widget/eloh-payment-sdk.js"></script>
    </head>
    <body>
      <div id="payment-widget"></div>
      <script>
        const eloh = new ELOHPaymentSDK();
        eloh.createWidget({
          container: '#payment-widget',
          amount: ${req.query.amount || 100},
          email: '${req.query.email || '<EMAIL>'}'
        });
      </script>
    </body>
    </html>
  `);
});
```

### Flutter Integration

```dart
import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';

class PaymentWidget extends StatefulWidget {
  final double amount;
  final String email;
  final Function(Map<String, dynamic>) onSuccess;
  
  PaymentWidget({required this.amount, required this.email, required this.onSuccess});
  
  @override
  _PaymentWidgetState createState() => _PaymentWidgetState();
}

class _PaymentWidgetState extends State<PaymentWidget> {
  late WebViewController controller;
  
  @override
  Widget build(BuildContext context) {
    return Container(
      height: 600,
      child: WebView(
        initialUrl: 'https://elohprocessing.infy.uk/widget/universal-payment-widget.html'
            '?amount=${widget.amount}&email=${Uri.encodeComponent(widget.email)}',
        javascriptMode: JavascriptMode.unrestricted,
        onWebViewCreated: (WebViewController webViewController) {
          controller = webViewController;
        },
        javascriptChannels: {
          JavascriptChannel(
            name: 'PaymentHandler',
            onMessageReceived: (JavascriptMessage message) {
              // Handle payment success/error messages
              final data = jsonDecode(message.message);
              if (data['type'] == 'eloh_payment_success') {
                widget.onSuccess(data['data']);
              }
            },
          ),
        },
      ),
    );
  }
}

// Usage
PaymentWidget(
  amount: 100.00,
  email: '<EMAIL>',
  onSuccess: (data) {
    print('Payment successful: $data');
  },
)
```

### Angular Integration

```typescript
import { Component, ElementRef, Input, OnInit, OnDestroy, ViewChild } from '@angular/core';

declare global {
  interface Window {
    ELOHPaymentSDK: any;
  }
}

@Component({
  selector: 'app-payment-widget',
  template: '<div #paymentContainer class="payment-widget"></div>'
})
export class PaymentWidgetComponent implements OnInit, OnDestroy {
  @ViewChild('paymentContainer', { static: true }) paymentContainer!: ElementRef;
  @Input() amount: number = 0;
  @Input() email: string = '';
  
  private widget: any;
  
  async ngOnInit() {
    await this.loadSDK();
    this.initializeWidget();
  }
  
  ngOnDestroy() {
    if (this.widget) {
      this.widget.destroy();
    }
  }
  
  private loadSDK(): Promise<void> {
    return new Promise((resolve) => {
      if (window.ELOHPaymentSDK) {
        resolve();
        return;
      }
      
      const script = document.createElement('script');
      script.src = 'https://elohprocessing.infy.uk/widget/eloh-payment-sdk.js';
      script.onload = () => resolve();
      document.head.appendChild(script);
    });
  }
  
  private initializeWidget() {
    const eloh = new window.ELOHPaymentSDK();
    
    this.widget = eloh.createWidget({
      container: this.paymentContainer.nativeElement,
      amount: this.amount,
      email: this.email,
      onSuccess: (widget: any, data: any) => {
        console.log('Payment successful:', data);
      }
    });
  }
}
```

## 📱 Mobile App Integration

### React Native

```javascript
import React from 'react';
import { WebView } from 'react-native-webview';

const PaymentWidget = ({ amount, email, onPaymentSuccess }) => {
  const handleMessage = (event) => {
    const data = JSON.parse(event.nativeEvent.data);
    
    if (data.type === 'eloh_payment_success') {
      onPaymentSuccess(data.data);
    }
  };
  
  return (
    <WebView
      source={{ 
        uri: `https://elohprocessing.infy.uk/widget/universal-payment-widget.html?amount=${amount}&email=${encodeURIComponent(email)}` 
      }}
      style={{ width: 420, height: 600 }}
      onMessage={handleMessage}
      javaScriptEnabled={true}
      domStorageEnabled={true}
    />
  );
};
```

### Ionic/Cordova

```javascript
// In your component
loadPaymentWidget() {
  const iframe = document.createElement('iframe');
  iframe.src = `https://elohprocessing.infy.uk/widget/universal-payment-widget.html?amount=100.00`;
  iframe.style.width = '100%';
  iframe.style.height = '600px';
  iframe.style.border = 'none';
  
  document.getElementById('payment-container').appendChild(iframe);
  
  // Listen for payment events
  window.addEventListener('message', (event) => {
    if (event.data.type === 'eloh_payment_success') {
      this.handlePaymentSuccess(event.data.data);
    }
  });
}
```

## 🔧 Configuration Options

### SDK Configuration

```javascript
const eloh = new ELOHPaymentSDK({
  baseUrl: 'https://elohprocessing.infy.uk',
  environment: 'production', // or 'sandbox'
  debug: true,
  timeout: 30000
});
```

### Widget Configuration

```javascript
const widget = eloh.createWidget({
  container: '#payment-widget',
  amount: 100.00,
  email: '<EMAIL>',
  description: 'Product Purchase',
  gateway: 'btcpay', // or 'nowpayments'
  theme: 'light', // or 'dark'
  width: '420px',
  height: 'auto',
  autoResize: true,
  
  // Event handlers
  onLoad: (widget) => console.log('Widget loaded'),
  onSuccess: (widget, data) => console.log('Payment successful'),
  onError: (widget, error) => console.log('Payment error'),
  onPaymentCreated: (widget, data) => console.log('Payment created')
});
```

## 🔔 Event Handling

```javascript
// Listen for widget events
window.addEventListener('message', (event) => {
  switch (event.data.type) {
    case 'eloh_payment_success':
      // Payment completed successfully
      handlePaymentSuccess(event.data.data);
      break;
      
    case 'eloh_payment_error':
      // Payment failed
      handlePaymentError(event.data.data);
      break;
      
    case 'eloh_payment_created':
      // Payment created, user redirected to gateway
      handlePaymentCreated(event.data.data);
      break;
      
    case 'eloh_widget_resize':
      // Widget height changed (for auto-resize)
      handleWidgetResize(event.data.height);
      break;
  }
});
```

## 🌍 Platform Compatibility

- ✅ **Web Browsers** - Chrome, Firefox, Safari, Edge
- ✅ **React** - Create React App, Next.js, Gatsby
- ✅ **Vue.js** - Vue 2/3, Nuxt.js
- ✅ **Angular** - Angular 10+
- ✅ **Node.js** - Express, Fastify, Koa
- ✅ **Mobile** - React Native, Flutter, Ionic
- ✅ **Desktop** - Electron, Tauri, WebView
- ✅ **Static Sites** - GitHub Pages, Netlify, Vercel

## 🚀 Ready for Any Framework!

The universal widget provides a consistent payment experience across all platforms while maintaining the flexibility to integrate with any technology stack.

**Perfect for modern development workflows!** 🌍💰
