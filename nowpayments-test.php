<?php
// NowPayments Live Configuration Test
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once "includes/nowpayments-gateway.php";

echo "<h1>NowPayments Live Configuration Test</h1>";

try {
    $gateway = new NowPayments_Gateway(false); // Live mode
    $debug = $gateway->getDebugInfo();

    echo "<h2>Configuration Status:</h2>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    foreach ($debug as $key => $value) {
        $status = is_bool($value) ? ($value ? '✅ Yes' : '❌ No') : htmlspecialchars($value);
        $color = ($key === 'api_key_set' && !$value) ? 'color: red;' : '';
        echo "<tr>";
        echo "<td style='padding: 8px; font-weight: bold;'>" . ucfirst(str_replace('_', ' ', $key)) . ":</td>";
        echo "<td style='padding: 8px; $color'>$status</td>";
        echo "</tr>";
    }
    echo "</table>";

    // Check if API key is configured
    if (!$debug['api_key_set']) {
        echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h3>⚠️ API Key Required</h3>";
        echo "<p>You need to configure your NowPayments API key:</p>";
        echo "<ol>";
        echo "<li>Sign up at: <a href='https://account.nowpayments.io' target='_blank'>https://account.nowpayments.io</a></li>";
        echo "<li>Generate API key in dashboard</li>";
        echo "<li>Edit <code>includes/nowpayments-config.php</code></li>";
        echo "<li>Replace <code>your_live_api_key_here</code> with your actual API key</li>";
        echo "</ol>";
        echo "</div>";
    } else {
        echo "<div style='background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h3>✅ API Key Configured</h3>";
        echo "<p>Your NowPayments API key is configured. You can test payments!</p>";
        echo "</div>";

        // Test API connection
        echo "<h2>API Connection Test:</h2>";

        if (isset($_POST['test_connection'])) {
            echo "<p>Testing connection to NowPayments API...</p>";

            $currencies = $gateway->getAvailableCurrencies();

            if ($currencies['success']) {
                echo "<div style='background: #d4edda; color: #155724; padding: 15px; border-radius: 5px;'>";
                echo "<h3>✅ API Connection Successful!</h3>";
                echo "<p>Successfully connected to NowPayments API.</p>";
                echo "<p><strong>Available currencies:</strong> " . count($currencies['currencies']) . " cryptocurrencies</p>";
                echo "<p><strong>Sample currencies:</strong> " . implode(', ', array_slice($currencies['currencies'], 0, 10)) . "...</p>";
                echo "</div>";
            } else {
                echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px;'>";
                echo "<h3>❌ API Connection Failed</h3>";
                echo "<p><strong>Error:</strong> " . htmlspecialchars($currencies['error']) . "</p>";
                if (isset($currencies['details'])) {
                    echo "<p><strong>Details:</strong> " . htmlspecialchars(json_encode($currencies['details'])) . "</p>";
                }
                echo "</div>";
            }
        } else {
            echo "<form method='POST'>";
            echo "<button type='submit' name='test_connection' style='background: #0077cc; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;'>Test API Connection</button>";
            echo "</form>";
        }
    }

    echo "<h2>Supported Cryptocurrencies:</h2>";
    $popular = $gateway->getPopularCurrencies();
    echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px; margin: 20px 0;'>";
    foreach ($popular as $symbol => $name) {
        echo "<div style='background: #f8f9fa; padding: 10px; border-radius: 5px; text-align: center;'>";
        echo "<strong>$symbol</strong><br>$name";
        echo "</div>";
    }
    echo "</div>";

    echo "<h2>Test Payment Creation:</h2>";

    if (isset($_POST['test_payment']) && $debug['api_key_set']) {
        echo "<p>Creating test payment...</p>";

        // First check minimum amount for BTC
        echo "<p>Checking minimum amount for Bitcoin...</p>";
        $minAmount = $gateway->getMinimumAmount('btc');

        if ($minAmount['success']) {
            $minUsd = $minAmount['min_amount'];
            echo "<p><strong>Minimum amount:</strong> $" . number_format($minUsd, 2) . " USD</p>";

            // Use a higher test amount to ensure it's above minimum
            $testAmount = max(10.00, $minUsd * 1.5); // At least $10 or 1.5x minimum
            echo "<p><strong>Test amount:</strong> $" . number_format($testAmount, 2) . " USD</p>";
        } else {
            $testAmount = 10.00; // Default to $10 if we can't get minimum
            echo "<p><strong>Using default test amount:</strong> $" . number_format($testAmount, 2) . " USD</p>";
        }

        $testPayment = $gateway->createPayment(
            $testAmount,
            'btc', // Bitcoin
            'TEST_' . time(),
            'Test payment for ELOH Processing',
            '<EMAIL>'
        );

        if ($testPayment['success']) {
            echo "<div style='background: #d4edda; color: #155724; padding: 15px; border-radius: 5px;'>";
            echo "<h3>✅ Test Payment Created Successfully!</h3>";
            echo "<p><strong>Payment ID:</strong> " . htmlspecialchars($testPayment['payment_id']) . "</p>";
            echo "<p><strong>Amount:</strong> " . $testPayment['pay_amount'] . " " . strtoupper($testPayment['pay_currency']) . "</p>";
            echo "<p><strong>Status:</strong> " . ucfirst($testPayment['payment_status']) . "</p>";
            echo "<p><strong>Address:</strong> " . htmlspecialchars($testPayment['pay_address']) . "</p>";
            echo "<p><em>This is a test payment. You can use it to verify the integration works.</em></p>";
            echo "</div>";
        } else {
            echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px;'>";
            echo "<h3>❌ Test Payment Failed</h3>";
            echo "<p><strong>Error:</strong> " . htmlspecialchars($testPayment['error']) . "</p>";
            if (isset($testPayment['details'])) {
                echo "<p><strong>Details:</strong> " . htmlspecialchars(json_encode($testPayment['details'])) . "</p>";
            }
            echo "</div>";
        }
    } else if ($debug['api_key_set']) {
        echo "<form method='POST'>";
        echo "<button type='submit' name='test_payment' style='background: #28a745; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;'>Create Test Payment ($10+)</button>";
        echo "</form>";
        echo "<p><small>This will create a test payment (minimum $10) to verify your integration works.</small></p>";
    }

} catch (Exception $e) {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px;'>";
    echo "<h3>❌ Error</h3>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

echo "<hr>";
echo "<h2>Setup Instructions:</h2>";
echo "<ol>";
echo "<li><strong>Sign up</strong> for NowPayments: <a href='https://account.nowpayments.io' target='_blank'>https://account.nowpayments.io</a></li>";
echo "<li><strong>Add wallet addresses</strong> in the dashboard for cryptocurrencies you want to accept</li>";
echo "<li><strong>Generate API key</strong> in Settings → API Keys</li>";
echo "<li><strong>Generate IPN secret</strong> for webhook validation</li>";
echo "<li><strong>Edit</strong> <code>includes/nowpayments-config.php</code> with your credentials</li>";
echo "<li><strong>Test</strong> using this page</li>";
echo "<li><strong>Go live</strong> with <code>multi-gateway-payment-form.php</code></li>";
echo "</ol>";

echo "<hr>";
echo "<p><a href='multi-gateway-payment-form.php'>← Test Multi-Gateway Payment Form</a> | <a href='index.php'>Homepage</a></p>";
?>
