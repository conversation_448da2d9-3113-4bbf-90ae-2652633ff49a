"""
Rule-Based Payment Gateway Routing for ELOH Processing

This module implements the rule-based routing logic that determines which
payment gateway to use for a given transaction. It serves as the primary
routing mechanism and will act as a fallback when AI-driven routing is implemented.

The routing system considers multiple factors:
- User preferences and configuration
- Payment amount and currency
- Gateway availability and reliability
- Business rules and constraints

Future AI Integration:
The rule-based system is designed to be AI-friendly and will serve as a
reliable fallback mechanism when AI routing is uncertain or unavailable.
"""

from typing import Dict, Any, List, Optional, Tuple
from decimal import Decimal
import logging
from dataclasses import dataclass
from enum import Enum

from ..models.payment import PaymentRequest, Currency, PaymentMethod
from .adapter_registry import get_adapter_registry
from .exceptions import PaymentGatewayException

logger = logging.getLogger(__name__)


class RoutingStrategy(str, Enum):
    """Available routing strategies"""
    RULE_BASED = "rule_based"
    AI_DRIVEN = "ai_driven"  # For future implementation
    HYBRID = "hybrid"  # AI with rule-based fallback


@dataclass
class UserGatewayConfig:
    """User-specific gateway configuration"""
    user_id: str
    enabled_gateways: List[str]
    preferred_gateway: Optional[str] = None
    gateway_priority: Optional[List[str]] = None
    currency_preferences: Optional[Dict[str, str]] = None  # currency -> preferred_gateway
    amount_thresholds: Optional[Dict[str, Dict]] = None  # gateway -> {min_amount, max_amount}
    disabled_gateways: Optional[List[str]] = None
    routing_strategy: RoutingStrategy = RoutingStrategy.RULE_BASED


@dataclass
class RoutingContext:
    """Context information for routing decisions"""
    payment_request: PaymentRequest
    user_config: UserGatewayConfig
    available_gateways: List[str]
    gateway_health: Dict[str, float]  # gateway -> health score (0-1)
    current_load: Dict[str, float]  # gateway -> current load (0-1)


class PaymentRouter:
    """
    Rule-based payment gateway router.
    
    This class implements intelligent routing logic that selects the most
    appropriate payment gateway based on various factors and business rules.
    
    The router is designed to be:
    - Deterministic and predictable
    - Configurable per user/merchant
    - Extensible for new routing rules
    - AI-ready for future integration
    """
    
    def __init__(self):
        self.registry = get_adapter_registry()
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        
        # Default routing rules configuration
        self.default_rules = {
            "min_amount_thresholds": {
                "stripe": Decimal("0.50"),  # Stripe minimum
                "btcpay": Decimal("0.01"),  # Bitcoin minimum
                "square": Decimal("1.00"),  # Square minimum
                "nowpayments": Decimal("1.00")  # NowPayments minimum
            },
            "max_amount_thresholds": {
                "stripe": Decimal("999999"),  # Stripe maximum
                "btcpay": Decimal("1000000"),  # Bitcoin has no practical limit
                "square": Decimal("50000"),  # Square daily limit
                "nowpayments": Decimal("100000")  # NowPayments limit
            },
            "currency_preferences": {
                "BTC": ["btcpay", "nowpayments"],
                "ETH": ["nowpayments"],
                "USD": ["stripe", "square", "btcpay"],
                "EUR": ["stripe", "btcpay"],
                "GBP": ["stripe"]
            },
            "payment_method_preferences": {
                "card": ["stripe", "square"],
                "bitcoin": ["btcpay", "nowpayments"],
                "lightning": ["btcpay"],
                "cryptocurrency": ["nowpayments", "btcpay"],
                "bank_transfer": ["stripe"]
            }
        }
    
    async def route_payment(self, payment_request: PaymentRequest, user_config: UserGatewayConfig) -> str:
        """
        Route a payment request to the most appropriate gateway.
        
        Args:
            payment_request: The payment request to route
            user_config: User-specific gateway configuration
            
        Returns:
            str: Selected gateway identifier
            
        Raises:
            PaymentGatewayException: If no suitable gateway is found
        """
        self.logger.info(f"Routing payment: {payment_request.amount} {payment_request.currency}")
        
        # Build routing context
        context = await self._build_routing_context(payment_request, user_config)
        
        # Apply routing strategy
        if user_config.routing_strategy == RoutingStrategy.AI_DRIVEN:
            # Future AI routing implementation
            gateway = await self._ai_route_payment(context)
            if gateway:
                self.logger.info(f"AI routing selected: {gateway}")
                return gateway
            else:
                self.logger.warning("AI routing failed, falling back to rule-based")
        
        # Rule-based routing (primary method and AI fallback)
        gateway = await self._rule_based_route_payment(context)
        
        self.logger.info(f"Rule-based routing selected: {gateway}")
        return gateway
    
    async def _build_routing_context(self, payment_request: PaymentRequest, user_config: UserGatewayConfig) -> RoutingContext:
        """Build routing context with all necessary information"""
        
        # Get available gateways
        available_gateways = self.registry.list_gateways()
        
        # Filter by user-enabled gateways
        if user_config.enabled_gateways:
            available_gateways = [g for g in available_gateways if g in user_config.enabled_gateways]
        
        # Remove disabled gateways
        if user_config.disabled_gateways:
            available_gateways = [g for g in available_gateways if g not in user_config.disabled_gateways]
        
        # Mock gateway health and load (in production, this would come from monitoring)
        gateway_health = {gateway: 0.95 for gateway in available_gateways}  # High health by default
        current_load = {gateway: 0.3 for gateway in available_gateways}  # Low load by default
        
        return RoutingContext(
            payment_request=payment_request,
            user_config=user_config,
            available_gateways=available_gateways,
            gateway_health=gateway_health,
            current_load=current_load
        )
    
    async def _rule_based_route_payment(self, context: RoutingContext) -> str:
        """
        Apply rule-based routing logic.
        
        This method implements a comprehensive set of routing rules that
        consider user preferences, payment characteristics, and gateway capabilities.
        """
        payment_request = context.payment_request
        user_config = context.user_config
        available_gateways = context.available_gateways
        
        if not available_gateways:
            raise PaymentGatewayException("No available payment gateways")
        
        # Step 1: Check user's preferred gateway first
        if user_config.preferred_gateway and user_config.preferred_gateway in available_gateways:
            if await self._is_gateway_suitable(user_config.preferred_gateway, payment_request, user_config):
                return user_config.preferred_gateway
        
        # Step 2: Apply user's gateway priority list
        if user_config.gateway_priority:
            for gateway in user_config.gateway_priority:
                if gateway in available_gateways:
                    if await self._is_gateway_suitable(gateway, payment_request, user_config):
                        return gateway
        
        # Step 3: Apply currency-based routing
        currency_gateways = self._get_gateways_for_currency(payment_request.currency, available_gateways)
        if currency_gateways:
            # Check user's currency preferences first
            if (user_config.currency_preferences and 
                payment_request.currency in user_config.currency_preferences):
                preferred = user_config.currency_preferences[payment_request.currency]
                if preferred in currency_gateways:
                    if await self._is_gateway_suitable(preferred, payment_request, user_config):
                        return preferred
            
            # Use default currency preferences
            default_prefs = self.default_rules["currency_preferences"].get(payment_request.currency, [])
            for gateway in default_prefs:
                if gateway in currency_gateways:
                    if await self._is_gateway_suitable(gateway, payment_request, user_config):
                        return gateway
        
        # Step 4: Apply payment method-based routing
        if payment_request.payment_method:
            method_gateways = self._get_gateways_for_payment_method(
                payment_request.payment_method, available_gateways
            )
            if method_gateways:
                # Use first suitable gateway for this payment method
                for gateway in method_gateways:
                    if await self._is_gateway_suitable(gateway, payment_request, user_config):
                        return gateway
        
        # Step 5: Apply amount-based routing
        amount_suitable_gateways = []
        for gateway in available_gateways:
            if await self._is_amount_suitable(gateway, payment_request.amount, user_config):
                amount_suitable_gateways.append(gateway)
        
        if amount_suitable_gateways:
            # Choose gateway with best health and lowest load
            best_gateway = self._select_best_gateway_by_performance(
                amount_suitable_gateways, context.gateway_health, context.current_load
            )
            if best_gateway:
                return best_gateway
        
        # Step 6: Fallback to any available gateway
        if available_gateways:
            self.logger.warning("Using fallback gateway selection")
            return self._select_best_gateway_by_performance(
                available_gateways, context.gateway_health, context.current_load
            )
        
        # No suitable gateway found
        raise PaymentGatewayException("No suitable payment gateway found for this request")
    
    async def _ai_route_payment(self, context: RoutingContext) -> Optional[str]:
        """
        AI-driven routing logic (placeholder for future implementation).
        
        This method will integrate with machine learning models to make
        intelligent routing decisions based on historical data, user behavior,
        success rates, and other factors.
        
        Returns:
            Optional[str]: Selected gateway or None if AI routing fails
        """
        # Placeholder for future AI implementation
        # This would integrate with ML models, feature engineering, etc.
        
        self.logger.info("AI routing not yet implemented, falling back to rules")
        return None
    
    async def _is_gateway_suitable(self, gateway: str, payment_request: PaymentRequest, user_config: UserGatewayConfig) -> bool:
        """Check if a gateway is suitable for the payment request"""
        
        # Check if gateway is registered
        if not self.registry.is_gateway_registered(gateway):
            return False
        
        # Check amount limits
        if not await self._is_amount_suitable(gateway, payment_request.amount, user_config):
            return False
        
        # Check currency support
        gateway_info = self.registry.get_gateway_info(gateway)
        supported_currencies = gateway_info.get("supported_currencies", [])
        if payment_request.currency not in supported_currencies:
            return False
        
        # Check payment method support (if specified)
        if payment_request.payment_method:
            supported_methods = gateway_info.get("supported_methods", [])
            if payment_request.payment_method not in supported_methods:
                return False
        
        return True
    
    async def _is_amount_suitable(self, gateway: str, amount: Decimal, user_config: UserGatewayConfig) -> bool:
        """Check if the payment amount is suitable for the gateway"""
        
        # Check user-specific amount thresholds first
        if (user_config.amount_thresholds and 
            gateway in user_config.amount_thresholds):
            thresholds = user_config.amount_thresholds[gateway]
            min_amount = thresholds.get("min_amount", 0)
            max_amount = thresholds.get("max_amount", float('inf'))
            return min_amount <= amount <= max_amount
        
        # Check default amount thresholds
        min_threshold = self.default_rules["min_amount_thresholds"].get(gateway, Decimal("0"))
        max_threshold = self.default_rules["max_amount_thresholds"].get(gateway, Decimal("999999"))
        
        return min_threshold <= amount <= max_threshold
    
    def _get_gateways_for_currency(self, currency: str, available_gateways: List[str]) -> List[str]:
        """Get gateways that support the specified currency"""
        suitable_gateways = []
        
        for gateway in available_gateways:
            if self.registry.is_gateway_registered(gateway):
                gateway_info = self.registry.get_gateway_info(gateway)
                supported_currencies = gateway_info.get("supported_currencies", [])
                if currency in supported_currencies:
                    suitable_gateways.append(gateway)
        
        return suitable_gateways
    
    def _get_gateways_for_payment_method(self, payment_method: str, available_gateways: List[str]) -> List[str]:
        """Get gateways that support the specified payment method"""
        suitable_gateways = []
        
        for gateway in available_gateways:
            if self.registry.is_gateway_registered(gateway):
                gateway_info = self.registry.get_gateway_info(gateway)
                supported_methods = gateway_info.get("supported_methods", [])
                if payment_method in supported_methods:
                    suitable_gateways.append(gateway)
        
        return suitable_gateways
    
    def _select_best_gateway_by_performance(self, gateways: List[str], health: Dict[str, float], load: Dict[str, float]) -> str:
        """Select the best gateway based on health and load metrics"""
        if not gateways:
            raise PaymentGatewayException("No gateways available for selection")
        
        # Calculate performance score for each gateway
        gateway_scores = []
        for gateway in gateways:
            health_score = health.get(gateway, 0.5)
            load_score = 1.0 - load.get(gateway, 0.5)  # Lower load is better
            
            # Weighted performance score
            performance_score = (health_score * 0.7) + (load_score * 0.3)
            gateway_scores.append((gateway, performance_score))
        
        # Sort by performance score (highest first)
        gateway_scores.sort(key=lambda x: x[1], reverse=True)
        
        return gateway_scores[0][0]


# Global router instance
_router = PaymentRouter()


def get_payment_router() -> PaymentRouter:
    """
    Get the global payment router instance.
    
    Returns:
        PaymentRouter: The global router instance
    """
    return _router


async def route_payment(payment_request: PaymentRequest, user_config: UserGatewayConfig) -> str:
    """
    Convenience function to route a payment.
    
    Args:
        payment_request: Payment request to route
        user_config: User gateway configuration
        
    Returns:
        str: Selected gateway identifier
    """
    return await _router.route_payment(payment_request, user_config)
