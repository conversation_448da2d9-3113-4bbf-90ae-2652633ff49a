"""
Tenant management API endpoints
"""

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import Dict, Any
import logging

from ...database.connection import get_db_session
from ...database.models import Tenant

router = APIRouter()
logger = logging.getLogger(__name__)


@router.post("/tenants")
async def create_tenant(
    tenant_data: Dict[str, Any],
    db: Session = Depends(get_db_session)
):
    """Create a new tenant"""
    try:
        tenant = Tenant(
            company_name=tenant_data.get("company_name"),
            business_type=tenant_data.get("business_type"),
            contact_email=tenant_data.get("contact_email"),
            contact_name=tenant_data.get("contact_name"),
            country=tenant_data.get("country", "DM"),
            plan=tenant_data.get("plan", "starter")
        )
        
        db.add(tenant)
        db.commit()
        db.refresh(tenant)
        
        return {
            "tenant_id": tenant.tenant_id,
            "api_key": tenant.api_key,
            "company_name": tenant.company_name,
            "plan": tenant.plan,
            "status": tenant.status,
            "created_at": tenant.created_at.isoformat()
        }
        
    except Exception as e:
        logger.error(f"Tenant creation failed: {e}")
        raise HTTPException(status_code=400, detail="Tenant creation failed")


@router.get("/tenants/{tenant_id}")
async def get_tenant(
    tenant_id: str,
    db: Session = Depends(get_db_session)
):
    """Get tenant details"""
    tenant = db.query(Tenant).filter(Tenant.tenant_id == tenant_id).first()
    
    if not tenant:
        raise HTTPException(status_code=404, detail="Tenant not found")
    
    return {
        "tenant_id": tenant.tenant_id,
        "company_name": tenant.company_name,
        "business_type": tenant.business_type,
        "contact_email": tenant.contact_email,
        "plan": tenant.plan,
        "status": tenant.status,
        "total_transactions": tenant.total_transactions,
        "total_volume": tenant.total_volume,
        "created_at": tenant.created_at.isoformat()
    }
