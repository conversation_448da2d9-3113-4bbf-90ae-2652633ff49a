# VS Code Development Environment Setup

## Required Extensions

1. **GitHub Copilot** - AI code assistance
2. **ESLint** - JavaScript linting
3. **Prettier** - Code formatting
4. **Thunder Client** - API testing
5. **GitLens** - Enhanced Git integration
6. **Docker** - Container management
7. **OpenAPI (Swagger) Editor** - API documentation

## Workspace Settings

Create `.vscode/settings.json` with:

```json
{
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  },
  "eslint.validate": ["javascript", "typescript"],
  "files.autoSave": "onFocusChange"
}
```

## Development Workflow

1. Use GitHub Copilot for boilerplate code generation
2. Test API endpoints with Thunder Client
3. Document all endpoints in OpenAPI spec
4. Use GitLens for code review before commits
5. Run linting before submitting PRs