# 🌴 ELOH Processing Payment Gateway - Render Deployment

## 📋 Overview

Multi-tenant payment gateway optimized for the Caribbean region with crypto-first approach. This version is specifically prepared for deployment on Render.com.

## 🚀 Quick Deploy to Render

### Option 1: One-Click Deploy
[![Deploy to Render](https://render.com/images/deploy-to-render-button.svg)](https://render.com/deploy)

### Option 2: Manual Deployment

1. **Fork/Clone this repository**
2. **Push to your GitHub account**
3. **Create Render services:**
   - PostgreSQL Database: `eloh-gateway-db`
   - Web Service: Connect to your GitHub repo

## 🌍 Regional Gateway Availability

### ✅ Available in Dominica
- **BTCPay Server** - Bitcoin/Lightning payments (Global)
- **NowPayments** - 300+ cryptocurrencies (Global)

### ⚠️ Limited in Dominica
- **Stripe** - Not officially supported
- **Square** - Not available

## 🛠️ Environment Variables

Set these in your Render dashboard:

### Required
```env
APP_NAME=ELOH Processing Payment Gateway
ENVIRONMENT=production
DEBUG=false
SECRET_KEY=auto-generated-by-render
```

### Gateway Credentials
```env
# BTCPay Server (Recommended)
BTCPAY_SERVER_URL=https://your-btcpay-server.com
BTCPAY_API_KEY=your_api_key
BTCPAY_STORE_ID=your_store_id

# NowPayments (Recommended)
NOWPAYMENTS_API_KEY=your_nowpayments_key
NOWPAYMENTS_ENVIRONMENT=production
```

## 📚 API Endpoints

After deployment, visit:
- **Health Check**: `https://your-app.onrender.com/health`
- **API Docs**: `https://your-app.onrender.com/docs`
- **Tenant Portal**: `https://your-app.onrender.com/v1/portal`
- **Gateway Status**: `https://your-app.onrender.com/v1/portal/status`

## 🧪 Testing

### Create a Payment
```bash
curl -X POST "https://your-app.onrender.com/v1/payments" \
  -H "Authorization: Bearer your_api_key" \
  -H "Content-Type: application/json" \
  -d '{
    "amount": 100.00,
    "currency": "USD",
    "description": "Test payment",
    "email": "<EMAIL>"
  }'
```

### Check Gateway Status
```bash
curl "https://your-app.onrender.com/v1/portal/status"
```

## 🔧 Local Development

```bash
# Install dependencies
pip install -r requirements.txt

# Setup database
python setup_database.py --sample-data

# Start server
uvicorn main:app --reload
```

## 💰 Render Pricing

- **Free Tier**: 750 hours/month (perfect for testing)
- **Starter Plan**: $7/month (recommended for production)
- **Database**: Free 1GB, $7/month for 10GB

## 🌟 Features

- ✅ Multi-tenant architecture
- ✅ Regional gateway availability detection
- ✅ Crypto-first payment processing
- ✅ Caribbean/Dominica optimized
- ✅ Beautiful tenant portal
- ✅ Comprehensive API documentation
- ✅ Production-ready monitoring

## 🆘 Support

- **Documentation**: Visit `/docs` endpoint
- **Tenant Portal**: Visit `/v1/portal` endpoint
- **Health Check**: Visit `/health` endpoint

---

**🎉 Ready to process payments in the Caribbean with crypto-first approach!**
