<?php
/**
 * ELOH Processing Production Widget
 * Stateless, cross-platform payment widget
 * Works with: Web, Mobile Apps, Desktop Apps, Static Sites
 */

// Disable session handling completely
ini_set('session.use_cookies', 0);
ini_set('session.use_only_cookies', 0);
ini_set('session.use_trans_sid', 0);

// Set headers for cross-platform compatibility
header('X-Frame-Options: SAMEORIGIN');
header('Content-Security-Policy: frame-ancestors *');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Utility functions
function sanitize_text_field($value) {
    return htmlspecialchars(strip_tags(trim($value ?? '')));
}

function sanitize_email($email) {
    return filter_var(trim($email ?? ''), FILTER_SANITIZE_EMAIL);
}

function load_widget_config($widget_id) {
    // Load from JSON file (stateless)
    $config_file = __DIR__ . "/configs/{$widget_id}.json";
    
    if (file_exists($config_file)) {
        $config = json_decode(file_get_contents($config_file), true);
        if ($config) {
            return $config;
        }
    }
    
    // Return default production config
    return [
        'theme' => 'light',
        'primary_color' => '#667eea',
        'accent_color' => '#764ba2',
        'border_radius' => '12px',
        'font_family' => 'Inter, sans-serif',
        'show_logo' => false,
        'company_name' => 'ELOH Processing',
        'enabled_gateways' => ['btcpay', 'nowpayments'],
        'default_gateway' => 'btcpay',
        'min_amount' => 5.0,
        'max_amount' => 10000.0,
        'require_email' => true,
        'title' => 'Complete Your Payment',
        'description' => 'Secure cryptocurrency payment processing',
        'button_text' => 'Pay Now',
        'show_powered_by' => true,
        'allowed_domains' => [],
        'production_mode' => true,
        'language' => 'en'
    ];
}

// Get widget parameters
$widget_id = sanitize_text_field($_GET['widget_id'] ?? 'production');
$amount = floatval($_GET['amount'] ?? 0);
$description = sanitize_text_field($_GET['description'] ?? '');
$customer_email = sanitize_email($_GET['email'] ?? '');
$currency = strtoupper($_GET['currency'] ?? 'USD');
$gateway = sanitize_text_field($_GET['gateway'] ?? '');
$theme = sanitize_text_field($_GET['theme'] ?? '');

// Load widget configuration
$config = load_widget_config($widget_id);

// Override config with URL parameters if provided
if ($theme) $config['theme'] = $theme;
if ($gateway && in_array($gateway, $config['enabled_gateways'])) {
    $config['default_gateway'] = $gateway;
}

// Determine if this is production mode
$is_production = $config['production_mode'] ?? false;
$is_demo = $widget_id === 'demo' || !$is_production;

// Setup available gateways
if ($is_demo) {
    // Demo mode - mock gateways
    $enabledGateways = [
        'btcpay' => [
            'name' => 'BTCPay Server',
            'icon' => '⚡',
            'description' => 'Self-hosted Bitcoin payment processor',
            'supported_currencies' => ['BTC']
        ],
        'nowpayments' => [
            'name' => 'NowPayments',
            'icon' => '🌐',
            'description' => 'Multi-cryptocurrency payment gateway',
            'supported_currencies' => ['BTC', 'ETH', 'USDT', 'LTC']
        ]
    ];
} else {
    // Production mode - real gateways
    $enabledGateways = [];
    foreach ($config['enabled_gateways'] as $gatewayId) {
        switch ($gatewayId) {
            case 'btcpay':
                $enabledGateways['btcpay'] = [
                    'name' => 'BTCPay Server',
                    'icon' => '⚡',
                    'description' => 'Bitcoin Lightning Network',
                    'supported_currencies' => ['BTC']
                ];
                break;
            case 'nowpayments':
                $enabledGateways['nowpayments'] = [
                    'name' => 'NowPayments',
                    'icon' => '🌐',
                    'description' => 'Multi-cryptocurrency gateway',
                    'supported_currencies' => ['BTC', 'ETH', 'USDT', 'LTC', 'XMR', 'ADA']
                ];
                break;
            case 'square':
                $enabledGateways['square'] = [
                    'name' => 'Square',
                    'icon' => '💳',
                    'description' => 'Credit & Debit Cards',
                    'supported_currencies' => ['USD']
                ];
                break;
        }
    }
}

// Set default gateway if not specified
if (!$gateway || !isset($enabledGateways[$gateway])) {
    $gateway = $config['default_gateway'] ?? array_key_first($enabledGateways);
}

// Generate CSS
function generateCSS($config) {
    $css = ":root {\n";
    $css .= "  --widget-primary: {$config['primary_color']};\n";
    $css .= "  --widget-accent: {$config['accent_color']};\n";
    $css .= "  --widget-radius: {$config['border_radius']};\n";
    $css .= "  --widget-font: {$config['font_family']};\n";
    $css .= "}\n";
    
    if ($config['theme'] === 'dark') {
        $css .= "body { background: #1a1a2e; color: #f7fafc; }\n";
    }
    
    return $css;
}

// Generate JS config
function generateJSConfig($config) {
    return [
        'theme' => $config['theme'],
        'autoResize' => true,
        'debugMode' => !($config['production_mode'] ?? false),
        'timeout' => 300,
        'successRedirect' => $config['success_redirect'] ?? '',
        'cancelRedirect' => $config['cancel_redirect'] ?? ''
    ];
}

?>
<!DOCTYPE html>
<html lang="<?php echo $config['language']; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($config['title']); ?></title>
    
    <!-- Cross-platform compatibility -->
    <meta http-equiv="Content-Security-Policy" content="frame-ancestors *;">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        <?php echo generateCSS($config); ?>
        
        /* Widget Base Styles */
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        
        body {
            font-family: var(--widget-font);
            background: transparent;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .widget-container {
            background: white;
            border-radius: var(--widget-radius);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            padding: 24px;
            width: 100%;
            max-width: 400px;
            border: 1px solid #e2e8f0;
        }
        
        .widget-header {
            text-align: center;
            margin-bottom: 24px;
        }
        
        .widget-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #1a202c;
            margin-bottom: 8px;
        }
        
        .widget-description {
            color: #718096;
            font-size: 0.9rem;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-label {
            display: block;
            font-weight: 500;
            color: #4a5568;
            margin-bottom: 8px;
            font-size: 0.9rem;
        }
        
        .form-input, .form-select {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e2e8f0;
            border-radius: var(--widget-radius);
            font-size: 1rem;
            transition: all 0.2s ease;
            background: white;
        }
        
        .form-input:focus, .form-select:focus {
            outline: none;
            border-color: var(--widget-primary);
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .gateway-selector {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 12px;
            margin-bottom: 20px;
        }
        
        .gateway-option {
            position: relative;
        }
        
        .gateway-option input[type="radio"] {
            position: absolute;
            opacity: 0;
        }
        
        .gateway-option label {
            display: block;
            padding: 16px 12px;
            border: 2px solid #e2e8f0;
            border-radius: var(--widget-radius);
            text-align: center;
            cursor: pointer;
            transition: all 0.2s ease;
            background: white;
        }
        
        .gateway-option input[type="radio"]:checked + label {
            border-color: var(--widget-primary);
            background: rgba(102, 126, 234, 0.05);
        }
        
        .gateway-icon {
            font-size: 1.5rem;
            margin-bottom: 8px;
        }
        
        .gateway-name {
            font-size: 0.85rem;
            font-weight: 500;
            color: #4a5568;
        }
        
        .amount-input-group {
            position: relative;
        }
        
        .currency-symbol {
            position: absolute;
            left: 16px;
            top: 50%;
            transform: translateY(-50%);
            color: #718096;
            font-weight: 500;
        }
        
        .amount-input-group .form-input {
            padding-left: 40px;
        }
        
        .pay-button {
            width: 100%;
            background: linear-gradient(135deg, var(--widget-primary) 0%, var(--widget-accent) 100%);
            color: white;
            border: none;
            padding: 16px 24px;
            border-radius: var(--widget-radius);
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            margin-top: 8px;
        }
        
        .pay-button:hover {
            transform: translateY(-1px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }
        
        .pay-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .powered-by {
            text-align: center;
            margin-top: 20px;
            font-size: 0.8rem;
            color: #a0aec0;
        }
        
        .powered-by a {
            color: var(--widget-primary);
            text-decoration: none;
        }
        
        .error-message {
            background: #fed7d7;
            color: #c53030;
            padding: 12px 16px;
            border-radius: var(--widget-radius);
            margin-bottom: 20px;
            font-size: 0.9rem;
        }
        
        .success-message {
            background: #f0fdf4;
            color: #166534;
            padding: 12px 16px;
            border-radius: var(--widget-radius);
            margin-bottom: 20px;
            font-size: 0.9rem;
        }
        
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }
        
        .loading-spinner {
            width: 32px;
            height: 32px;
            border: 3px solid #e2e8f0;
            border-top: 3px solid var(--widget-primary);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 12px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .demo-badge {
            background: linear-gradient(135deg, #fbbf24, #f59e0b);
            color: white;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            margin-left: 8px;
        }
        
        /* Dark theme */
        body.dark-theme .widget-container {
            background: #2d3748;
            border-color: #4a5568;
            color: #f7fafc;
        }
        
        body.dark-theme .widget-title {
            color: #f7fafc;
        }
        
        body.dark-theme .form-input,
        body.dark-theme .form-select,
        body.dark-theme .gateway-option label {
            background: #4a5568;
            border-color: #718096;
            color: #f7fafc;
        }
        
        /* Responsive */
        @media (max-width: 480px) {
            body {
                padding: 12px;
            }
            
            .widget-container {
                padding: 20px;
            }
            
            .gateway-selector {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body class="<?php echo $config['theme'] === 'dark' ? 'dark-theme' : ''; ?>">
    <div class="widget-container">
        <div class="widget-header">
            <h1 class="widget-title">
                <?php echo htmlspecialchars($config['title']); ?>
                <?php if ($is_demo): ?>
                <span class="demo-badge">Demo</span>
                <?php endif; ?>
            </h1>
            <p class="widget-description"><?php echo htmlspecialchars($config['description']); ?></p>
        </div>
        
        <div id="message-container"></div>
        
        <form id="widget-payment-form" method="POST" action="<?php echo $is_demo ? '#' : 'production-api.php'; ?>">
            <input type="hidden" name="widget_id" value="<?php echo htmlspecialchars($widget_id); ?>">
            <input type="hidden" name="action" value="create_payment">
            
            <?php if (count($enabledGateways) > 1): ?>
            <div class="form-group">
                <label class="form-label">Payment Method</label>
                <div class="gateway-selector">
                    <?php foreach ($enabledGateways as $gatewayId => $gatewayInfo): ?>
                    <div class="gateway-option">
                        <input type="radio" name="gateway" value="<?php echo $gatewayId; ?>" 
                               id="gateway_<?php echo $gatewayId; ?>"
                               <?php echo $gatewayId === $gateway ? 'checked' : ''; ?>>
                        <label for="gateway_<?php echo $gatewayId; ?>">
                            <div class="gateway-icon"><?php echo $gatewayInfo['icon']; ?></div>
                            <div class="gateway-name"><?php echo $gatewayInfo['name']; ?></div>
                        </label>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
            <?php else: ?>
            <input type="hidden" name="gateway" value="<?php echo array_key_first($enabledGateways); ?>">
            <?php endif; ?>
            
            <div class="form-group">
                <label for="amount" class="form-label">Amount</label>
                <div class="amount-input-group">
                    <span class="currency-symbol">$</span>
                    <input type="number" id="amount" name="amount" 
                           min="<?php echo $config['min_amount']; ?>" 
                           max="<?php echo $config['max_amount']; ?>"
                           step="0.01" required class="form-input"
                           value="<?php echo $amount > 0 ? number_format($amount, 2, '.', '') : ''; ?>"
                           placeholder="0.00">
                </div>
            </div>
            
            <?php if ($config['require_email']): ?>
            <div class="form-group">
                <label for="email" class="form-label">Email Address</label>
                <input type="email" id="email" name="email" required class="form-input"
                       value="<?php echo htmlspecialchars($customer_email); ?>"
                       placeholder="<EMAIL>">
            </div>
            <?php endif; ?>
            
            <div class="form-group">
                <label for="description" class="form-label">Description</label>
                <input type="text" id="description" name="description" class="form-input"
                       value="<?php echo htmlspecialchars($description); ?>"
                       placeholder="Payment description">
            </div>
            
            <button type="submit" class="pay-button" id="pay-button">
                <?php echo htmlspecialchars($config['button_text']); ?>
            </button>
        </form>
        
        <div class="loading" id="loading">
            <div class="loading-spinner"></div>
            <div>Processing payment...</div>
        </div>
        
        <?php if ($config['show_powered_by']): ?>
        <div class="powered-by">
            Powered by <a href="https://elohprocessing.infy.uk" target="_blank">ELOH Processing</a>
        </div>
        <?php endif; ?>
    </div>
    
    <script>
        // Widget configuration
        const widgetConfig = <?php echo json_encode(generateJSConfig($config)); ?>;
        const isDemo = <?php echo json_encode($is_demo); ?>;
        
        // Form handling
        document.getElementById('widget-payment-form').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const payButton = document.getElementById('pay-button');
            const loading = document.getElementById('loading');
            const messageContainer = document.getElementById('message-container');
            
            // Show loading state
            payButton.style.display = 'none';
            loading.style.display = 'block';
            messageContainer.innerHTML = '';
            
            // Get form values
            const amount = formData.get('amount');
            const email = formData.get('email');
            const gateway = formData.get('gateway');
            const description = formData.get('description');
            
            if (isDemo) {
                // Demo mode processing
                setTimeout(() => {
                    // Basic validation
                    if (!amount || parseFloat(amount) < <?php echo $config['min_amount']; ?>) {
                        showMessage('Minimum amount is $<?php echo number_format($config['min_amount'], 2); ?>', 'error');
                        resetForm();
                        return;
                    }
                    
                    if (parseFloat(amount) > <?php echo $config['max_amount']; ?>) {
                        showMessage('Maximum amount is $<?php echo number_format($config['max_amount'], 2); ?>', 'error');
                        resetForm();
                        return;
                    }
                    
                    if (!email || !email.includes('@')) {
                        showMessage('Valid email address is required', 'error');
                        resetForm();
                        return;
                    }
                    
                    // Show demo success
                    const gatewayName = getGatewayName(gateway);
                    showMessage(`✅ Demo payment of $${parseFloat(amount).toFixed(2)} via ${gatewayName} completed successfully!<br><small>This is a demo - no real payment was processed.</small>`, 'success');
                    
                    // Send success message to parent
                    if (window.parent && window.parent !== window) {
                        window.parent.postMessage({
                            type: 'eloh_widget_success',
                            payload: {
                                amount: parseFloat(amount),
                                gateway: gateway,
                                email: email,
                                description: description,
                                demo: true
                            }
                        }, '*');
                    }
                    
                }, 2000);
                
                return;
            }
            
            // Production payment processing
            fetch('production-api.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    if (data.redirect_url) {
                        window.top.location.href = data.redirect_url;
                    } else {
                        showMessage('Payment completed successfully!', 'success');
                        
                        // Send success message to parent
                        if (window.parent && window.parent !== window) {
                            window.parent.postMessage({
                                type: 'eloh_widget_success',
                                payload: data
                            }, '*');
                        }
                    }
                } else {
                    showMessage(data.error || 'Payment failed. Please try again.', 'error');
                    resetForm();
                }
            })
            .catch(error => {
                console.error('Payment error:', error);
                showMessage('Network error. Please check your connection and try again.', 'error');
                resetForm();
            });
        });
        
        function showMessage(message, type) {
            const messageContainer = document.getElementById('message-container');
            const className = type === 'error' ? 'error-message' : 'success-message';
            messageContainer.innerHTML = `<div class="${className}">${message}</div>`;
            
            // Auto-resize
            sendResize();
        }
        
        function resetForm() {
            document.getElementById('pay-button').style.display = 'block';
            document.getElementById('loading').style.display = 'none';
        }
        
        function getGatewayName(gateway) {
            const gateways = {
                'btcpay': 'BTCPay Server',
                'nowpayments': 'NowPayments',
                'square': 'Square'
            };
            return gateways[gateway] || gateway;
        }
        
        // Auto-resize functionality
        function sendResize() {
            if (window.parent && window.parent !== window) {
                const height = document.body.scrollHeight;
                window.parent.postMessage({
                    type: 'eloh_widget_resize',
                    height: height
                }, '*');
            }
        }
        
        // Send initial size
        setTimeout(sendResize, 100);
        
        // Send size on changes
        window.addEventListener('resize', sendResize);
        document.addEventListener('change', () => setTimeout(sendResize, 100));
        document.addEventListener('input', () => setTimeout(sendResize, 100));
        
        // Theme detection
        if (widgetConfig.theme === 'auto') {
            const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
            if (prefersDark) {
                document.body.classList.add('dark-theme');
            }
        }
    </script>
</body>
</html>
