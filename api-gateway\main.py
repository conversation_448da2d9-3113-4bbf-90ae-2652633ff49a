"""
ELOH Processing - Consolidated Payment Gateway API
FastAPI-based payment gateway with rule-based routing and future AI integration support
"""

from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Depends, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn
import logging
from contextlib import asynccontextmanager

from app.api.v1 import payments, customers, webhooks, tenants
from app.core.config import get_settings
from app.core.logging import setup_logging
from app.core.exceptions import PaymentGatewayException, handle_payment_exception

# Setup logging
setup_logging()
logger = logging.getLogger(__name__)

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan events"""
    # Startup
    logger.info("🚀 ELOH Processing Payment Gateway API starting up...")
    logger.info("⚡ Rule-based routing system initialized")
    logger.info("🤖 AI fallback system ready for future integration")
    yield
    # Shutdown
    logger.info("🔄 ELOH Processing Payment Gateway API shutting down...")

# Initialize FastAPI app
app = FastAPI(
    title="ELOH Processing Payment Gateway API",
    description="""
    Consolidated payment gateway API with rule-based routing.

    ## Features
    - **Multi-Gateway Support**: Stripe, Square, BTCPay Server, NowPayments
    - **Rule-Based Routing**: Intelligent gateway selection based on configurable rules
    - **AI-Ready Architecture**: Built for future AI-driven routing with rule-based fallback
    - **Unified Interface**: Single API for all payment operations
    - **Secure**: Encrypted credential management and webhook verification

    ## Supported Gateways
    - **Stripe**: Credit cards, ACH, international payments
    - **Square**: In-person and online payments
    - **BTCPay Server**: Bitcoin and Lightning Network
    - **NowPayments**: 300+ cryptocurrencies
    """,
    version="1.0.0",
    contact={
        "name": "ELOH Processing",
        "url": "https://elohprocessing.infy.uk",
        "email": "<EMAIL>"
    },
    license_info={
        "name": "MIT",
        "url": "https://opensource.org/licenses/MIT"
    },
    lifespan=lifespan
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Global exception handler for payment gateway exceptions
@app.exception_handler(PaymentGatewayException)
async def payment_gateway_exception_handler(request: Request, exc: PaymentGatewayException):
    return await handle_payment_exception(request, exc)

# Health check endpoint
@app.get("/health", tags=["Health"])
async def health_check():
    """Health check endpoint for monitoring"""
    return {
        "status": "healthy",
        "service": "ELOH Processing Payment Gateway",
        "version": "1.0.0",
        "routing": "rule-based",
        "ai_ready": True
    }

# API version info
@app.get("/", tags=["Info"])
async def root():
    """API information and status"""
    return {
        "message": "ELOH Processing Payment Gateway API",
        "version": "1.0.0",
        "docs": "/docs",
        "health": "/health",
        "api_v1": "/v1",
        "features": {
            "rule_based_routing": True,
            "ai_fallback_ready": True,
            "multi_gateway_support": True,
            "unified_interface": True
        },
        "supported_gateways": [
            "stripe",
            "square",
            "btcpay",
            "nowpayments"
        ]
    }

# Include API routers
app.include_router(
    payments.router,
    prefix="/v1",
    tags=["Payments"]
)

app.include_router(
    customers.router,
    prefix="/v1",
    tags=["Customers"]
)

app.include_router(
    webhooks.router,
    prefix="/v1",
    tags=["Webhooks"]
)

app.include_router(
    tenants.router,
    prefix="/v1",
    tags=["Tenants"]
)

# Development server
if __name__ == "__main__":
    settings = get_settings()
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
