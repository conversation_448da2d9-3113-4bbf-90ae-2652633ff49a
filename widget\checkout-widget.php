<?php
/**
 * ELOH Processing Production Checkout Widget
 * Stateless, cross-platform payment widget
 * Works with: Web, Mobile Apps, Desktop Apps, Static Sites
 */

// Disable session handling completely
ini_set('session.use_cookies', 0);
ini_set('session.use_only_cookies', 0);
ini_set('session.use_trans_sid', 0);

// Set headers for cross-platform compatibility
header('X-Frame-Options: SAMEORIGIN');
header('Content-Security-Policy: frame-ancestors *');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Utility functions
function sanitize_text_field($value) {
    return htmlspecialchars(strip_tags(trim($value ?? '')));
}

function sanitize_email($email) {
    return filter_var(trim($email ?? ''), FILTER_SANITIZE_EMAIL);
}

function load_widget_config($widget_id) {
    // Load from JSON file (stateless)
    $config_file = __DIR__ . "/configs/{$widget_id}.json";

    if (file_exists($config_file)) {
        $config = json_decode(file_get_contents($config_file), true);
        if ($config) {
            return $config;
        }
    }

    // Return default production config
    return [
        'theme' => 'light',
        'primary_color' => '#667eea',
        'accent_color' => '#764ba2',
        'border_radius' => '12px',
        'font_family' => 'Inter, sans-serif',
        'show_logo' => false,
        'company_name' => 'ELOH Processing',
        'enabled_gateways' => ['btcpay', 'nowpayments'],
        'default_gateway' => 'btcpay',
        'min_amount' => 5.0,
        'max_amount' => 10000.0,
        'require_email' => true,
        'title' => 'Complete Your Payment',
        'description' => 'Secure cryptocurrency payment processing',
        'button_text' => 'Pay Now',
        'show_powered_by' => true,
        'allowed_domains' => [],
        'production_mode' => true
    ];
}

// Get widget parameters
$widget_id = sanitize_text_field($_GET['widget_id'] ?? 'production');
$amount = floatval($_GET['amount'] ?? 0);
$description = sanitize_text_field($_GET['description'] ?? '');
$customer_email = sanitize_email($_GET['email'] ?? '');
$currency = strtoupper($_GET['currency'] ?? 'USD');
$gateway = sanitize_text_field($_GET['gateway'] ?? '');
$theme = sanitize_text_field($_GET['theme'] ?? '');

// Load widget configuration
$config = load_widget_config($widget_id);

// Override config with URL parameters if provided
if ($theme) $config['theme'] = $theme;
if ($gateway && in_array($gateway, $config['enabled_gateways'])) {
    $config['default_gateway'] = $gateway;
}

// Determine if this is production mode
$is_production = $config['production_mode'] ?? false;
$is_demo = $widget_id === 'demo' || !$is_production;

// Get widget parameters
$amount = floatval($_GET['amount'] ?? 0);
$description = sanitize_text_field($_GET['description'] ?? '');
$customer_email = sanitize_email($_GET['email'] ?? '');
$currency = strtoupper($_GET['currency'] ?? 'USD');
$gateway = sanitize_text_field($_GET['gateway'] ?? $widget_config->get('default_gateway'));

// For demo mode, create mock gateway data
if ($widget_id === 'demo') {
    $enabledGateways = [
        'btcpay' => [
            'name' => 'BTCPay Server',
            'icon' => '⚡',
            'description' => 'Self-hosted Bitcoin payment processor',
            'supported_currencies' => ['BTC']
        ],
        'nowpayments' => [
            'name' => 'NowPayments',
            'icon' => '🌐',
            'description' => 'Multi-cryptocurrency payment gateway',
            'supported_currencies' => ['BTC', 'ETH', 'USDT', 'LTC']
        ]
    ];
} else {
    // Initialize payment gateway manager for real widgets
    require_once __DIR__ . '/../includes/payment-gateway-manager.php';
    $gatewayManager = new Payment_Gateway_Manager();
    $availableGateways = $gatewayManager->getAvailableGateways();

    // Filter gateways based on widget configuration
    $enabledGateways = array_intersect_key(
        $availableGateways,
        array_flip($widget_config->get('enabled_gateways'))
    );
}

?>
<!DOCTYPE html>
<html lang="<?php echo $widget_config->get('language'); ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($widget_config->get('title')); ?></title>

    <!-- Prevent embedding in frames from unauthorized domains -->
    <meta http-equiv="Content-Security-Policy" content="frame-ancestors 'self' <?php echo implode(' ', $widget_config->get('allowed_domains')); ?>;">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <style>
        <?php echo $widget_config->generateCSS(); ?>

        /* Widget Base Styles */
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: var(--widget-font);
            background: transparent;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .widget-container {
            background: white;
            border-radius: var(--widget-radius);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            padding: 24px;
            width: 100%;
            max-width: 400px;
            border: 1px solid #e2e8f0;
        }

        .widget-header {
            text-align: center;
            margin-bottom: 24px;
        }

        .widget-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #1a202c;
            margin-bottom: 8px;
        }

        .widget-description {
            color: #718096;
            font-size: 0.9rem;
        }

        .widget-logo {
            max-height: 40px;
            margin-bottom: 16px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            font-weight: 500;
            color: #4a5568;
            margin-bottom: 8px;
            font-size: 0.9rem;
        }

        .form-input, .form-select {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e2e8f0;
            border-radius: var(--widget-radius);
            font-size: 1rem;
            transition: all 0.2s ease;
            background: white;
        }

        .form-input:focus, .form-select:focus {
            outline: none;
            border-color: var(--widget-primary);
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .gateway-selector {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 12px;
            margin-bottom: 20px;
        }

        .gateway-option {
            position: relative;
        }

        .gateway-option input[type="radio"] {
            position: absolute;
            opacity: 0;
        }

        .gateway-option label {
            display: block;
            padding: 16px 12px;
            border: 2px solid #e2e8f0;
            border-radius: var(--widget-radius);
            text-align: center;
            cursor: pointer;
            transition: all 0.2s ease;
            background: white;
        }

        .gateway-option input[type="radio"]:checked + label {
            border-color: var(--widget-primary);
            background: rgba(102, 126, 234, 0.05);
        }

        .gateway-icon {
            font-size: 1.5rem;
            margin-bottom: 8px;
        }

        .gateway-name {
            font-size: 0.85rem;
            font-weight: 500;
            color: #4a5568;
        }

        .amount-input-group {
            position: relative;
        }

        .currency-symbol {
            position: absolute;
            left: 16px;
            top: 50%;
            transform: translateY(-50%);
            color: #718096;
            font-weight: 500;
        }

        .amount-input-group .form-input {
            padding-left: 40px;
        }

        .pay-button {
            width: 100%;
            background: linear-gradient(135deg, var(--widget-primary) 0%, var(--widget-accent) 100%);
            color: white;
            border: none;
            padding: 16px 24px;
            border-radius: var(--widget-radius);
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            margin-top: 8px;
        }

        .pay-button:hover {
            transform: translateY(-1px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .pay-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .powered-by {
            text-align: center;
            margin-top: 20px;
            font-size: 0.8rem;
            color: #a0aec0;
        }

        .powered-by a {
            color: var(--widget-primary);
            text-decoration: none;
        }

        .error-message {
            background: #fed7d7;
            color: #c53030;
            padding: 12px 16px;
            border-radius: var(--widget-radius);
            margin-bottom: 20px;
            font-size: 0.9rem;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }

        .loading-spinner {
            width: 32px;
            height: 32px;
            border: 3px solid #e2e8f0;
            border-top: 3px solid var(--widget-primary);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 12px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Dark theme */
        body.dark-theme .widget-container {
            background: #2d3748;
            border-color: #4a5568;
            color: #f7fafc;
        }

        body.dark-theme .widget-title {
            color: #f7fafc;
        }

        body.dark-theme .form-input,
        body.dark-theme .form-select,
        body.dark-theme .gateway-option label {
            background: #4a5568;
            border-color: #718096;
            color: #f7fafc;
        }

        /* Responsive */
        @media (max-width: 480px) {
            body {
                padding: 12px;
            }

            .widget-container {
                padding: 20px;
            }

            .gateway-selector {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body class="<?php echo $widget_config->get('theme') === 'dark' ? 'dark-theme' : ''; ?>">
    <div class="widget-container">
        <?php if ($widget_config->get('show_logo') && $widget_config->get('logo_url')): ?>
        <div class="widget-header">
            <img src="<?php echo htmlspecialchars($widget_config->get('logo_url')); ?>"
                 alt="<?php echo htmlspecialchars($widget_config->get('company_name')); ?>"
                 class="widget-logo">
        </div>
        <?php endif; ?>

        <div class="widget-header">
            <h1 class="widget-title"><?php echo htmlspecialchars($widget_config->get('title')); ?></h1>
            <p class="widget-description"><?php echo htmlspecialchars($widget_config->get('description')); ?></p>
        </div>

        <div id="error-container"></div>

        <form id="widget-payment-form" method="POST" action="<?php echo $widget_id === 'demo' ? '#' : 'widget-api.php'; ?>">
            <input type="hidden" name="widget_id" value="<?php echo htmlspecialchars($widget_id); ?>">
            <input type="hidden" name="action" value="create_payment">

            <?php if (count($enabledGateways) > 1): ?>
            <div class="form-group">
                <label class="form-label">Payment Method</label>
                <div class="gateway-selector">
                    <?php foreach ($enabledGateways as $gatewayId => $gatewayInfo): ?>
                    <div class="gateway-option">
                        <input type="radio" name="gateway" value="<?php echo $gatewayId; ?>"
                               id="gateway_<?php echo $gatewayId; ?>"
                               <?php echo $gatewayId === $gateway ? 'checked' : ''; ?>>
                        <label for="gateway_<?php echo $gatewayId; ?>">
                            <div class="gateway-icon"><?php echo $gatewayInfo['icon']; ?></div>
                            <div class="gateway-name"><?php echo $gatewayInfo['name']; ?></div>
                        </label>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
            <?php else: ?>
            <input type="hidden" name="gateway" value="<?php echo array_key_first($enabledGateways); ?>">
            <?php endif; ?>

            <div class="form-group">
                <label for="amount" class="form-label">Amount</label>
                <div class="amount-input-group">
                    <span class="currency-symbol">$</span>
                    <input type="number" id="amount" name="amount"
                           min="<?php echo $widget_config->get('min_amount'); ?>"
                           max="<?php echo $widget_config->get('max_amount'); ?>"
                           step="0.01" required class="form-input"
                           value="<?php echo $amount > 0 ? number_format($amount, 2, '.', '') : ''; ?>"
                           placeholder="0.00">
                </div>
            </div>

            <?php if ($widget_config->get('require_email')): ?>
            <div class="form-group">
                <label for="email" class="form-label">Email Address</label>
                <input type="email" id="email" name="email" required class="form-input"
                       value="<?php echo htmlspecialchars($customer_email); ?>"
                       placeholder="<EMAIL>">
            </div>
            <?php endif; ?>

            <?php if ($widget_config->get('require_description')): ?>
            <div class="form-group">
                <label for="description" class="form-label">Description</label>
                <input type="text" id="description" name="description" class="form-input"
                       value="<?php echo htmlspecialchars($description); ?>"
                       placeholder="Payment description">
            </div>
            <?php endif; ?>

            <button type="submit" class="pay-button" id="pay-button">
                <?php echo htmlspecialchars($widget_config->get('button_text')); ?>
            </button>
        </form>

        <div class="loading" id="loading">
            <div class="loading-spinner"></div>
            <div>Processing payment...</div>
        </div>

        <?php if ($widget_config->get('show_powered_by')): ?>
        <div class="powered-by">
            Powered by <a href="https://elohprocessing.infy.uk" target="_blank">ELOH Processing</a>
        </div>
        <?php endif; ?>
    </div>

    <script>
        // Widget configuration
        const widgetConfig = <?php echo json_encode($widget_config->generateJSConfig()); ?>;

        // Form handling
        document.getElementById('widget-payment-form').addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            const payButton = document.getElementById('pay-button');
            const loading = document.getElementById('loading');
            const errorContainer = document.getElementById('error-container');
            const widgetId = formData.get('widget_id');

            // Show loading state
            payButton.style.display = 'none';
            loading.style.display = 'block';
            errorContainer.innerHTML = '';

            // Handle demo mode
            if (widgetId === 'demo') {
                // Simulate demo payment processing
                setTimeout(() => {
                    const amount = formData.get('amount');
                    const email = formData.get('email');
                    const gateway = formData.get('gateway');

                    // Basic validation for demo
                    if (!amount || parseFloat(amount) < 5) {
                        showError('Minimum amount is $5.00');
                        payButton.style.display = 'block';
                        loading.style.display = 'none';
                        return;
                    }

                    if (!email || !email.includes('@')) {
                        showError('Valid email address is required');
                        payButton.style.display = 'block';
                        loading.style.display = 'none';
                        return;
                    }

                    // Show demo success
                    showSuccess(`Demo payment of $${amount} via ${gateway} completed successfully! (This is a demo - no real payment was processed)`);
                }, 2000); // 2 second delay to simulate processing

                return;
            }

            // Real payment processing
            fetch('widget-api.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    if (data.redirect_url) {
                        window.top.location.href = data.redirect_url;
                    } else {
                        showSuccess(data.message || 'Payment completed successfully!');
                    }
                } else {
                    showError(data.error || 'Payment failed. Please try again.');
                }
            })
            .catch(error => {
                console.error('Payment error:', error);
                showError('Network error. Please check your connection and try again.');
            })
            .finally(() => {
                payButton.style.display = 'block';
                loading.style.display = 'none';
            });
        });

        function showError(message) {
            const errorContainer = document.getElementById('error-container');
            errorContainer.innerHTML = `<div class="error-message">${message}</div>`;
        }

        function showSuccess(message) {
            const container = document.querySelector('.widget-container');
            container.innerHTML = `
                <div style="text-align: center; padding: 40px 20px;">
                    <div style="font-size: 3rem; margin-bottom: 16px;">✅</div>
                    <h2 style="color: #22c55e; margin-bottom: 12px;">Payment Successful!</h2>
                    <p style="color: #718096;">${message}</p>
                </div>
            `;

            // Auto-resize if enabled
            if (widgetConfig.autoResize && window.parent) {
                window.parent.postMessage({
                    type: 'eloh_widget_resize',
                    height: container.offsetHeight
                }, '*');
            }

            // Redirect after delay if configured
            if (widgetConfig.successRedirect) {
                setTimeout(() => {
                    window.top.location.href = widgetConfig.successRedirect;
                }, 3000);
            }
        }

        // Auto-resize functionality
        if (widgetConfig.autoResize && window.parent) {
            function sendResize() {
                const height = document.body.scrollHeight;
                window.parent.postMessage({
                    type: 'eloh_widget_resize',
                    height: height
                }, '*');
            }

            // Send initial size
            setTimeout(sendResize, 100);

            // Send size on window resize
            window.addEventListener('resize', sendResize);

            // Send size when form changes
            document.addEventListener('change', () => setTimeout(sendResize, 100));
        }

        // Theme detection
        if (widgetConfig.theme === 'auto') {
            const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
            if (prefersDark) {
                document.body.classList.add('dark-theme');
            }
        }
    </script>
</body>
</html>
