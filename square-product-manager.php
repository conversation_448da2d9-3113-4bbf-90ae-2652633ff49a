<?php
require_once "includes/square-business-manager.php";
include "header.php";

$businessManager = new Square_Business_Manager();
$services = $businessManager->getServices();
?>

<main>
  <section class="hero">
    <h1>Square Product & Service Manager</h1>
  </section>

  <section class="section">
    <div style="max-width: 1000px; margin: 0 auto;">

      <!-- Add New Service Form -->
      <div style="background: white; padding: 20px; border-radius: 10px; margin-bottom: 30px; border: 1px solid #ddd;">
        <h2>➕ Add New Service/Product</h2>
        <form id="addServiceForm" style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
          <div>
            <label for="serviceName" style="display: block; margin-bottom: 5px; font-weight: bold;">Service Name:</label>
            <input type="text" id="serviceName" name="serviceName" required
                   style="width: 100%; padding: 10px; border: 1px solid #ccc; border-radius: 5px;"
                   placeholder="e.g., Advanced Crypto Analysis">
          </div>

          <div>
            <label for="servicePrice" style="display: block; margin-bottom: 5px; font-weight: bold;">Price (USD):</label>
            <input type="number" id="servicePrice" name="servicePrice" min="1" step="0.01" required
                   style="width: 100%; padding: 10px; border: 1px solid #ccc; border-radius: 5px;"
                   placeholder="99.00">
          </div>

          <div style="grid-column: 1 / -1;">
            <label for="serviceDescription" style="display: block; margin-bottom: 5px; font-weight: bold;">Description:</label>
            <textarea id="serviceDescription" name="serviceDescription" rows="3" required
                      style="width: 100%; padding: 10px; border: 1px solid #ccc; border-radius: 5px;"
                      placeholder="Detailed description of the service..."></textarea>
          </div>

          <div>
            <label for="serviceCategory" style="display: block; margin-bottom: 5px; font-weight: bold;">Category:</label>
            <select id="serviceCategory" name="serviceCategory" required
                    style="width: 100%; padding: 10px; border: 1px solid #ccc; border-radius: 5px;">
              <option value="">Select category...</option>
              <option value="consulting">Consulting</option>
              <option value="membership">Membership</option>
              <option value="service">Service</option>
              <option value="report">Report</option>
              <option value="product">Product</option>
            </select>
          </div>

          <div>
            <label for="serviceDuration" style="display: block; margin-bottom: 5px; font-weight: bold;">Duration (minutes):</label>
            <input type="number" id="serviceDuration" name="serviceDuration" min="15" step="15"
                   style="width: 100%; padding: 10px; border: 1px solid #ccc; border-radius: 5px;"
                   placeholder="60">
            <small style="color: #666;">Leave empty for non-time-based services</small>
          </div>

          <div style="grid-column: 1 / -1; text-align: center; margin-top: 20px;">
            <button type="submit" class="cta-button" style="padding: 12px 30px;">
              ➕ Add Service to Catalog
            </button>
          </div>
        </form>
      </div>

      <!-- Current Services -->
      <div style="background: white; padding: 20px; border-radius: 10px; margin-bottom: 30px; border: 1px solid #ddd;">
        <h2>📦 Current Services & Products</h2>

        <?php if ($services['success']): ?>
        <div style="display: grid; gap: 20px;">
          <?php foreach ($services['services'] as $index => $service): ?>
          <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; border-left: 4px solid #0077cc;">
            <div style="display: grid; grid-template-columns: 1fr auto auto; gap: 20px; align-items: start;">

              <!-- Service Info -->
              <div>
                <h3 style="margin: 0 0 10px 0; color: #0077cc;"><?php echo htmlspecialchars($service['name']); ?></h3>
                <p style="color: #666; margin: 10px 0;"><?php echo htmlspecialchars($service['description']); ?></p>
                <div style="display: flex; gap: 15px; margin-top: 15px;">
                  <span style="background: #28a745; color: white; padding: 4px 12px; border-radius: 20px; font-size: 0.8em;">
                    💰 $<?php echo number_format($service['price'], 2); ?>
                  </span>
                  <span style="background: #6c757d; color: white; padding: 4px 12px; border-radius: 20px; font-size: 0.8em;">
                    📂 <?php echo ucfirst($service['category']); ?>
                  </span>
                  <span style="background: #17a2b8; color: white; padding: 4px 12px; border-radius: 20px; font-size: 0.8em;">
                    🆔 <?php echo htmlspecialchars($service['id']); ?>
                  </span>
                </div>
              </div>

              <!-- Actions -->
              <div style="display: flex; flex-direction: column; gap: 10px;">
                <button onclick="editService('<?php echo $service['id']; ?>')"
                        style="background: #ffc107; color: #212529; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">
                  ✏️ Edit
                </button>
                <button onclick="viewAnalytics('<?php echo $service['id']; ?>')"
                        style="background: #17a2b8; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">
                  📊 Analytics
                </button>
              </div>

              <!-- Quick Payment Link -->
              <div>
                <a href="multi-gateway-payment-form.php?type=service&service=<?php echo urlencode($service['id']); ?>&amount=<?php echo $service['price']; ?>"
                   target="_blank" class="cta-button" style="text-decoration: none; padding: 10px 20px; font-size: 0.9em;">
                  💳 Payment Link
                </a>
              </div>
            </div>
          </div>
          <?php endforeach; ?>
        </div>
        <?php else: ?>
        <div style="background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px;">
          <h3>❌ Error Loading Services</h3>
          <p><?php echo htmlspecialchars($services['error']); ?></p>
          <p><strong>Note:</strong> This is expected when Square SDK is not installed or configured.</p>
        </div>
        <?php endif; ?>
      </div>

      <!-- Inventory Management -->
      <div style="background: white; padding: 20px; border-radius: 10px; margin-bottom: 30px; border: 1px solid #ddd;">
        <h2>📊 Inventory & Stock Management</h2>
        <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 20px;">

          <div style="background: #e7f3ff; padding: 15px; border-radius: 8px; text-align: center;">
            <h3>🔄 Service Availability</h3>
            <p style="font-size: 2em; margin: 10px 0; color: #28a745;">✅</p>
            <p>All services available</p>
            <button onclick="toggleAvailability()" style="background: #0077cc; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">
              Manage Availability
            </button>
          </div>

          <div style="background: #f0f8ff; padding: 15px; border-radius: 8px; text-align: center;">
            <h3>📈 Booking Capacity</h3>
            <p style="font-size: 2em; margin: 10px 0; color: #0077cc;">85%</p>
            <p>Current utilization</p>
            <button onclick="manageCapacity()" style="background: #28a745; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">
              Adjust Capacity
            </button>
          </div>

          <div style="background: #fff8f0; padding: 15px; border-radius: 8px; text-align: center;">
            <h3>⏰ Schedule Management</h3>
            <p style="font-size: 2em; margin: 10px 0; color: #ffc107;">📅</p>
            <p>Booking calendar</p>
            <button onclick="manageSchedule()" style="background: #6c757d; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">
              View Schedule
            </button>
          </div>
        </div>
      </div>

      <!-- Pricing & Promotions -->
      <div style="background: white; padding: 20px; border-radius: 10px; margin-bottom: 30px; border: 1px solid #ddd;">
        <h2>💰 Pricing & Promotions</h2>
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">

          <div style="background: #f8f9fa; padding: 15px; border-radius: 8px;">
            <h3>🎯 Dynamic Pricing</h3>
            <p>Adjust prices based on demand, time, or customer type.</p>
            <button onclick="managePricing()" class="cta-button" style="width: 100%; margin-top: 10px;">
              Configure Pricing Rules
            </button>
          </div>

          <div style="background: #f8f9fa; padding: 15px; border-radius: 8px;">
            <h3>🎁 Promotions & Discounts</h3>
            <p>Create discount codes, loyalty rewards, and special offers.</p>
            <button onclick="managePromotions()" class="cta-button" style="width: 100%; margin-top: 10px; background: #28a745;">
              Create Promotions
            </button>
          </div>
        </div>
      </div>

      <!-- Navigation -->
      <div style="text-align: center; margin: 30px 0;">
        <a href="square-admin-dashboard.php" class="cta-button" style="text-decoration: none; margin-right: 10px;">
          ← Admin Dashboard
        </a>
        <a href="square-customer-manager.php" class="cta-button" style="text-decoration: none; margin-right: 10px; background: #28a745;">
          👥 Customer Manager
        </a>
        <a href="multi-gateway-payment-form.php" class="cta-button" style="text-decoration: none; background: #6c757d;">
          💳 Payment Form
        </a>
      </div>
    </div>
  </section>
</main>

<script>
console.log('Square Product Manager loaded');

// Form submission
document.getElementById('addServiceForm').addEventListener('submit', function(e) {
  e.preventDefault();

  const formData = new FormData(this);
  const serviceData = {
    name: formData.get('serviceName'),
    price: parseFloat(formData.get('servicePrice')),
    description: formData.get('serviceDescription'),
    category: formData.get('serviceCategory'),
    duration: formData.get('serviceDuration') ? parseInt(formData.get('serviceDuration')) : null
  };

  // In a real implementation, this would make an AJAX call to create the service
  console.log('Creating service:', serviceData);
  alert('Service would be created in Square catalog (requires Square SDK and credentials)');

  // Reset form
  this.reset();
});

// Management functions
function editService(serviceId) {
  alert(`Edit service: ${serviceId} (requires Square SDK)`);
}

function viewAnalytics(serviceId) {
  alert(`View analytics for service: ${serviceId} (requires Square SDK and data)`);
}

function toggleAvailability() {
  alert('Service availability management (requires Square Inventory API)');
}

function manageCapacity() {
  alert('Booking capacity management (requires Square Bookings API)');
}

function manageSchedule() {
  alert('Schedule management (requires Square Bookings API)');
}

function managePricing() {
  alert('Dynamic pricing configuration (requires Square Catalog API)');
}

function managePromotions() {
  alert('Promotions and discounts management (requires Square Catalog API)');
}

// Enhanced form interactions with futuristic styling
document.querySelectorAll('input, select, textarea').forEach(element => {
  // Add form styling classes
  element.classList.add('form-input');
  if (element.tagName === 'SELECT') {
    element.classList.add('form-select');
  }
  if (element.tagName === 'TEXTAREA') {
    element.classList.add('form-textarea');
  }

  // Enhanced focus effects
  element.addEventListener('focus', function() {
    this.style.borderColor = 'var(--primary-color)';
    this.style.boxShadow = '0 0 0 3px var(--focus-ring), var(--shadow-md)';
    this.style.transform = 'translateY(-2px)';
  });

  element.addEventListener('blur', function() {
    this.style.borderColor = 'var(--border-color)';
    this.style.boxShadow = 'var(--shadow-sm)';
    this.style.transform = 'translateY(0)';
  });

  // Hover effects
  element.addEventListener('mouseenter', function() {
    if (this !== document.activeElement) {
      this.style.borderColor = 'var(--primary-color)';
      this.style.boxShadow = 'var(--shadow-md)';
    }
  });

  element.addEventListener('mouseleave', function() {
    if (this !== document.activeElement) {
      this.style.borderColor = 'var(--border-color)';
      this.style.boxShadow = 'var(--shadow-sm)';
    }
  });
});
</script>

<?php include "footer.php"; ?>
