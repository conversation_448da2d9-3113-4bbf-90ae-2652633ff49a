<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ELOH Processing Widget Demo</title>
    <style>
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            line-height: 1.6;
            color: #333;
            background: #f8fafc;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 40px 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 12px;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 16px;
            font-weight: 700;
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .demo-section {
            background: white;
            border-radius: 12px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        .demo-section h2 {
            color: #2d3748;
            margin-bottom: 20px;
            font-size: 1.5rem;
        }

        .demo-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            align-items: start;
        }

        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 14px;
            overflow-x: auto;
            line-height: 1.5;
        }

        .code-block .comment {
            color: #68d391;
        }

        .code-block .tag {
            color: #f687b3;
        }

        .code-block .attr {
            color: #90cdf4;
        }

        .code-block .string {
            color: #fbb6ce;
        }

        .widget-container {
            min-height: 400px;
            border: 2px dashed #e2e8f0;
            border-radius: 12px;
            padding: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f7fafc;
        }

        .widget-placeholder {
            text-align: center;
            color: #718096;
        }

        .controls {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .control-group {
            display: flex;
            flex-direction: column;
        }

        .control-group label {
            font-weight: 500;
            margin-bottom: 8px;
            color: #4a5568;
        }

        .control-group input,
        .control-group select {
            padding: 10px 12px;
            border: 2px solid #e2e8f0;
            border-radius: 6px;
            font-size: 14px;
        }

        .control-group input:focus,
        .control-group select:focus {
            outline: none;
            border-color: #667eea;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            font-weight: 500;
            cursor: pointer;
            transition: transform 0.2s ease;
        }

        .btn:hover {
            transform: translateY(-1px);
        }

        .integration-methods {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .method-card {
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            padding: 20px;
            background: white;
        }

        .method-card h3 {
            color: #2d3748;
            margin-bottom: 12px;
        }

        .method-card p {
            color: #718096;
            margin-bottom: 16px;
        }

        @media (max-width: 768px) {
            .demo-grid {
                grid-template-columns: 1fr;
            }

            .header h1 {
                font-size: 2rem;
            }

            .controls {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 ELOH Processing Widget Demo</h1>
            <p>Embeddable cryptocurrency payment widget for your website</p>
        </div>

        <!-- Live Demo Section -->
        <div class="demo-section">
            <h2>🎯 Live Demo</h2>
            <div class="controls">
                <div class="control-group">
                    <label for="demo-amount">Amount ($)</label>
                    <input type="number" id="demo-amount" value="50.00" min="5" step="0.01">
                </div>
                <div class="control-group">
                    <label for="demo-email">Email</label>
                    <input type="email" id="demo-email" value="<EMAIL>">
                </div>
                <div class="control-group">
                    <label for="demo-description">Description</label>
                    <input type="text" id="demo-description" value="Demo payment">
                </div>
                <div class="control-group">
                    <label for="demo-theme">Theme</label>
                    <select id="demo-theme">
                        <option value="light">Light</option>
                        <option value="dark">Dark</option>
                        <option value="auto">Auto</option>
                    </select>
                </div>
            </div>
            <button class="btn" onclick="updateDemo()">Update Widget</button>

            <div class="demo-grid" style="margin-top: 30px;">
                <div>
                    <h3>Widget Preview</h3>
                    <div class="widget-container" id="demo-widget">
                        <div class="widget-placeholder">
                            <p>Widget will appear here</p>
                            <p style="font-size: 12px; margin-top: 8px;">Click "Update Widget" to load</p>
                        </div>
                    </div>
                </div>
                <div>
                    <h3>Generated Code</h3>
                    <div class="code-block" id="generated-code">
                        <span class="comment">// Click "Update Widget" to see the code</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Integration Methods -->
        <div class="demo-section">
            <h2>📋 Integration Methods</h2>
            <div class="integration-methods">
                <div class="method-card">
                    <h3>1. JavaScript API</h3>
                    <p>Full control with JavaScript API</p>
                    <div class="code-block">
<span class="tag">&lt;script</span> <span class="attr">src</span>=<span class="string">"https://elohprocessing.infy.uk/widget/widget-embed.js"</span><span class="tag">&gt;&lt;/script&gt;</span>
<span class="tag">&lt;div</span> <span class="attr">id</span>=<span class="string">"payment-widget"</span><span class="tag">&gt;&lt;/div&gt;</span>
<span class="tag">&lt;script&gt;</span>
  ELOHWidget.create({
    widgetId: 'your-widget-id',
    container: '#payment-widget',
    amount: 100.00,
    email: '<EMAIL>'
  });
<span class="tag">&lt;/script&gt;</span>
                    </div>
                </div>

                <div class="method-card">
                    <h3>2. Data Attributes</h3>
                    <p>Simple HTML data attributes</p>
                    <div class="code-block">
<span class="tag">&lt;script</span> <span class="attr">src</span>=<span class="string">"https://elohprocessing.infy.uk/widget/widget-embed.js"</span><span class="tag">&gt;&lt;/script&gt;</span>
<span class="tag">&lt;div</span>
  <span class="attr">data-eloh-widget</span>=<span class="string">"your-widget-id"</span>
  <span class="attr">data-amount</span>=<span class="string">"100.00"</span>
  <span class="attr">data-email</span>=<span class="string">"<EMAIL>"</span>
  <span class="attr">data-theme</span>=<span class="string">"light"</span><span class="tag">&gt;</span>
<span class="tag">&lt;/div&gt;</span>
                    </div>
                </div>

                <div class="method-card">
                    <h3>3. Direct iframe</h3>
                    <p>Simple iframe embedding</p>
                    <div class="code-block">
<span class="tag">&lt;iframe</span>
  <span class="attr">src</span>=<span class="string">"https://elohprocessing.infy.uk/widget/checkout-widget.php?widget_id=your-widget-id&amount=100.00"</span>
  <span class="attr">width</span>=<span class="string">"400"</span>
  <span class="attr">height</span>=<span class="string">"600"</span>
  <span class="attr">frameborder</span>=<span class="string">"0"</span><span class="tag">&gt;</span>
<span class="tag">&lt;/iframe&gt;</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Configuration Options -->
        <div class="demo-section">
            <h2>⚙️ Configuration Options</h2>
            <div class="code-block">
ELOHWidget.create({
  <span class="comment">// Required</span>
  widgetId: 'your-widget-id',        <span class="comment">// Your widget ID</span>
  container: '#payment-widget',      <span class="comment">// Container selector or element</span>

  <span class="comment">// Payment Options</span>
  amount: 100.00,                    <span class="comment">// Pre-fill amount</span>
  email: '<EMAIL>',     <span class="comment">// Pre-fill email</span>
  description: 'Product purchase',   <span class="comment">// Payment description</span>
  currency: 'USD',                   <span class="comment">// Currency code</span>
  gateway: 'btcpay',                 <span class="comment">// Preferred gateway</span>

  <span class="comment">// Appearance</span>
  theme: 'auto',                     <span class="comment">// light, dark, auto</span>
  width: '100%',                     <span class="comment">// Widget width</span>
  height: 'auto',                    <span class="comment">// Widget height</span>

  <span class="comment">// Behavior</span>
  autoResize: true,                  <span class="comment">// Auto-resize iframe</span>
  showLoader: true,                  <span class="comment">// Show loading indicator</span>
  timeout: 30000,                    <span class="comment">// Loading timeout (ms)</span>

  <span class="comment">// Event Callbacks</span>
  onLoad: function(widget) {         <span class="comment">// Widget loaded</span>
    console.log('Widget loaded');
  },
  onSuccess: function(widget, data) { <span class="comment">// Payment successful</span>
    console.log('Payment success:', data);
  },
  onError: function(widget, error) {  <span class="comment">// Payment error</span>
    console.log('Payment error:', error);
  },
  onCancel: function(widget) {       <span class="comment">// Payment cancelled</span>
    console.log('Payment cancelled');
  }
});
            </div>
        </div>

        <!-- Features -->
        <div class="demo-section">
            <h2>✨ Features</h2>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px;">
                <div style="padding: 20px; border: 1px solid #e2e8f0; border-radius: 8px;">
                    <h4 style="color: #2d3748; margin-bottom: 8px;">🔒 Secure</h4>
                    <p style="color: #718096; font-size: 14px;">Enterprise-grade security with iframe sandboxing and domain validation</p>
                </div>
                <div style="padding: 20px; border: 1px solid #e2e8f0; border-radius: 8px;">
                    <h4 style="color: #2d3748; margin-bottom: 8px;">📱 Responsive</h4>
                    <p style="color: #718096; font-size: 14px;">Mobile-first design that works on all devices and screen sizes</p>
                </div>
                <div style="padding: 20px; border: 1px solid #e2e8f0; border-radius: 8px;">
                    <h4 style="color: #2d3748; margin-bottom: 8px;">🎨 Customizable</h4>
                    <p style="color: #718096; font-size: 14px;">Flexible theming and branding options to match your website</p>
                </div>
                <div style="padding: 20px; border: 1px solid #e2e8f0; border-radius: 8px;">
                    <h4 style="color: #2d3748; margin-bottom: 8px;">⚡ Fast</h4>
                    <p style="color: #718096; font-size: 14px;">Lightweight and optimized for fast loading and smooth performance</p>
                </div>
                <div style="padding: 20px; border: 1px solid #e2e8f0; border-radius: 8px;">
                    <h4 style="color: #2d3748; margin-bottom: 8px;">🔗 Multiple Gateways</h4>
                    <p style="color: #718096; font-size: 14px;">Support for BTCPay Server, NowPayments, and Square</p>
                </div>
                <div style="padding: 20px; border: 1px solid #e2e8f0; border-radius: 8px;">
                    <h4 style="color: #2d3748; margin-bottom: 8px;">🔔 Webhooks</h4>
                    <p style="color: #718096; font-size: 14px;">Real-time payment notifications to your backend systems</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Load the widget script -->
    <script src="widget-embed.js"></script>

    <script>
        let currentWidget = null;

        function updateDemo() {
            const amount = document.getElementById('demo-amount').value;
            const email = document.getElementById('demo-email').value;
            const description = document.getElementById('demo-description').value;
            const theme = document.getElementById('demo-theme').value;

            // Clear container
            const container = document.getElementById('demo-widget');
            container.innerHTML = '';

            // Create iframe with standalone demo widget
            const iframe = document.createElement('iframe');
            iframe.src = `demo-widget-standalone.html?amount=${amount}&email=${encodeURIComponent(email)}&description=${encodeURIComponent(description)}&theme=${theme}`;
            iframe.style.width = '100%';
            iframe.style.height = '600px';
            iframe.style.border = 'none';
            iframe.style.borderRadius = '12px';
            iframe.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.1)';
            iframe.setAttribute('allowtransparency', 'true');
            iframe.setAttribute('scrolling', 'no');

            // Listen for resize messages
            window.addEventListener('message', function(event) {
                if (event.data && event.data.type === 'eloh_widget_resize') {
                    iframe.style.height = event.data.height + 'px';
                }
            });

            container.appendChild(iframe);

            // Update generated code
            updateGeneratedCode(amount, email, description, theme);
        }

        function updateGeneratedCode(amount, email, description, theme) {
            const code = `<span class="comment">// JavaScript API Method</span>
<span class="tag">&lt;script</span> <span class="attr">src</span>=<span class="string">"https://elohprocessing.infy.uk/widget/widget-embed.js"</span><span class="tag">&gt;&lt;/script&gt;</span>
<span class="tag">&lt;div</span> <span class="attr">id</span>=<span class="string">"payment-widget"</span><span class="tag">&gt;&lt;/div&gt;</span>
<span class="tag">&lt;script&gt;</span>
  ELOHWidget.create({
    widgetId: 'your-widget-id',
    container: '#payment-widget',
    amount: ${amount},
    email: '${email}',
    description: '${description}',
    theme: '${theme}'
  });
<span class="tag">&lt;/script&gt;</span>

<span class="comment">// Data Attributes Method</span>
<span class="tag">&lt;div</span>
  <span class="attr">data-eloh-widget</span>=<span class="string">"your-widget-id"</span>
  <span class="attr">data-amount</span>=<span class="string">"${amount}"</span>
  <span class="attr">data-email</span>=<span class="string">"${email}"</span>
  <span class="attr">data-description</span>=<span class="string">"${description}"</span>
  <span class="attr">data-theme</span>=<span class="string">"${theme}"</span><span class="tag">&gt;</span>
<span class="tag">&lt;/div&gt;</span>`;

            document.getElementById('generated-code').innerHTML = code;
        }

        // Initialize demo on page load
        document.addEventListener('DOMContentLoaded', function() {
            updateDemo();
        });
    </script>
</body>
</html>
