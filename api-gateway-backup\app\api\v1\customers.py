"""
Customer endpoints for ELOH Processing Payment Gateway API

This module implements customer management endpoints that provide
a unified interface for managing customers across multiple gateways.
"""

from fastapi import APIRouter, Depends, HTTPException
from typing import List, Optional
import logging

from ...models.customer import (
    CustomerRequest, CustomerResponse, CustomerUpdateRequest,
    CustomerListRequest, CustomerListResponse
)
from ...core.routing import UserGatewayConfig
from ...core.adapter_registry import get_adapter_registry
from ...core.exceptions import PaymentGatewayException, create_http_exception
from ...core.config import get_settings

router = APIRouter()
logger = logging.getLogger(__name__)


# Dependency to get user configuration (same as payments)
async def get_user_gateway_config(user_id: str = "default") -> UserGatewayConfig:
    """Get user-specific gateway configuration"""
    from .payments import get_user_gateway_config as get_config
    return await get_config(user_id)


@router.post("/customers", response_model=CustomerResponse)
async def create_customer(
    customer_request: CustomerRequest,
    user_config: UserGatewayConfig = Depends(get_user_gateway_config)
) -> CustomerResponse:
    """
    Create a customer across configured gateways.
    
    This endpoint creates a customer record in the primary gateway
    and optionally in other configured gateways for future use.
    """
    try:
        logger.info(f"Creating customer: {customer_request.email}")
        
        # Use the first available gateway as primary
        if not user_config.enabled_gateways:
            raise PaymentGatewayException(
                "No gateways enabled for customer creation",
                error_code="NO_GATEWAYS_ENABLED"
            )
        
        primary_gateway = user_config.enabled_gateways[0]
        
        # Get adapter and credentials
        registry = get_adapter_registry()
        adapter_class = registry.get_adapter_class(primary_gateway)
        settings = get_settings()
        gateway_credentials = settings.get_gateway_credentials(primary_gateway)
        
        if not gateway_credentials:
            raise PaymentGatewayException(
                f"No credentials configured for gateway: {primary_gateway}",
                error_code="GATEWAY_NOT_CONFIGURED",
                gateway=primary_gateway
            )
        
        # Create adapter
        if primary_gateway == "stripe":
            from ...adapters.stripe_adapter import StripeCredentials
            credentials = StripeCredentials(
                secret_key=gateway_credentials["secret_key"],
                publishable_key=gateway_credentials["publishable_key"],
                webhook_secret=gateway_credentials.get("webhook_secret")
            )
        elif primary_gateway == "btcpay":
            from ...adapters.btcpay_adapter import BTCPayCredentials
            credentials = BTCPayCredentials(
                server_url=gateway_credentials["server_url"],
                api_key=gateway_credentials["api_key"],
                store_id=gateway_credentials["store_id"],
                webhook_secret=gateway_credentials.get("webhook_secret")
            )
        else:
            raise PaymentGatewayException(
                f"Adapter not implemented for gateway: {primary_gateway}",
                error_code="ADAPTER_NOT_IMPLEMENTED",
                gateway=primary_gateway
            )
        
        adapter = adapter_class(credentials)
        customer_response = await adapter.create_customer(customer_request)
        
        logger.info(f"Customer created: {customer_response.customer_id}")
        return customer_response
        
    except PaymentGatewayException as e:
        logger.error(f"Customer creation failed: {e.message}")
        raise create_http_exception(e)
    except Exception as e:
        logger.error(f"Unexpected error creating customer: {e}")
        raise HTTPException(
            status_code=500,
            detail={"error": {"code": "INTERNAL_ERROR", "message": "Internal server error"}}
        )


@router.get("/customers/{customer_id}", response_model=CustomerResponse)
async def get_customer(
    customer_id: str,
    user_config: UserGatewayConfig = Depends(get_user_gateway_config)
) -> CustomerResponse:
    """
    Get customer details from the appropriate gateway.
    """
    try:
        logger.info(f"Retrieving customer: {customer_id}")
        
        # Extract gateway from customer ID (format: gateway_actual_id)
        if "_" not in customer_id:
            raise PaymentGatewayException(
                "Invalid customer ID format",
                error_code="INVALID_CUSTOMER_ID"
            )
        
        gateway, gateway_customer_id = customer_id.split("_", 1)
        
        # Verify user has access to this gateway
        if gateway not in user_config.enabled_gateways:
            raise PaymentGatewayException(
                f"Access denied to gateway: {gateway}",
                error_code="ACCESS_DENIED",
                gateway=gateway
            )
        
        # Get adapter and retrieve customer
        registry = get_adapter_registry()
        adapter_class = registry.get_adapter_class(gateway)
        settings = get_settings()
        gateway_credentials = settings.get_gateway_credentials(gateway)
        
        if not gateway_credentials:
            raise PaymentGatewayException(
                f"No credentials configured for gateway: {gateway}",
                error_code="GATEWAY_NOT_CONFIGURED",
                gateway=gateway
            )
        
        # Create adapter
        if gateway == "stripe":
            from ...adapters.stripe_adapter import StripeCredentials
            credentials = StripeCredentials(
                secret_key=gateway_credentials["secret_key"],
                publishable_key=gateway_credentials["publishable_key"],
                webhook_secret=gateway_credentials.get("webhook_secret")
            )
        elif gateway == "btcpay":
            from ...adapters.btcpay_adapter import BTCPayCredentials
            credentials = BTCPayCredentials(
                server_url=gateway_credentials["server_url"],
                api_key=gateway_credentials["api_key"],
                store_id=gateway_credentials["store_id"],
                webhook_secret=gateway_credentials.get("webhook_secret")
            )
        else:
            raise PaymentGatewayException(
                f"Adapter not implemented for gateway: {gateway}",
                error_code="ADAPTER_NOT_IMPLEMENTED",
                gateway=gateway
            )
        
        adapter = adapter_class(credentials)
        customer_response = await adapter.get_customer(gateway_customer_id)
        
        logger.info(f"Customer retrieved: {customer_id}")
        return customer_response
        
    except PaymentGatewayException as e:
        logger.error(f"Failed to retrieve customer: {e.message}")
        raise create_http_exception(e)
    except Exception as e:
        logger.error(f"Unexpected error retrieving customer: {e}")
        raise HTTPException(
            status_code=500,
            detail={"error": {"code": "INTERNAL_ERROR", "message": "Internal server error"}}
        )
