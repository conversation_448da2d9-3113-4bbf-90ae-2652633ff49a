#!/bin/bash

# ELOH Processing Payment Gateway - Quick Deploy to Render Script

set -e  # Exit on any error

echo "🚀 ELOH Processing Payment Gateway - Render Deployment"
echo "=" * 60

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check if git is available
if ! command -v git &> /dev/null; then
    print_error "Git is required but not installed"
    exit 1
fi

# Check if we're in a git repository
if ! git rev-parse --git-dir > /dev/null 2>&1; then
    print_error "This script must be run from a git repository"
    exit 1
fi

print_info "Preparing ELOH Processing Payment Gateway for Render deployment..."

# 1. Check if all required files exist
echo ""
echo "📋 Checking required files..."

required_files=(
    "requirements.txt"
    "main.py"
    "render.yaml"
    "build.sh"
    "Dockerfile"
    ".env.render"
    "setup_database.py"
)

for file in "${required_files[@]}"; do
    if [ -f "$file" ]; then
        print_status "$file exists"
    else
        print_error "$file is missing"
        exit 1
    fi
done

# 2. Make build script executable
echo ""
echo "🔧 Setting up build script..."
chmod +x build.sh
print_status "Build script is executable"

# 3. Check Python dependencies
echo ""
echo "📦 Validating Python dependencies..."
if python3 -c "import sys; sys.exit(0 if sys.version_info >= (3, 11) else 1)" 2>/dev/null; then
    print_status "Python 3.11+ is available"
else
    print_warning "Python 3.11+ recommended for best compatibility"
fi

# 4. Test local setup
echo ""
echo "🧪 Testing local setup..."
if python3 -c "
import sys
sys.path.insert(0, '.')
try:
    from app.core.config import get_settings
    from app.database.connection import get_database_manager
    print('✅ Core imports successful')
except ImportError as e:
    print(f'❌ Import error: {e}')
    sys.exit(1)
"; then
    print_status "Local setup validation passed"
else
    print_error "Local setup validation failed"
    exit 1
fi

# 5. Check git status
echo ""
echo "📝 Checking git status..."
if [ -n "$(git status --porcelain)" ]; then
    print_warning "You have uncommitted changes"
    echo "Uncommitted files:"
    git status --porcelain
    echo ""
    read -p "Do you want to commit these changes? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        git add .
        git commit -m "Prepare for Render deployment"
        print_status "Changes committed"
    else
        print_warning "Proceeding with uncommitted changes"
    fi
else
    print_status "Git working directory is clean"
fi

# 6. Display deployment instructions
echo ""
echo "🌐 Ready for Render Deployment!"
echo "=" * 50

echo ""
echo "📋 Next Steps:"
echo ""
echo "1. 🔗 Push to GitHub:"
echo "   git push origin main"
echo ""
echo "2. 🌐 Go to Render Dashboard:"
echo "   https://dashboard.render.com"
echo ""
echo "3. 📊 Create PostgreSQL Database:"
echo "   - Click 'New +' → 'PostgreSQL'"
echo "   - Name: eloh-gateway-db"
echo "   - Database: eloh_gateway"
echo "   - User: eloh_user"
echo "   - Plan: Free (can upgrade later)"
echo ""
echo "4. 🚀 Create Web Service:"
echo "   - Click 'New +' → 'Web Service'"
echo "   - Connect your GitHub repository"
echo "   - Name: eloh-payment-gateway-api"
echo "   - Environment: Python 3"
echo "   - Build Command: chmod +x build.sh && ./build.sh"
echo "   - Start Command: uvicorn main:app --host 0.0.0.0 --port \$PORT"
echo ""
echo "5. ⚙️  Set Environment Variables:"
echo "   Copy from .env.render and set in Render dashboard:"

echo ""
echo "🔑 Required Environment Variables:"
echo "   APP_NAME=ELOH Processing Payment Gateway"
echo "   ENVIRONMENT=production"
echo "   DEBUG=false"
echo "   LOG_LEVEL=INFO"
echo ""
echo "🔐 Gateway Credentials (set these manually):"
echo "   BTCPAY_SERVER_URL=https://your-btcpay-server.com"
echo "   BTCPAY_API_KEY=your_btcpay_api_key"
echo "   BTCPAY_STORE_ID=your_btcpay_store_id"
echo "   NOWPAYMENTS_API_KEY=your_nowpayments_api_key"
echo ""

echo "6. 🎯 After Deployment:"
echo "   - Test: https://your-app.onrender.com/health"
echo "   - API Docs: https://your-app.onrender.com/docs"
echo "   - Portal: https://your-app.onrender.com/v1/portal"
echo ""

echo "💡 Pro Tips:"
echo "   - Use Render's auto-deploy from GitHub"
echo "   - Set up custom domain for production"
echo "   - Monitor logs in Render dashboard"
echo "   - Upgrade to paid plan for production use"
echo ""

echo "🆘 Need Help?"
echo "   - Render Docs: https://render.com/docs"
echo "   - Deployment Guide: ./RENDER_DEPLOYMENT.md"
echo "   - Support: <EMAIL>"
echo ""

print_status "Deployment preparation complete!"
print_info "Your ELOH Processing Payment Gateway is ready for Render! 🚀"

# Optional: Open Render dashboard
if command -v open &> /dev/null; then
    read -p "Open Render dashboard in browser? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        open "https://dashboard.render.com"
    fi
elif command -v xdg-open &> /dev/null; then
    read -p "Open Render dashboard in browser? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        xdg-open "https://dashboard.render.com"
    fi
fi

echo ""
echo "🎉 Happy deploying!"
