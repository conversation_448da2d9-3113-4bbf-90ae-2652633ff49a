<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>Contact Us - ELOH Processing LLC</title>
  
  <!-- PWA Meta Tags -->
  <meta name="description" content="Get in touch with ELOH Processing LLC. Contact us for cryptocurrency mining services, consulting, and investment opportunities.">
  <meta name="keywords" content="contact eloh processing, crypto mining contact, sustainable mining inquiry">
  
  <!-- PWA Manifest -->
  <link rel="manifest" href="manifest.json">
  <meta name="theme-color" content="#764ba2">
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-title" content="ELOH Processing">
  <link rel="apple-touch-icon" href="assets/icons/icon-192x192.png">
  
  <!-- Favicon -->
  <link rel="icon" type="image/png" sizes="32x32" href="assets/icons/icon-32x32.png">
  
  <!-- Google Fonts -->
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
  
  <!-- Styles -->
  <link rel="stylesheet" href="assets/css/styles.css">
  
  <!-- Scripts -->
  <script src="assets/js/pwa-app.js" defer></script>
  <script src="assets/js/static-site.js" defer></script>
</head>
<body>
  <header>
    <div class="header-container">
      <div class="logo">
        <div class="logo-text">⚡ ELOH Processing</div>
      </div>
      
      <button class="mobile-menu-toggle" onclick="toggleMobileMenu()">
        <div class="hamburger">
          <span></span>
          <span></span>
          <span></span>
        </div>
      </button>
      
      <div class="nav-container">
        <nav>
          <ul>
            <li><a href="index.html" class="nav-link">Home</a></li>
            <li><a href="about.html" class="nav-link">About</a></li>
            <li><a href="services.html" class="nav-link">Services</a></li>
            <li><a href="operations.html" class="nav-link">Operations</a></li>
            <li><a href="investors.html" class="nav-link">Investors</a></li>
            <li><a href="contact.html" class="nav-link active">Contact</a></li>
          </ul>
        </nav>
        
        <button class="theme-toggle" onclick="toggleTheme()">
          <span class="theme-icon">🌙</span>
          <span class="theme-text">Dark</span>
        </button>
      </div>
    </div>
  </header>

  <main>
    <!-- Hero Section -->
    <section class="hero">
      <div class="hero-background">
        <div class="hero-gradient"></div>
        <div class="hero-pattern"></div>
      </div>
      
      <div class="hero-content">
        <div class="hero-text">
          <h1 class="hero-title">
            <span class="title-line">Get In</span>
            <span class="title-line gradient-text">Touch</span>
          </h1>
          
          <p class="hero-description">
            Ready to start your sustainable cryptocurrency mining journey? 
            We're here to help with expert guidance and personalized solutions.
          </p>
        </div>
      </div>
    </section>

    <!-- Contact Stats Section -->
    <section class="section">
      <div class="container">
        <div class="stats-grid">
          <div class="stat-card">
            <div class="stat-icon">⚡</div>
            <div class="stat-content">
              <div class="stat-value">&lt;2hrs</div>
              <div class="stat-title">Response Time</div>
              <div class="stat-description">Average response to all inquiries</div>
            </div>
          </div>
          
          <div class="stat-card">
            <div class="stat-icon">🌍</div>
            <div class="stat-content">
              <div class="stat-value">24/7</div>
              <div class="stat-title">Global Support</div>
              <div class="stat-description">Round-the-clock assistance worldwide</div>
            </div>
          </div>
          
          <div class="stat-card">
            <div class="stat-icon">💬</div>
            <div class="stat-content">
              <div class="stat-value">100%</div>
              <div class="stat-title">Satisfaction</div>
              <div class="stat-description">Client satisfaction rate</div>
            </div>
          </div>
          
          <div class="stat-card">
            <div class="stat-icon">🔒</div>
            <div class="stat-content">
              <div class="stat-value">Secure</div>
              <div class="stat-title">Communication</div>
              <div class="stat-description">Encrypted and confidential</div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Contact Form Section -->
    <section class="section">
      <div class="container">
        <div class="contact-grid">
          <div class="contact-info">
            <h2>💼 Let's Discuss Your Needs</h2>
            <p>
              Whether you're interested in our mining services, consulting, or investment opportunities, 
              our team is ready to provide personalized solutions for your cryptocurrency needs.
            </p>
            
            <div class="contact-methods">
              <div class="contact-method">
                <div class="method-icon">📧</div>
                <div class="method-content">
                  <h4>Email Us</h4>
                  <p><EMAIL></p>
                  <small>Best for detailed inquiries</small>
                </div>
              </div>
              
              <div class="contact-method">
                <div class="method-icon">💬</div>
                <div class="method-content">
                  <h4>Live Chat</h4>
                  <p>Available 24/7</p>
                  <small>Instant support and answers</small>
                </div>
              </div>
              
              <div class="contact-method">
                <div class="method-icon">📞</div>
                <div class="method-content">
                  <h4>Schedule Call</h4>
                  <p>Book a consultation</p>
                  <small>Personalized discussion</small>
                </div>
              </div>
            </div>
            
            <div class="business-hours">
              <h4>🕒 Business Hours</h4>
              <div class="hours-list">
                <div class="hours-item">
                  <span>Monday - Friday</span>
                  <span>9:00 AM - 6:00 PM EST</span>
                </div>
                <div class="hours-item">
                  <span>Saturday</span>
                  <span>10:00 AM - 4:00 PM EST</span>
                </div>
                <div class="hours-item">
                  <span>Sunday</span>
                  <span>Emergency Support Only</span>
                </div>
              </div>
            </div>
          </div>
          
          <div class="contact-form-container">
            <form class="contact-form" id="contact-form">
              <h3>📝 Send Us a Message</h3>
              
              <div class="form-group">
                <label for="name">Full Name *</label>
                <input type="text" id="name" name="name" required>
              </div>
              
              <div class="form-group">
                <label for="email">Email Address *</label>
                <input type="email" id="email" name="email" required>
              </div>
              
              <div class="form-group">
                <label for="phone">Phone Number</label>
                <input type="tel" id="phone" name="phone">
              </div>
              
              <div class="form-group">
                <label for="subject">Subject *</label>
                <select id="subject" name="subject" required>
                  <option value="">Select a topic...</option>
                  <option value="consulting">Crypto Consulting</option>
                  <option value="mining-pool">Mining Pool Membership</option>
                  <option value="mining-services">Mining Services</option>
                  <option value="investment">Investment Opportunities</option>
                  <option value="partnership">Partnership Inquiry</option>
                  <option value="support">Technical Support</option>
                  <option value="other">Other</option>
                </select>
              </div>
              
              <div class="form-group">
                <label for="budget">Budget Range</label>
                <select id="budget" name="budget">
                  <option value="">Select budget range...</option>
                  <option value="under-1k">Under $1,000</option>
                  <option value="1k-5k">$1,000 - $5,000</option>
                  <option value="5k-10k">$5,000 - $10,000</option>
                  <option value="10k-50k">$10,000 - $50,000</option>
                  <option value="50k-plus">$50,000+</option>
                  <option value="enterprise">Enterprise Level</option>
                </select>
              </div>
              
              <div class="form-group">
                <label for="message">Message *</label>
                <textarea id="message" name="message" rows="5" required 
                          placeholder="Tell us about your project, goals, or questions..."></textarea>
              </div>
              
              <div class="form-group checkbox-group">
                <label class="checkbox-label">
                  <input type="checkbox" id="newsletter" name="newsletter">
                  <span class="checkmark"></span>
                  Subscribe to our newsletter for updates and insights
                </label>
              </div>
              
              <div class="form-group checkbox-group">
                <label class="checkbox-label">
                  <input type="checkbox" id="privacy" name="privacy" required>
                  <span class="checkmark"></span>
                  I agree to the <a href="#" target="_blank">Privacy Policy</a> and <a href="#" target="_blank">Terms of Service</a> *
                </label>
              </div>
              
              <button type="submit" class="submit-button">
                <span class="button-icon">📤</span>
                <span class="button-text">Send Message</span>
              </button>
            </form>
          </div>
        </div>
      </div>
    </section>

    <!-- FAQ Section -->
    <section class="section">
      <div class="container">
        <div class="section-header">
          <h2>❓ Frequently Asked Questions</h2>
          <p>Quick answers to common questions</p>
        </div>
        
        <div class="faq-grid">
          <div class="faq-item">
            <h4>How quickly can I get started?</h4>
            <p>Most services can be initiated within 24-48 hours after consultation and agreement finalization.</p>
          </div>
          
          <div class="faq-item">
            <h4>What makes your mining sustainable?</h4>
            <p>We operate exclusively on 100% renewable energy sources, including solar, wind, and hydroelectric power.</p>
          </div>
          
          <div class="faq-item">
            <h4>Do you offer custom solutions?</h4>
            <p>Yes, we provide tailored mining and consulting solutions based on your specific requirements and goals.</p>
          </div>
          
          <div class="faq-item">
            <h4>What are your minimum investment amounts?</h4>
            <p>Investment minimums vary by service: Consulting ($150), Mining Pool ($200), Services ($500), Equity ($10,000).</p>
          </div>
          
          <div class="faq-item">
            <h4>How do you ensure security?</h4>
            <p>We employ enterprise-grade security measures including 24/7 monitoring, encrypted communications, and secure facilities.</p>
          </div>
          
          <div class="faq-item">
            <h4>Can I visit your facilities?</h4>
            <p>Yes, we offer facility tours for serious investors and partners. Contact us to schedule a visit.</p>
          </div>
        </div>
      </div>
    </section>
  </main>

  <footer>
    <div class="footer-container">
      <div class="footer-content">
        <div class="footer-section">
          <h4>⚡ ELOH Processing</h4>
          <p>Sustainable cryptocurrency mining powered by renewable energy</p>
        </div>
        
        <div class="footer-section">
          <h5>Services</h5>
          <ul>
            <li><a href="services.html">Crypto Consulting</a></li>
            <li><a href="services.html">Mining Pool</a></li>
            <li><a href="services.html">Mining Services</a></li>
            <li><a href="services.html">Market Analysis</a></li>
          </ul>
        </div>
        
        <div class="footer-section">
          <h5>Company</h5>
          <ul>
            <li><a href="about.html">About Us</a></li>
            <li><a href="operations.html">Operations</a></li>
            <li><a href="investors.html">Investors</a></li>
            <li><a href="contact.html">Contact</a></li>
          </ul>
        </div>
        
        <div class="footer-section">
          <h5>Connect</h5>
          <div class="social-links">
            <a href="#" class="social-link">📧 Email</a>
            <a href="#" class="social-link">💬 Discord</a>
            <a href="#" class="social-link">🐦 Twitter</a>
          </div>
        </div>
      </div>
      
      <div class="footer-bottom">
        <p>&copy; 2024 ELOH Processing LLC. All rights reserved.</p>
        <p>Sustainable cryptocurrency mining powered by renewable energy</p>
      </div>
    </div>
  </footer>

  <style>
    /* Contact Page Specific Styles */
    .contact-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: var(--space-3xl);
      align-items: start;
    }
    
    .contact-info h2 {
      margin-bottom: var(--space-lg);
    }
    
    .contact-methods {
      margin: var(--space-2xl) 0;
    }
    
    .contact-method {
      display: flex;
      gap: var(--space-md);
      margin-bottom: var(--space-lg);
      padding: var(--space-lg);
      background: var(--surface-color);
      border: 1px solid var(--border-color);
      border-radius: var(--radius-lg);
      transition: all var(--transition-base);
    }
    
    .contact-method:hover {
      border-color: var(--primary-color);
      transform: translateY(-2px);
      box-shadow: var(--shadow-md);
    }
    
    .method-icon {
      font-size: 2rem;
      flex-shrink: 0;
    }
    
    .method-content h4 {
      margin-bottom: var(--space-xs);
    }
    
    .method-content p {
      color: var(--primary-color);
      font-weight: 600;
      margin-bottom: var(--space-xs);
    }
    
    .method-content small {
      color: var(--text-muted);
    }
    
    .business-hours {
      background: var(--surface-color);
      border: 1px solid var(--border-color);
      border-radius: var(--radius-lg);
      padding: var(--space-lg);
    }
    
    .business-hours h4 {
      margin-bottom: var(--space-md);
    }
    
    .hours-item {
      display: flex;
      justify-content: space-between;
      padding: var(--space-sm) 0;
      border-bottom: 1px solid var(--border-light);
    }
    
    .hours-item:last-child {
      border-bottom: none;
    }
    
    .contact-form-container {
      background: var(--surface-color);
      border: 1px solid var(--border-color);
      border-radius: var(--radius-xl);
      padding: var(--space-2xl);
      box-shadow: var(--shadow-sm);
    }
    
    .contact-form h3 {
      margin-bottom: var(--space-xl);
      text-align: center;
    }
    
    .form-group {
      margin-bottom: var(--space-lg);
    }
    
    .form-group label {
      display: block;
      margin-bottom: var(--space-sm);
      font-weight: 600;
      color: var(--text-primary);
    }
    
    .form-group input,
    .form-group select,
    .form-group textarea {
      width: 100%;
      padding: var(--space-md);
      border: 1px solid var(--border-color);
      border-radius: var(--radius-md);
      font-size: 1rem;
      transition: all var(--transition-fast);
      background: var(--background-color);
      color: var(--text-primary);
    }
    
    .form-group input:focus,
    .form-group select:focus,
    .form-group textarea:focus {
      outline: none;
      border-color: var(--primary-color);
      box-shadow: 0 0 0 3px var(--focus-ring);
    }
    
    .checkbox-group {
      display: flex;
      align-items: flex-start;
      gap: var(--space-sm);
    }
    
    .checkbox-label {
      display: flex;
      align-items: flex-start;
      gap: var(--space-sm);
      cursor: pointer;
      font-size: 0.9rem;
      line-height: 1.4;
    }
    
    .checkbox-label input[type="checkbox"] {
      width: auto;
      margin: 0;
    }
    
    .submit-button {
      width: 100%;
      background: var(--primary-gradient);
      color: white;
      border: none;
      padding: var(--space-lg);
      border-radius: var(--radius-lg);
      font-size: 1.1rem;
      font-weight: 600;
      cursor: pointer;
      transition: all var(--transition-base);
      display: flex;
      align-items: center;
      justify-content: center;
      gap: var(--space-sm);
    }
    
    .submit-button:hover {
      transform: translateY(-2px);
      box-shadow: var(--shadow-lg);
    }
    
    .submit-button:disabled {
      opacity: 0.6;
      cursor: not-allowed;
      transform: none;
    }
    
    .faq-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
      gap: var(--space-xl);
    }
    
    .faq-item {
      background: var(--surface-color);
      border: 1px solid var(--border-color);
      border-radius: var(--radius-lg);
      padding: var(--space-lg);
      transition: all var(--transition-base);
    }
    
    .faq-item:hover {
      border-color: var(--primary-color);
      transform: translateY(-2px);
      box-shadow: var(--shadow-md);
    }
    
    .faq-item h4 {
      color: var(--primary-color);
      margin-bottom: var(--space-md);
    }
    
    .faq-item p {
      color: var(--text-muted);
      margin: 0;
    }
    
    @media (max-width: 768px) {
      .contact-grid {
        grid-template-columns: 1fr;
        gap: var(--space-xl);
      }
      
      .contact-form-container {
        padding: var(--space-lg);
      }
      
      .faq-grid {
        grid-template-columns: 1fr;
      }
    }
  </style>
</body>
</html>
