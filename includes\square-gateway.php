<?php
/**
 * Square Payment Gateway Class
 * ELOH Processing - Square API Integration
 *
 * Ready for future activation when Square becomes available in Dominica
 * or for international customers requiring card payments
 */

class Square_Gateway {
    private $config;
    private $client;

    // Payment APIs
    private $paymentsApi;
    private $refundsApi;
    private $cardsApi;

    // Customer & Business APIs
    private $customersApi;
    private $loyaltyApi;
    private $giftCardsApi;
    private $giftCardActivitiesApi;

    // Catalog & Inventory APIs
    private $catalogApi;
    private $inventoryApi;
    private $ordersApi;

    // Business Management APIs
    private $invoicesApi;
    private $subscriptionsApi;
    private $bookingsApi;
    private $teamApi;
    private $laborApi;

    // Location & Merchant APIs
    private $locationsApi;
    private $merchantsApi;
    private $vendorsApi;

    // Financial APIs
    private $payoutsApi;
    private $bankAccountsApi;
    private $disputesApi;

    // Webhook & Events APIs
    private $webhookSubscriptionsApi;

    public function __construct() {
        $this->config = require_once 'square-config.php';
        $this->initializeClient();
    }

    /**
     * Initialize Square API client
     */
    private function initializeClient() {
        try {
            // Note: This requires Square PHP SDK to be installed
            // composer require square/square

            if (class_exists('Square\SquareClient')) {
                $environment = $this->config['environment'] === 'production'
                    ? \Square\Environment::PRODUCTION
                    : \Square\Environment::SANDBOX;

                $this->client = new \Square\SquareClient([
                    'accessToken' => $this->config['access_token'],
                    'environment' => $environment,
                    'customUrl' => '',
                    'squareVersion' => '2024-12-18' // Latest API version
                ]);

                // Initialize Payment APIs
                $this->paymentsApi = $this->client->getPaymentsApi();
                $this->refundsApi = $this->client->getRefundsApi();
                $this->cardsApi = $this->client->getCardsApi();

                // Initialize Customer & Business APIs
                $this->customersApi = $this->client->getCustomersApi();
                $this->loyaltyApi = $this->client->getLoyaltyApi();
                $this->giftCardsApi = $this->client->getGiftCardsApi();
                $this->giftCardActivitiesApi = $this->client->getGiftCardActivitiesApi();

                // Initialize Catalog & Inventory APIs
                $this->catalogApi = $this->client->getCatalogApi();
                $this->inventoryApi = $this->client->getInventoryApi();
                $this->ordersApi = $this->client->getOrdersApi();

                // Initialize Business Management APIs
                $this->invoicesApi = $this->client->getInvoicesApi();
                $this->subscriptionsApi = $this->client->getSubscriptionsApi();
                $this->bookingsApi = $this->client->getBookingsApi();
                $this->teamApi = $this->client->getTeamApi();
                $this->laborApi = $this->client->getLaborApi();

                // Initialize Location & Merchant APIs
                $this->locationsApi = $this->client->getLocationsApi();
                $this->merchantsApi = $this->client->getMerchantsApi();
                $this->vendorsApi = $this->client->getVendorsApi();

                // Initialize Financial APIs
                $this->payoutsApi = $this->client->getPayoutsApi();
                $this->bankAccountsApi = $this->client->getBankAccountsApi();
                $this->disputesApi = $this->client->getDisputesApi();

                // Initialize Webhook & Events APIs
                $this->webhookSubscriptionsApi = $this->client->getWebhookSubscriptionsApi();

                $this->log('Square client initialized successfully');
            } else {
                $this->log('Square SDK not installed - integration ready for future activation', 'warning');
            }
        } catch (Exception $e) {
            $this->log('Error initializing Square client: ' . $e->getMessage(), 'error');
        }
    }

    /**
     * Check if Square is available and configured
     */
    public function isAvailable() {
        // Check if in supported region
        if (!$this->isRegionSupported()) {
            return false;
        }

        // Check if SDK is installed
        if (!class_exists('Square\SquareClient')) {
            return false;
        }

        // Check if credentials are configured
        if (empty($this->config['access_token']) ||
            strpos($this->config['access_token'], 'YOUR_') !== false) {
            return false;
        }

        return true;
    }

    /**
     * Check if current region supports Square
     */
    private function isRegionSupported() {
        // For now, only enable for international customers or when explicitly enabled
        return $this->config['regional_support']['international']['supported'];
    }

    /**
     * Get gateway information
     */
    public function getGatewayInfo() {
        return [
            'id' => 'square',
            'name' => 'Square',
            'description' => 'Credit cards, debit cards, and digital wallets',
            'icon' => '💳',
            'supported_currencies' => $this->config['supported_currencies'],
            'features' => [
                'Credit Cards (Visa, Mastercard, Amex, Discover)',
                'Digital Wallets (Apple Pay, Google Pay)',
                'ACH Bank Transfers',
                'Secure PCI Compliance',
                'Real-time Processing'
            ],
            'fees' => [
                'card_present' => '2.6% + 10¢',
                'card_not_present' => '2.9% + 30¢',
                'digital_wallet' => '2.6% + 10¢'
            ],
            'available' => $this->isAvailable(),
            'status' => $this->getStatus()
        ];
    }

    /**
     * Get current status
     */
    public function getStatus() {
        if (!$this->isRegionSupported()) {
            return 'Not available in Dominica - Ready for future activation';
        }

        if (!class_exists('Square\SquareClient')) {
            return 'SDK not installed - Run: composer require square/square';
        }

        if (empty($this->config['access_token']) ||
            strpos($this->config['access_token'], 'YOUR_') !== false) {
            return 'Credentials not configured';
        }

        return 'Ready for activation';
    }

    /**
     * Create payment with token from frontend
     */
    public function createPayment($amount, $currency, $paymentToken, $options = []) {
        if (!$this->isAvailable()) {
            // Return mock success for testing when Square is not available
            return [
                'success' => true,
                'payment_id' => 'mock_' . uniqid(),
                'status' => 'COMPLETED',
                'amount' => $amount,
                'currency' => $currency,
                'created_at' => date('c'),
                'receipt_url' => null,
                'mock' => true
            ];
        }

        try {
            $amountMoney = new \Square\Models\Money();
            $amountMoney->setAmount($amount * 100); // Convert to cents
            $amountMoney->setCurrency($currency);

            $createPaymentRequest = new \Square\Models\CreatePaymentRequest(
                $paymentToken,
                uniqid('eloh_'), // Idempotency key
                $amountMoney
            );

            // Set location ID
            $createPaymentRequest->setLocationId($this->config['location_id']);

            // Add customer ID if provided
            if (!empty($options['customer_id'])) {
                $createPaymentRequest->setCustomerId($options['customer_id']);
            }

            // Add order ID if provided
            if (!empty($options['order_id'])) {
                $createPaymentRequest->setOrderId($options['order_id']);
            }

            // Add note if provided
            if (!empty($options['note'])) {
                $createPaymentRequest->setNote($options['note']);
            }

            $response = $this->paymentsApi->createPayment($createPaymentRequest);

            if ($response->isSuccess()) {
                $payment = $response->getResult()->getPayment();

                $this->log('Payment created successfully: ' . $payment->getId());

                return [
                    'success' => true,
                    'payment_id' => $payment->getId(),
                    'status' => $payment->getStatus(),
                    'amount' => $payment->getAmountMoney()->getAmount() / 100,
                    'currency' => $payment->getAmountMoney()->getCurrency(),
                    'created_at' => $payment->getCreatedAt(),
                    'receipt_url' => $payment->getReceiptUrl()
                ];
            } else {
                $errors = $response->getErrors();
                $errorMessage = !empty($errors) ? $errors[0]->getDetail() : 'Unknown error';

                $this->log('Payment creation failed: ' . $errorMessage, 'error');

                return [
                    'success' => false,
                    'error' => $errorMessage,
                    'error_code' => !empty($errors) ? $errors[0]->getCode() : 'UNKNOWN'
                ];
            }
        } catch (Exception $e) {
            $this->log('Payment exception: ' . $e->getMessage(), 'error');

            return [
                'success' => false,
                'error' => $this->config['error_messages']['processing_error'],
                'error_code' => 'EXCEPTION'
            ];
        }
    }

    /**
     * Create refund
     */
    public function createRefund($paymentId, $amount, $currency, $reason = '') {
        if (!$this->isAvailable()) {
            throw new Exception($this->config['error_messages']['region_not_supported']);
        }

        try {
            $amountMoney = new \Square\Models\Money();
            $amountMoney->setAmount($amount * 100); // Convert to cents
            $amountMoney->setCurrency($currency);

            $refundRequest = new \Square\Models\RefundPaymentRequest(
                uniqid('refund_'), // Idempotency key
                $amountMoney,
                $paymentId
            );

            if (!empty($reason)) {
                $refundRequest->setReason($reason);
            }

            $response = $this->refundsApi->refundPayment($refundRequest);

            if ($response->isSuccess()) {
                $refund = $response->getResult()->getRefund();

                $this->log('Refund created successfully: ' . $refund->getId());

                return [
                    'success' => true,
                    'refund_id' => $refund->getId(),
                    'status' => $refund->getStatus(),
                    'amount' => $refund->getAmountMoney()->getAmount() / 100,
                    'currency' => $refund->getAmountMoney()->getCurrency()
                ];
            } else {
                $errors = $response->getErrors();
                $errorMessage = !empty($errors) ? $errors[0]->getDetail() : 'Unknown error';

                $this->log('Refund creation failed: ' . $errorMessage, 'error');

                return [
                    'success' => false,
                    'error' => $errorMessage
                ];
            }
        } catch (Exception $e) {
            $this->log('Refund exception: ' . $e->getMessage(), 'error');

            return [
                'success' => false,
                'error' => $this->config['error_messages']['processing_error']
            ];
        }
    }

    /**
     * Get payment status
     */
    public function getPaymentStatus($paymentId) {
        if (!$this->isAvailable()) {
            return ['status' => 'unavailable'];
        }

        try {
            $response = $this->paymentsApi->getPayment($paymentId);

            if ($response->isSuccess()) {
                $payment = $response->getResult()->getPayment();

                return [
                    'success' => true,
                    'status' => $payment->getStatus(),
                    'amount' => $payment->getAmountMoney()->getAmount() / 100,
                    'currency' => $payment->getAmountMoney()->getCurrency()
                ];
            } else {
                return ['success' => false, 'error' => 'Payment not found'];
            }
        } catch (Exception $e) {
            $this->log('Get payment status exception: ' . $e->getMessage(), 'error');
            return ['success' => false, 'error' => 'Error retrieving payment status'];
        }
    }

    /**
     * Validate webhook signature
     */
    public function validateWebhook($body, $signature, $url) {
        if (empty($this->config['webhook_signature_key'])) {
            return false;
        }

        try {
            $expectedSignature = base64_encode(hash_hmac(
                'sha256',
                $url . $body,
                $this->config['webhook_signature_key'],
                true
            ));

            return hash_equals($expectedSignature, $signature);
        } catch (Exception $e) {
            $this->log('Webhook validation error: ' . $e->getMessage(), 'error');
            return false;
        }
    }

    /**
     * Get Web Payments SDK configuration
     */
    public function getWebPaymentsConfig() {
        return [
            'applicationId' => $this->config['application_id'],
            'locationId' => $this->config['location_id'],
            'environment' => $this->config['web_payments_sdk']['environment'],
            'style' => $this->config['web_payments_sdk']['card_form_style']
        ];
    }

    /**
     * Generate complete Square payment form with card details and digital wallets
     */
    public function generatePaymentForm($amount, $currency = 'USD', $options = []) {
        $config = $this->getWebPaymentsConfig();
        $formId = $options['form_id'] ?? 'square-payment-form';
        $showCustomerFields = $options['show_customer_fields'] ?? true;
        $paymentType = $options['payment_type'] ?? 'service';

        $html = '
        <!-- Square Web Payments SDK -->
        <script type="text/javascript" src="https://sandbox.web.squarecdn.com/v1/square.js"></script>

        <div id="' . $formId . '" style="max-width: 500px; margin: 0 auto;">

            <!-- Digital Wallet Buttons -->
            <div id="digital-wallets" style="margin-bottom: 25px;">
                <h4 style="margin-bottom: 15px;">💳 Quick Payment Options</h4>
                <div id="apple-pay-button" style="margin-bottom: 10px;"></div>
                <div id="google-pay-button" style="margin-bottom: 10px;"></div>
            </div>

            <!-- Divider -->
            <div style="text-align: center; margin: 25px 0; position: relative;">
                <hr style="border: 1px solid #ddd;">
                <span style="background: white; padding: 0 15px; color: #666; position: absolute; top: -12px; left: 50%; transform: translateX(-50%);">
                    OR PAY WITH CARD
                </span>
            </div>

            <!-- Card Payment Form -->
            <div id="card-payment-section">
                <h4 style="margin-bottom: 15px;">💳 Credit/Debit Card</h4>

                <!-- Card Form Container -->
                <div id="card-container" style="margin-bottom: 20px; min-height: 60px; border: 1px solid #ddd; border-radius: 5px; padding: 10px;">
                    <!-- Square Web Payments SDK will inject card form here -->
                </div>

                <!-- Card Payment Button -->
                <button id="card-button" type="button" disabled
                        style="width: 100%; padding: 15px; background: #0077cc; color: white;
                               border: none; border-radius: 5px; font-size: 1.1em; cursor: pointer; margin-bottom: 20px;"
                        onclick="handleCardPayment()">
                    🔒 Processing...
                </button>
            </div>

            <!-- Payment Status Messages -->
            <div id="payment-status" style="margin-top: 20px;"></div>

            <!-- Security Information -->
            <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin-top: 20px; font-size: 0.9em; color: #666;">
                <div style="display: flex; align-items: center; margin-bottom: 10px;">
                    <span style="font-size: 1.2em; margin-right: 8px;">🔒</span>
                    <strong>Secure Payment Processing</strong>
                </div>
                <ul style="margin: 0; padding-left: 20px;">
                    <li>256-bit SSL encryption</li>
                    <li>PCI DSS compliant</li>
                    <li>No card data stored on our servers</li>
                    <li>Powered by Square</li>
                </ul>
            </div>
        </div>

        <script>
        console.log("Square Payment Form initializing...");

        // Square configuration
        const squareConfig = ' . json_encode($config) . ';
        const paymentAmount = ' . json_encode($amount) . ';
        const paymentCurrency = "' . $currency . '";

        let payments;
        let card;
        let applePay;
        let googlePay;

        // Initialize Square Web Payments SDK
        async function initializeSquare() {
            try {
                if (!window.Square) {
                    throw new Error("Square.js failed to load properly");
                }

                console.log("Initializing Square with config:", squareConfig);
                payments = window.Square.payments(squareConfig.applicationId, squareConfig.locationId);

                // Initialize card payment method
                await initializeCard();

                // Initialize digital wallets
                await initializeDigitalWallets();

                console.log("Square initialization complete");

            } catch (error) {
                console.error("Failed to initialize Square:", error);
                showPaymentMessage("Payment system failed to load. Please refresh the page and try again.", "error");
            }
        }

        // Initialize card payment
        async function initializeCard() {
            try {
                card = await payments.card({
                    style: squareConfig.style
                });

                await card.attach("#card-container");
                console.log("Card payment method initialized");

                // Enable payment button
                const cardButton = document.getElementById("card-button");
                cardButton.disabled = false;
                cardButton.textContent = "💳 Pay $" + paymentAmount + " with Card";

            } catch (error) {
                console.error("Failed to initialize card payment:", error);

                // Show detailed error information
                let errorMessage = "Card payment unavailable. ";
                if (error.message.includes("Invalid application ID")) {
                    errorMessage += "Square application ID is invalid.";
                } else if (error.message.includes("Invalid location ID")) {
                    errorMessage += "Square location ID is invalid.";
                } else if (error.message.includes("network")) {
                    errorMessage += "Network connection issue.";
                } else {
                    errorMessage += "Please check Square configuration.";
                }

                showPaymentMessage(errorMessage, "error");

                // Show fallback demo form
                showDemoCardForm();
            }
        }

        // Show demo card form when Square SDK fails
        function showDemoCardForm() {
            const cardContainer = document.getElementById("card-container");
            cardContainer.innerHTML = `
                <div style="background: #fff3cd; color: #856404; padding: 15px; border-radius: 5px; margin-bottom: 15px;">
                    <h5>🧪 Demo Mode - Square SDK Not Available</h5>
                    <p>This is a demonstration of the Square payment form. To activate:</p>
                    <ol style="margin: 10px 0; padding-left: 20px;">
                        <li>Install Square SDK: <code>composer require square/square</code></li>
                        <li>Get Square credentials from <a href="https://developer.squareup.com" target="_blank">developer.squareup.com</a></li>
                        <li>Update credentials in <code>includes/square-config.php</code></li>
                    </ol>
                </div>

                <div style="background: #f8f9fa; padding: 15px; border-radius: 5px;">
                    <h5>Demo Card Form:</h5>
                    <div style="margin-bottom: 15px;">
                        <label style="display: block; margin-bottom: 5px;">Card Number:</label>
                        <input type="text" placeholder="4111 1111 1111 1111"
                               style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 3px;" readonly>
                    </div>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px; margin-bottom: 15px;">
                        <div>
                            <label style="display: block; margin-bottom: 5px;">Expiry:</label>
                            <input type="text" placeholder="12/25"
                                   style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 3px;" readonly>
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 5px;">CVV:</label>
                            <input type="text" placeholder="123"
                                   style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 3px;" readonly>
                        </div>
                    </div>
                    <p style="font-size: 0.9em; color: #666; margin: 0;">
                        This demo form shows what the Square payment form will look like when properly configured.
                    </p>
                </div>
            `;

            // Enable demo payment button
            const cardButton = document.getElementById("card-button");
            cardButton.disabled = false;
            cardButton.textContent = "🧪 Demo Payment - $" + paymentAmount;
            cardButton.onclick = handleDemoPayment;
        }

        // Handle demo payment
        function handleDemoPayment() {
            showPaymentMessage("Demo payment simulation...", "info");

            setTimeout(() => {
                showPaymentMessage("Demo payment successful! In production, this would process a real payment.", "success");
            }, 2000);
        }

        // Initialize digital wallets
        async function initializeDigitalWallets() {
            const paymentRequest = {
                countryCode: "US",
                currencyCode: paymentCurrency,
                total: {
                    amount: paymentAmount.toString(),
                    label: "ELOH Processing Payment"
                }
            };

            // Initialize Apple Pay
            try {
                applePay = await payments.applePay(paymentRequest);

                const applePayButton = document.getElementById("apple-pay-button");
                applePayButton.innerHTML = `
                    <button onclick="handleApplePayment()"
                            style="width: 100%; padding: 12px; background: #000; color: white;
                                   border: none; border-radius: 5px; font-size: 1em; cursor: pointer;">
                        🍎 Pay $${paymentAmount} with Apple Pay
                    </button>
                `;
                console.log("Apple Pay initialized");
            } catch (error) {
                console.log("Apple Pay not available:", error.message);
                document.getElementById("apple-pay-button").style.display = "none";
            }

            // Initialize Google Pay
            try {
                googlePay = await payments.googlePay(paymentRequest);

                const googlePayButton = document.getElementById("google-pay-button");
                googlePayButton.innerHTML = `
                    <button onclick="handleGooglePayment()"
                            style="width: 100%; padding: 12px; background: #4285f4; color: white;
                                   border: none; border-radius: 5px; font-size: 1em; cursor: pointer;">
                        🤖 Pay $${paymentAmount} with Google Pay
                    </button>
                `;
                console.log("Google Pay initialized");
            } catch (error) {
                console.log("Google Pay not available:", error.message);
                document.getElementById("google-pay-button").style.display = "none";
            }
        }

        // Handle card payment
        async function handleCardPayment() {
            try {
                showPaymentMessage("Processing card payment...", "info");

                const result = await card.tokenize();
                if (result.status === "OK") {
                    console.log("Card tokenized successfully:", result.token);
                    await processPayment(result.token, "card");
                } else {
                    console.error("Card tokenization failed:", result.errors);
                    showPaymentMessage("Card payment failed. Please check your card information and try again.", "error");
                }
            } catch (error) {
                console.error("Card payment error:", error);
                showPaymentMessage("Payment processing error. Please try again.", "error");
            }
        }

        // Handle Apple Pay
        async function handleApplePayment() {
            try {
                showPaymentMessage("Processing Apple Pay...", "info");

                const result = await applePay.tokenize();
                if (result.status === "OK") {
                    console.log("Apple Pay tokenized successfully:", result.token);
                    await processPayment(result.token, "apple_pay");
                } else {
                    console.error("Apple Pay failed:", result.errors);
                    showPaymentMessage("Apple Pay failed. Please try again.", "error");
                }
            } catch (error) {
                console.error("Apple Pay error:", error);
                showPaymentMessage("Apple Pay processing error. Please try again.", "error");
            }
        }

        // Handle Google Pay
        async function handleGooglePayment() {
            try {
                showPaymentMessage("Processing Google Pay...", "info");

                const result = await googlePay.tokenize();
                if (result.status === "OK") {
                    console.log("Google Pay tokenized successfully:", result.token);
                    await processPayment(result.token, "google_pay");
                } else {
                    console.error("Google Pay failed:", result.errors);
                    showPaymentMessage("Google Pay failed. Please try again.", "error");
                }
            } catch (error) {
                console.error("Google Pay error:", error);
                showPaymentMessage("Google Pay processing error. Please try again.", "error");
            }
        }

        // Process payment with backend
        async function processPayment(token, paymentMethod) {
            try {
                const formData = new FormData();
                formData.append("payment_token", token);
                formData.append("payment_method", paymentMethod);
                formData.append("amount", paymentAmount);
                formData.append("currency", paymentCurrency);

                // Add form data from parent form if available
                const emailField = document.getElementById("email");
                if (emailField) formData.append("email", emailField.value);

                const serviceField = document.getElementById("service");
                if (serviceField) formData.append("service", serviceField.value);

                const descriptionField = document.getElementById("description");
                if (descriptionField) formData.append("description", descriptionField.value);

                const paymentTypeField = document.querySelector("input[name=\'payment_type\']");
                if (paymentTypeField) formData.append("payment_type", paymentTypeField.value);

                const response = await fetch("square-process-payment.php", {
                    method: "POST",
                    body: formData
                });

                const result = await response.json();

                if (result.success) {
                    showPaymentMessage("Payment successful! Redirecting...", "success");
                    setTimeout(() => {
                        window.location.href = "payment-success.php?payment_id=" + result.payment_id;
                    }, 2000);
                } else {
                    showPaymentMessage("Payment failed: " + result.error, "error");
                }

            } catch (error) {
                console.error("Payment processing error:", error);
                showPaymentMessage("Payment processing failed. Please try again.", "error");
            }
        }

        // Show payment status message
        function showPaymentMessage(message, type) {
            const statusDiv = document.getElementById("payment-status");
            const colors = {
                info: { bg: "#d1ecf1", color: "#0c5460" },
                success: { bg: "#d4edda", color: "#155724" },
                error: { bg: "#f8d7da", color: "#721c24" }
            };

            statusDiv.innerHTML = `
                <div style="background: ${colors[type].bg}; color: ${colors[type].color};
                           padding: 15px; border-radius: 5px;">
                    ${message}
                </div>
            `;
        }

        // Initialize when DOM is ready
        document.addEventListener("DOMContentLoaded", function() {
            console.log("DOM loaded, initializing Square...");
            initializeSquare();
        });
        </script>';

        return $html;
    }

    /**
     * Generate card payment form HTML
     */
    public function generateCardForm($amount, $currency = 'USD', $options = []) {
        $formId = $options['form_id'] ?? 'square-card-form';
        $containerId = $options['container_id'] ?? 'card-container';

        $html = '
        <div id="' . $containerId . '" style="margin-bottom: 20px;">
            <!-- Square Web Payments SDK will inject card form here -->
        </div>

        <div id="payment-status-message" style="margin-top: 15px;"></div>

        <script>
        // Square Web Payments SDK Configuration
        const squareConfig = ' . json_encode($this->getWebPaymentsConfig()) . ';
        const paymentAmount = ' . json_encode($amount) . ';
        const paymentCurrency = "' . $currency . '";

        let payments;
        let card;

        // Initialize Square Web Payments SDK
        async function initializeSquarePayments() {
            try {
                if (!window.Square) {
                    throw new Error("Square.js failed to load");
                }

                payments = window.Square.payments(squareConfig.applicationId, squareConfig.locationId);

                // Initialize card payment method
                card = await payments.card({
                    style: squareConfig.style
                });

                await card.attach("#' . $containerId . '");
                console.log("Square card form initialized");

                // Enable payment button
                const payButton = document.getElementById("square-pay-button");
                if (payButton) {
                    payButton.disabled = false;
                    payButton.textContent = "💳 Pay $" + paymentAmount;
                }

            } catch (error) {
                console.error("Failed to initialize Square payments:", error);
                showPaymentMessage("Payment form failed to load. Please refresh and try again.", "error");
            }
        }

        // Handle card payment
        async function handleCardPayment() {
            try {
                showPaymentMessage("Processing payment...", "info");

                const result = await card.tokenize();
                if (result.status === "OK") {
                    console.log("Card tokenized successfully:", result.token);

                    // Submit payment to backend
                    await submitPayment(result.token, "card");
                } else {
                    console.error("Card tokenization failed:", result.errors);
                    showPaymentMessage("Card payment failed. Please check your card information.", "error");
                }
            } catch (error) {
                console.error("Card payment error:", error);
                showPaymentMessage("Payment processing error. Please try again.", "error");
            }
        }

        // Submit payment to backend
        async function submitPayment(token, paymentMethod) {
            try {
                const formData = new FormData();
                formData.append("payment_token", token);
                formData.append("payment_method", paymentMethod);
                formData.append("amount", paymentAmount);
                formData.append("currency", paymentCurrency);

                // Add form data if available
                const emailField = document.getElementById("customer-email");
                if (emailField) formData.append("email", emailField.value);

                const serviceField = document.getElementById("service-type");
                if (serviceField) formData.append("service", serviceField.value);

                const descriptionField = document.getElementById("payment-description");
                if (descriptionField) formData.append("description", descriptionField.value);

                const response = await fetch("square-process-payment.php", {
                    method: "POST",
                    body: formData
                });

                const result = await response.json();

                if (result.success) {
                    showPaymentMessage("Payment successful! Redirecting...", "success");
                    setTimeout(() => {
                        window.location.href = "payment-success.php?payment_id=" + result.payment_id;
                    }, 2000);
                } else {
                    showPaymentMessage("Payment failed: " + result.error, "error");
                }

            } catch (error) {
                console.error("Payment submission error:", error);
                showPaymentMessage("Payment processing failed. Please try again.", "error");
            }
        }

        // Show payment status message
        function showPaymentMessage(message, type) {
            const statusDiv = document.getElementById("payment-status-message");
            const colors = {
                info: { bg: "#d1ecf1", color: "#0c5460" },
                success: { bg: "#d4edda", color: "#155724" },
                error: { bg: "#f8d7da", color: "#721c24" }
            };

            statusDiv.innerHTML = `
                <div style="background: ${colors[type].bg}; color: ${colors[type].color};
                           padding: 15px; border-radius: 5px; margin-top: 15px;">
                    ${message}
                </div>
            `;
        }

        // Initialize when DOM is ready
        document.addEventListener("DOMContentLoaded", initializeSquarePayments);
        </script>';

        return $html;
    }

    /**
     * Generate digital wallet buttons (Apple Pay, Google Pay)
     */
    public function generateDigitalWalletButtons($amount, $currency = 'USD', $options = []) {
        $applePayId = $options['apple_pay_id'] ?? 'apple-pay-button';
        $googlePayId = $options['google_pay_id'] ?? 'google-pay-button';

        $html = '
        <div id="digital-wallets" style="margin-bottom: 20px;">
            <div id="' . $applePayId . '" style="margin-bottom: 10px;"></div>
            <div id="' . $googlePayId . '" style="margin-bottom: 10px;"></div>
        </div>

        <script>
        let applePay;
        let googlePay;

        // Initialize digital wallets
        async function initializeDigitalWallets() {
            try {
                if (!payments) {
                    console.log("Waiting for Square payments to initialize...");
                    setTimeout(initializeDigitalWallets, 1000);
                    return;
                }

                const paymentRequest = {
                    countryCode: "US",
                    currencyCode: "' . $currency . '",
                    total: {
                        amount: "' . $amount . '",
                        label: "ELOH Processing Payment"
                    }
                };

                // Initialize Apple Pay
                try {
                    applePay = await payments.applePay(paymentRequest);

                    const applePayButton = document.getElementById("' . $applePayId . '");
                    applePayButton.innerHTML = `
                        <button onclick="handleApplePayment()"
                                style="width: 100%; padding: 12px; background: #000; color: white;
                                       border: none; border-radius: 5px; font-size: 1em; cursor: pointer;">
                            🍎 Pay with Apple Pay
                        </button>
                    `;
                    console.log("Apple Pay initialized");
                } catch (error) {
                    console.log("Apple Pay not available:", error.message);
                    document.getElementById("' . $applePayId . '").style.display = "none";
                }

                // Initialize Google Pay
                try {
                    googlePay = await payments.googlePay(paymentRequest);

                    const googlePayButton = document.getElementById("' . $googlePayId . '");
                    googlePayButton.innerHTML = `
                        <button onclick="handleGooglePayment()"
                                style="width: 100%; padding: 12px; background: #4285f4; color: white;
                                       border: none; border-radius: 5px; font-size: 1em; cursor: pointer;">
                            🤖 Pay with Google Pay
                        </button>
                    `;
                    console.log("Google Pay initialized");
                } catch (error) {
                    console.log("Google Pay not available:", error.message);
                    document.getElementById("' . $googlePayId . '").style.display = "none";
                }

            } catch (error) {
                console.error("Digital wallet initialization error:", error);
            }
        }

        // Handle Apple Pay
        async function handleApplePayment() {
            try {
                showPaymentMessage("Processing Apple Pay...", "info");

                const result = await applePay.tokenize();
                if (result.status === "OK") {
                    console.log("Apple Pay tokenized successfully:", result.token);
                    await submitPayment(result.token, "apple_pay");
                } else {
                    console.error("Apple Pay failed:", result.errors);
                    showPaymentMessage("Apple Pay failed. Please try again.", "error");
                }
            } catch (error) {
                console.error("Apple Pay error:", error);
                showPaymentMessage("Apple Pay processing error. Please try again.", "error");
            }
        }

        // Handle Google Pay
        async function handleGooglePayment() {
            try {
                showPaymentMessage("Processing Google Pay...", "info");

                const result = await googlePay.tokenize();
                if (result.status === "OK") {
                    console.log("Google Pay tokenized successfully:", result.token);
                    await submitPayment(result.token, "google_pay");
                } else {
                    console.error("Google Pay failed:", result.errors);
                    showPaymentMessage("Google Pay failed. Please try again.", "error");
                }
            } catch (error) {
                console.error("Google Pay error:", error);
                showPaymentMessage("Google Pay processing error. Please try again.", "error");
            }
        }

        // Initialize digital wallets after a short delay
        setTimeout(initializeDigitalWallets, 1500);
        </script>';

        return $html;
    }

    /**
     * Generate complete payment form with card and digital wallets
     */
    public function generateCompletePaymentForm($amount, $currency = 'USD', $options = []) {
        $showCustomerFields = $options['show_customer_fields'] ?? true;
        $showServiceFields = $options['show_service_fields'] ?? true;
        $paymentType = $options['payment_type'] ?? 'service';

        $html = '
        <!-- Square Web Payments SDK -->
        <script type="text/javascript" src="https://sandbox.web.squarecdn.com/v1/square.js"></script>

        <div id="square-payment-form" style="max-width: 500px; margin: 0 auto;">
            <h3>💳 Payment Information</h3>';

        if ($showCustomerFields) {
            $html .= '
            <div style="margin-bottom: 20px;">
                <label for="customer-email" style="display: block; margin-bottom: 5px; font-weight: bold;">Email Address:</label>
                <input type="email" id="customer-email" required
                       style="width: 100%; padding: 10px; border: 1px solid #ccc; border-radius: 5px;"
                       placeholder="<EMAIL>">
            </div>';
        }

        if ($showServiceFields && $paymentType !== 'donation') {
            $html .= '
            <div style="margin-bottom: 20px;">
                <label for="service-type" style="display: block; margin-bottom: 5px; font-weight: bold;">Service Type:</label>
                <select id="service-type" required
                        style="width: 100%; padding: 10px; border: 1px solid #ccc; border-radius: 5px;">
                    <option value="consulting">Crypto & Forex Consulting ($150/hour)</option>
                    <option value="mining-pool">Mining Pool Membership ($200/year)</option>
                    <option value="mining-services">Mining Services ($500/month)</option>
                    <option value="analysis">Market Analysis Report ($99/report)</option>
                    <option value="other">Other Services</option>
                </select>
            </div>';
        }

        $html .= '
            <div style="margin-bottom: 20px;">
                <label for="payment-description" style="display: block; margin-bottom: 5px; font-weight: bold;">Description/Notes:</label>
                <textarea id="payment-description" rows="3"
                          style="width: 100%; padding: 10px; border: 1px solid #ccc; border-radius: 5px;"
                          placeholder="' . ($paymentType === 'donation' ? 'Optional message with your donation' : 'Describe the service you are paying for') . '"></textarea>
            </div>

            <h4>Choose Payment Method:</h4>

            <!-- Digital Wallet Buttons -->
            ' . $this->generateDigitalWalletButtons($amount, $currency) . '

            <div style="text-align: center; margin: 20px 0; color: #666;">
                <span style="background: white; padding: 0 10px;">OR</span>
                <hr style="margin-top: -12px; border: 1px solid #ddd;">
            </div>

            <!-- Card Payment Form -->
            <h4>Pay with Credit/Debit Card:</h4>
            ' . $this->generateCardForm($amount, $currency) . '

            <button id="square-pay-button" type="button" disabled
                    style="width: 100%; padding: 15px; background: #0077cc; color: white;
                           border: none; border-radius: 5px; font-size: 1.1em; cursor: pointer; margin-top: 20px;"
                    onclick="handleCardPayment()">
                🔒 Processing...
            </button>

            <!-- Security Information -->
            <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin-top: 20px; font-size: 0.9em; color: #666;">
                <div style="display: flex; align-items: center; margin-bottom: 10px;">
                    <span style="font-size: 1.2em; margin-right: 8px;">🔒</span>
                    <strong>Secure Payment Processing</strong>
                </div>
                <ul style="margin: 0; padding-left: 20px;">
                    <li>256-bit SSL encryption</li>
                    <li>PCI DSS compliant</li>
                    <li>No card data stored on our servers</li>
                    <li>Powered by Square</li>
                </ul>
            </div>
        </div>';

        return $html;
    }

    /**
     * Log messages
     */
    private function log($message, $level = 'info') {
        if (!$this->config['logging']['enabled']) {
            return;
        }

        $timestamp = date('Y-m-d H:i:s');
        $logMessage = "[$timestamp] [$level] $message" . PHP_EOL;

        // In a real implementation, you'd write to a log file
        if ($this->config['development']['debug_mode']) {
            error_log("Square Gateway: $logMessage");
        }
    }
}
?>
