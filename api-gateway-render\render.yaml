services:
  # Main API Service
  - type: web
    name: eloh-payment-gateway-api
    env: python
    plan: starter
    buildCommand: |
      pip install --upgrade pip
      pip install -r requirements.txt
      python setup_database.py --sample-data
    startCommand: uvicorn main:app --host 0.0.0.0 --port $PORT --workers 1
    envVars:
      - key: APP_NAME
        value: ELOH Processing Payment Gateway
      - key: ENVIRONMENT
        value: production
      - key: DEBUG
        value: false
      - key: API_HOST
        value: 0.0.0.0
      - key: SECRET_KEY
        generateValue: true
      - key: DATABASE_URL
        fromDatabase:
          name: eloh-gateway-db
          property: connectionString
      - key: LOG_LEVEL
        value: INFO
      - key: LOG_FORMAT
        value: json
      - key: RATE_LIMIT_ENABLED
        value: true
      - key: RATE_LIMIT_REQUESTS_PER_MINUTE
        value: 100
      - key: ENABLE_METRICS
        value: true
      - key: CACHE_ENABLED
        value: true
      - key: CACHE_TTL
        value: 300
      # Gateway credentials (set these manually in Render dashboard)
      - key: BTCPAY_SERVER_URL
        sync: false
      - key: BTCPAY_API_KEY
        sync: false
      - key: BTCPAY_STORE_ID
        sync: false
      - key: BTCPAY_WEBHOOK_SECRET
        sync: false
      - key: NOWPAYMENTS_API_KEY
        sync: false
      - key: NOWPAYMENTS_IPN_SECRET
        sync: false
      - key: NOWPAYMENTS_ENVIRONMENT
        value: production
      # Optional: Stripe (limited in Dominica)
      - key: STRIPE_SECRET_KEY
        sync: false
      - key: STRIPE_PUBLISHABLE_KEY
        sync: false
      - key: STRIPE_WEBHOOK_SECRET
        sync: false
      # Optional: Square (not available in Dominica)
      - key: SQUARE_ACCESS_TOKEN
        sync: false
      - key: SQUARE_APPLICATION_ID
        sync: false
    healthCheckPath: /health
    domains:
      - api.elohprocessing.com

databases:
  - name: eloh-gateway-db
    databaseName: eloh_gateway
    user: eloh_user
    plan: starter
