<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ELOH Processing - Icon Generator</title>
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .container {
            background: white;
            border-radius: 16px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            text-align: center;
            max-width: 600px;
        }
        
        h1 {
            color: #667eea;
            margin-bottom: 20px;
        }
        
        .icon-preview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .icon-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 10px;
        }
        
        .icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 40px;
            color: white;
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
        }
        
        .icon-label {
            font-size: 12px;
            color: #666;
            font-weight: 500;
        }
        
        .download-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            margin: 10px;
            transition: transform 0.2s ease;
        }
        
        .download-btn:hover {
            transform: translateY(-2px);
        }
        
        canvas {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>⚡ ELOH Processing PWA Icons</h1>
        <p>Generate PWA icons for the ELOH Processing application</p>
        
        <div class="icon-preview">
            <div class="icon-item">
                <div class="icon">⚡</div>
                <div class="icon-label">192x192</div>
                <button class="download-btn" onclick="generateIcon(192)">Download</button>
            </div>
            
            <div class="icon-item">
                <div class="icon">⚡</div>
                <div class="icon-label">512x512</div>
                <button class="download-btn" onclick="generateIcon(512)">Download</button>
            </div>
            
            <div class="icon-item">
                <div class="icon">⚡</div>
                <div class="icon-label">144x144</div>
                <button class="download-btn" onclick="generateIcon(144)">Download</button>
            </div>
            
            <div class="icon-item">
                <div class="icon">⚡</div>
                <div class="icon-label">96x96</div>
                <button class="download-btn" onclick="generateIcon(96)">Download</button>
            </div>
        </div>
        
        <button class="download-btn" onclick="generateAllIcons()" style="font-size: 16px; padding: 16px 32px;">
            📦 Generate All Icons
        </button>
        
        <p style="margin-top: 30px; color: #666; font-size: 14px;">
            These icons will be used for the PWA installation and app shortcuts.
        </p>
    </div>
    
    <canvas id="iconCanvas"></canvas>
    
    <script>
        function generateIcon(size) {
            const canvas = document.getElementById('iconCanvas');
            const ctx = canvas.getContext('2d');
            
            // Set canvas size
            canvas.width = size;
            canvas.height = size;
            
            // Create gradient background
            const gradient = ctx.createLinearGradient(0, 0, size, size);
            gradient.addColorStop(0, '#667eea');
            gradient.addColorStop(1, '#764ba2');
            
            // Draw background with rounded corners
            const radius = size * 0.2;
            ctx.fillStyle = gradient;
            roundRect(ctx, 0, 0, size, size, radius);
            ctx.fill();
            
            // Draw lightning bolt emoji
            ctx.fillStyle = 'white';
            ctx.font = `${size * 0.5}px Arial`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText('⚡', size / 2, size / 2);
            
            // Download the icon
            const link = document.createElement('a');
            link.download = `icon-${size}x${size}.png`;
            link.href = canvas.toDataURL();
            link.click();
        }
        
        function generateAllIcons() {
            const sizes = [16, 32, 72, 96, 128, 144, 152, 192, 384, 512];
            
            sizes.forEach((size, index) => {
                setTimeout(() => {
                    generateIcon(size);
                }, index * 200); // Stagger downloads
            });
        }
        
        function roundRect(ctx, x, y, width, height, radius) {
            ctx.beginPath();
            ctx.moveTo(x + radius, y);
            ctx.lineTo(x + width - radius, y);
            ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
            ctx.lineTo(x + width, y + height - radius);
            ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
            ctx.lineTo(x + radius, y + height);
            ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
            ctx.lineTo(x, y + radius);
            ctx.quadraticCurveTo(x, y, x + radius, y);
            ctx.closePath();
        }
        
        // Generate a favicon
        function generateFavicon() {
            generateIcon(32);
        }
    </script>
</body>
</html>
