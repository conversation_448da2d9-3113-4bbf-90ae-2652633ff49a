#!/usr/bin/env python3
"""
Local Testing and Debugging Script for ELOH Processing Payment Gateway

This script helps test the application locally before deploying to Render.
"""

import os
import sys
import asyncio
import subprocess
from pathlib import Path

# Add the current directory to Python path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def print_status(message, status="INFO"):
    colors = {
        "INFO": "\033[94m",
        "SUCCESS": "\033[92m", 
        "WARNING": "\033[93m",
        "ERROR": "\033[91m",
        "RESET": "\033[0m"
    }
    print(f"{colors.get(status, '')}{status}: {message}{colors['RESET']}")

def check_file_exists(file_path):
    """Check if a file exists and print status"""
    if Path(file_path).exists():
        print_status(f"✅ {file_path} exists", "SUCCESS")
        return True
    else:
        print_status(f"❌ {file_path} missing", "ERROR")
        return False

def check_python_version():
    """Check Python version"""
    version = sys.version_info
    if version.major == 3 and version.minor >= 11:
        print_status(f"✅ Python {version.major}.{version.minor}.{version.micro} (compatible)", "SUCCESS")
        return True
    else:
        print_status(f"⚠️ Python {version.major}.{version.minor}.{version.micro} (recommend 3.11+)", "WARNING")
        return True

def test_imports():
    """Test critical imports"""
    try:
        print_status("Testing imports...", "INFO")
        
        # Test FastAPI
        import fastapi
        print_status(f"✅ FastAPI {fastapi.__version__}", "SUCCESS")
        
        # Test SQLAlchemy
        import sqlalchemy
        print_status(f"✅ SQLAlchemy {sqlalchemy.__version__}", "SUCCESS")
        
        # Test our app imports
        from app.core.config import get_settings
        print_status("✅ App config import successful", "SUCCESS")
        
        from app.database.connection import get_database_manager
        print_status("✅ Database connection import successful", "SUCCESS")
        
        from app.core.adapter_registry import get_adapter_registry
        print_status("✅ Adapter registry import successful", "SUCCESS")
        
        return True
        
    except ImportError as e:
        print_status(f"❌ Import error: {e}", "ERROR")
        return False

def test_database():
    """Test database connection"""
    try:
        print_status("Testing database...", "INFO")
        
        from app.database.connection import get_database_manager
        db_manager = get_database_manager()
        
        # Test health check
        if db_manager.health_check():
            print_status("✅ Database connection healthy", "SUCCESS")
            return True
        else:
            print_status("❌ Database connection failed", "ERROR")
            return False
            
    except Exception as e:
        print_status(f"❌ Database test error: {e}", "ERROR")
        return False

def test_gateway_registry():
    """Test gateway registry"""
    try:
        print_status("Testing gateway registry...", "INFO")
        
        from app.core.adapter_registry import get_adapter_registry
        registry = get_adapter_registry()
        
        gateways = registry.list_gateways()
        print_status(f"✅ Available gateways: {gateways}", "SUCCESS")
        
        # Test each gateway info
        for gateway in gateways:
            info = registry.get_gateway_info(gateway)
            print_status(f"  - {gateway}: {info['name']}", "INFO")
        
        return True
        
    except Exception as e:
        print_status(f"❌ Gateway registry error: {e}", "ERROR")
        return False

def test_configuration():
    """Test configuration"""
    try:
        print_status("Testing configuration...", "INFO")
        
        from app.core.config import get_settings
        settings = get_settings()
        
        print_status(f"✅ App: {settings.app_name}", "SUCCESS")
        print_status(f"✅ Environment: {settings.environment}", "SUCCESS")
        print_status(f"✅ Debug: {settings.debug}", "SUCCESS")
        
        # Check configured gateways
        configured = settings.get_configured_gateways()
        print_status(f"✅ Configured gateways: {configured}", "SUCCESS")
        
        return True
        
    except Exception as e:
        print_status(f"❌ Configuration error: {e}", "ERROR")
        return False

async def test_tenant_service():
    """Test tenant service"""
    try:
        print_status("Testing tenant service...", "INFO")
        
        from app.services.tenant_service import get_tenant_service
        tenant_service = get_tenant_service()
        
        print_status("✅ Tenant service initialized", "SUCCESS")
        return True
        
    except Exception as e:
        print_status(f"❌ Tenant service error: {e}", "ERROR")
        return False

async def test_gateway_availability():
    """Test gateway availability service"""
    try:
        print_status("Testing gateway availability...", "INFO")
        
        from app.services.gateway_availability_service import get_gateway_availability_service
        availability_service = get_gateway_availability_service()
        
        available_gateways = availability_service.get_available_gateways()
        
        print_status("✅ Gateway availability check:", "SUCCESS")
        for gw in available_gateways:
            status_icon = "✅" if gw.status.value == "available" else "⚠️" if gw.status.value == "limited" else "❌"
            print_status(f"  {status_icon} {gw.name}: {gw.message}", "INFO")
        
        return True
        
    except Exception as e:
        print_status(f"❌ Gateway availability error: {e}", "ERROR")
        return False

def test_fastapi_app():
    """Test FastAPI app creation"""
    try:
        print_status("Testing FastAPI app...", "INFO")
        
        from main import app
        print_status("✅ FastAPI app created successfully", "SUCCESS")
        
        # Test with TestClient
        from fastapi.testclient import TestClient
        client = TestClient(app)
        
        # Test health endpoint
        response = client.get("/health")
        if response.status_code == 200:
            print_status("✅ Health endpoint working", "SUCCESS")
            health_data = response.json()
            print_status(f"  Status: {health_data.get('status')}", "INFO")
        else:
            print_status(f"❌ Health endpoint failed: {response.status_code}", "ERROR")
            return False
        
        # Test docs endpoint
        response = client.get("/docs")
        if response.status_code == 200:
            print_status("✅ API docs endpoint working", "SUCCESS")
        else:
            print_status(f"⚠️ API docs endpoint issue: {response.status_code}", "WARNING")
        
        return True
        
    except Exception as e:
        print_status(f"❌ FastAPI app error: {e}", "ERROR")
        return False

async def run_all_tests():
    """Run all tests"""
    print("🧪 ELOH Processing Payment Gateway - Local Testing")
    print("=" * 60)
    
    # Check current directory
    current_dir = Path.cwd()
    print_status(f"Current directory: {current_dir}", "INFO")
    
    # Check if we're in the right directory
    if not (current_dir / "main.py").exists():
        print_status("❌ Not in api-gateway directory! Please run from api-gateway folder", "ERROR")
        return False
    
    tests_passed = 0
    total_tests = 0
    
    # File existence checks
    print_status("\n📁 Checking required files...", "INFO")
    required_files = [
        "main.py", "requirements.txt", "setup_database.py", 
        "render.yaml", "Dockerfile", "build.sh"
    ]
    
    for file in required_files:
        total_tests += 1
        if check_file_exists(file):
            tests_passed += 1
    
    # Python version check
    print_status("\n🐍 Checking Python version...", "INFO")
    total_tests += 1
    if check_python_version():
        tests_passed += 1
    
    # Import tests
    print_status("\n📦 Testing imports...", "INFO")
    total_tests += 1
    if test_imports():
        tests_passed += 1
    
    # Configuration test
    print_status("\n⚙️ Testing configuration...", "INFO")
    total_tests += 1
    if test_configuration():
        tests_passed += 1
    
    # Database test
    print_status("\n🗄️ Testing database...", "INFO")
    total_tests += 1
    if test_database():
        tests_passed += 1
    
    # Gateway registry test
    print_status("\n🌐 Testing gateway registry...", "INFO")
    total_tests += 1
    if test_gateway_registry():
        tests_passed += 1
    
    # Tenant service test
    print_status("\n🏢 Testing tenant service...", "INFO")
    total_tests += 1
    if await test_tenant_service():
        tests_passed += 1
    
    # Gateway availability test
    print_status("\n📍 Testing gateway availability...", "INFO")
    total_tests += 1
    if await test_gateway_availability():
        tests_passed += 1
    
    # FastAPI app test
    print_status("\n🚀 Testing FastAPI app...", "INFO")
    total_tests += 1
    if test_fastapi_app():
        tests_passed += 1
    
    # Results
    print("\n" + "=" * 60)
    print_status(f"Tests completed: {tests_passed}/{total_tests} passed", 
                "SUCCESS" if tests_passed == total_tests else "WARNING")
    
    if tests_passed == total_tests:
        print_status("🎉 All tests passed! Ready for deployment!", "SUCCESS")
        print_status("\n🚀 Next steps:", "INFO")
        print_status("1. Push to GitHub: git push origin main", "INFO")
        print_status("2. Deploy to Render: Follow RENDER_QUICK_START.md", "INFO")
        print_status("3. Set environment variables in Render dashboard", "INFO")
    else:
        print_status("⚠️ Some tests failed. Please fix issues before deploying.", "WARNING")
    
    return tests_passed == total_tests

if __name__ == "__main__":
    asyncio.run(run_all_tests())
