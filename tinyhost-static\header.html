<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>ELOH Processing LLC - Sustainable Crypto Mining</title>
  
  <!-- PWA Meta Tags -->
  <meta name="description" content="Sustainable cryptocurrency mining and financial services powered by renewable energy. Professional crypto consulting, mining pool membership, and investment opportunities.">
  <meta name="keywords" content="cryptocurrency mining, bitcoin, renewable energy, crypto consulting, mining pool, investment, sustainable mining">
  <meta name="author" content="ELOH Processing LLC">
  <meta name="robots" content="index, follow">
  
  <!-- PWA Manifest -->
  <link rel="manifest" href="/manifest.json">
  
  <!-- PWA Theme Colors -->
  <meta name="theme-color" content="#764ba2">
  <meta name="msapplication-TileColor" content="#667eea">
  
  <!-- Apple PWA Meta Tags -->
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="default">
  <meta name="apple-mobile-web-app-title" content="ELOH Processing">
  <link rel="apple-touch-icon" href="/assets/icons/icon-192x192.png">
  
  <!-- Favicon -->
  <link rel="icon" type="image/png" sizes="32x32" href="/assets/icons/icon-32x32.png">
  <link rel="shortcut icon" href="/favicon.ico">
  
  <!-- Open Graph Meta Tags -->
  <meta property="og:title" content="ELOH Processing LLC - Sustainable Crypto Mining">
  <meta property="og:description" content="Sustainable cryptocurrency mining and financial services powered by renewable energy">
  <meta property="og:image" content="/assets/icons/icon-512x512.png">
  <meta property="og:url" content="https://your-tinyhost-domain.com">
  <meta property="og:type" content="website">
  
  <!-- Twitter Card Meta Tags -->
  <meta name="twitter:card" content="summary_large_image">
  <meta name="twitter:title" content="ELOH Processing LLC - Sustainable Crypto Mining">
  <meta name="twitter:description" content="Sustainable cryptocurrency mining and financial services powered by renewable energy">
  <meta name="twitter:image" content="/assets/icons/icon-512x512.png">

  <!-- Google Fonts for Modern Typography -->
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=JetBrains+Mono:wght@400;500;600&display=swap" rel="stylesheet">
  
  <!-- PWA Application Script -->
  <script src="/assets/js/pwa-app.js" defer></script>
  
  <!-- Static Site JavaScript -->
  <script src="/assets/js/static-site.js" defer></script>

  <style>
    /* 🎨 FUTURISTIC DESIGN SYSTEM - ELOH Processing */

    /* CSS Variables for Light Theme */
    :root {
      /* Primary Brand Colors */
      --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      --primary-color: #667eea;
      --primary-dark: #5a67d8;
      --accent-color: #764ba2;
      --accent-light: #9f7aea;

      /* Neutral Colors */
      --background-color: #fafbfc;
      --surface-color: #ffffff;
      --text-primary: #1a202c;
      --text-secondary: #4a5568;
      --text-muted: #718096;
      --border-color: #e2e8f0;
      --border-light: #f7fafc;

      /* Interactive States */
      --hover-bg: #f7fafc;
      --active-bg: #edf2f7;
      --focus-ring: rgba(102, 126, 234, 0.3);

      /* Shadows & Effects */
      --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
      --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
      --shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.1);
      --shadow-xl: 0 20px 40px rgba(0, 0, 0, 0.1);
      --glow: 0 0 20px rgba(102, 126, 234, 0.3);

      /* Typography */
      --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
      --font-mono: 'JetBrains Mono', 'Fira Code', monospace;

      /* Spacing Scale */
      --space-xs: 0.25rem;
      --space-sm: 0.5rem;
      --space-md: 1rem;
      --space-lg: 1.5rem;
      --space-xl: 2rem;
      --space-2xl: 3rem;
      --space-3xl: 4rem;

      /* Border Radius */
      --radius-sm: 0.375rem;
      --radius-md: 0.5rem;
      --radius-lg: 0.75rem;
      --radius-xl: 1rem;
      --radius-2xl: 1.5rem;

      /* Transitions */
      --transition-fast: 0.15s ease-out;
      --transition-base: 0.2s ease-out;
      --transition-slow: 0.3s ease-out;
    }

    /* Dark Theme Variables */
    body.dark-theme {
      --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      --primary-color: #7c3aed;
      --primary-dark: #6d28d9;
      --accent-color: #a855f7;
      --accent-light: #c084fc;

      --background-color: #0f0f23;
      --surface-color: #1a1a2e;
      --text-primary: #f7fafc;
      --text-secondary: #e2e8f0;
      --text-muted: #a0aec0;
      --border-color: #2d3748;
      --border-light: #4a5568;

      --hover-bg: #2d3748;
      --active-bg: #4a5568;
      --focus-ring: rgba(124, 58, 237, 0.4);

      --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.3);
      --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.3);
      --shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.3);
      --shadow-xl: 0 20px 40px rgba(0, 0, 0, 0.4);
      --glow: 0 0 30px rgba(124, 58, 237, 0.4);
    }

    /* 🎯 GLOBAL RESET & BASE STYLES */
    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }

    html {
      scroll-behavior: smooth;
      font-size: 16px;
    }

    body {
      background: var(--background-color);
      color: var(--text-primary);
      font-family: var(--font-primary);
      font-weight: 400;
      line-height: 1.6;
      margin: 0;
      transition: all var(--transition-slow);
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
    }

    /* Typography Scale */
    h1, h2, h3, h4, h5, h6 {
      font-weight: 700;
      line-height: 1.2;
      margin-bottom: var(--space-md);
      color: var(--text-primary);
    }

    h1 { font-size: 3.5rem; font-weight: 800; }
    h2 { font-size: 2.5rem; font-weight: 700; }
    h3 { font-size: 2rem; font-weight: 600; }
    h4 { font-size: 1.5rem; font-weight: 600; }
    h5 { font-size: 1.25rem; font-weight: 500; }
    h6 { font-size: 1rem; font-weight: 500; }

    p {
      margin-bottom: var(--space-md);
      color: var(--text-secondary);
    }

    a {
      color: var(--primary-color);
      text-decoration: none;
      transition: all var(--transition-fast);
    }

    a:hover {
      color: var(--primary-dark);
      text-decoration: underline;
    }
  </style>

  <style>
    /* 🚀 FUTURISTIC HEADER DESIGN */
    header {
      background: var(--surface-color);
      backdrop-filter: blur(20px);
      border-bottom: 1px solid var(--border-color);
      padding: var(--space-lg) var(--space-xl);
      position: sticky;
      top: 0;
      z-index: 1000;
      box-shadow: var(--shadow-sm);
      transition: all var(--transition-base);
    }

    .header-container {
      max-width: 1400px;
      margin: 0 auto;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    /* 🎯 LOGO DESIGN */
    .logo {
      display: flex;
      align-items: center;
      gap: var(--space-md);
    }

    .logo-text {
      font-size: 1.5rem;
      font-weight: 800;
      background: var(--primary-gradient);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      letter-spacing: -0.02em;
    }

    /* 🧭 NAVIGATION DESIGN */
    .nav-container {
      display: flex;
      align-items: center;
      gap: var(--space-xl);
    }

    nav ul {
      display: flex;
      list-style: none;
      gap: var(--space-lg);
      margin: 0;
      padding: 0;
    }

    nav ul li a {
      color: var(--text-secondary);
      font-weight: 500;
      font-size: 0.95rem;
      padding: var(--space-sm) var(--space-md);
      border-radius: var(--radius-md);
      transition: all var(--transition-fast);
      position: relative;
      overflow: hidden;
    }

    nav ul li a:hover {
      color: var(--primary-color);
      background: var(--hover-bg);
      transform: translateY(-1px);
      text-decoration: none;
    }

    nav ul li a.active {
      color: var(--primary-color);
      background: var(--active-bg);
      font-weight: 600;
    }

    /* 🌙 THEME TOGGLE BUTTON */
    .theme-toggle {
      background: var(--surface-color);
      border: 1px solid var(--border-color);
      color: var(--text-secondary);
      padding: var(--space-sm) var(--space-md);
      border-radius: var(--radius-lg);
      cursor: pointer;
      font-size: 0.9rem;
      font-weight: 500;
      transition: all var(--transition-fast);
      display: flex;
      align-items: center;
      gap: var(--space-xs);
      box-shadow: var(--shadow-sm);
    }

    .theme-toggle:hover {
      background: var(--hover-bg);
      border-color: var(--primary-color);
      transform: translateY(-1px);
      box-shadow: var(--shadow-md);
    }

    /* 📱 MOBILE RESPONSIVE DESIGN */
    .mobile-menu-toggle {
      display: none;
      background: none;
      border: none;
      cursor: pointer;
      padding: var(--space-sm);
      border-radius: var(--radius-md);
      transition: all var(--transition-fast);
    }

    .hamburger {
      display: flex;
      flex-direction: column;
      gap: 3px;
    }

    .hamburger span {
      width: 24px;
      height: 2px;
      background: var(--text-primary);
      border-radius: 2px;
      transition: all var(--transition-base);
    }

    @media (max-width: 768px) {
      .mobile-menu-toggle {
        display: block;
      }

      .nav-container {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: var(--surface-color);
        border-top: 1px solid var(--border-color);
        box-shadow: var(--shadow-lg);
        transform: translateY(-100%);
        opacity: 0;
        visibility: hidden;
        transition: all var(--transition-base);
        z-index: 999;
      }

      .nav-container.active {
        transform: translateY(0);
        opacity: 1;
        visibility: visible;
      }

      nav ul {
        flex-direction: column;
        padding: var(--space-lg);
        gap: var(--space-sm);
      }

      nav ul li a {
        display: block;
        padding: var(--space-md);
        border-radius: var(--radius-md);
        text-align: center;
      }

      .theme-toggle {
        margin: var(--space-md) var(--space-lg);
      }
    }
  </style>
</head>
<body>
  <header>
    <div class="header-container">
      <div class="logo">
        <div class="logo-text">⚡ ELOH Processing</div>
      </div>
      
      <button class="mobile-menu-toggle" onclick="toggleMobileMenu()">
        <div class="hamburger">
          <span></span>
          <span></span>
          <span></span>
        </div>
      </button>
      
      <div class="nav-container">
        <nav>
          <ul>
            <li><a href="index.html" class="nav-link">Home</a></li>
            <li><a href="about.html" class="nav-link">About</a></li>
            <li><a href="services.html" class="nav-link">Services</a></li>
            <li><a href="operations.html" class="nav-link">Operations</a></li>
            <li><a href="investors.html" class="nav-link">Investors</a></li>
            <li><a href="contact.html" class="nav-link">Contact</a></li>
          </ul>
        </nav>
        
        <button class="theme-toggle" onclick="toggleTheme()">
          <span class="theme-icon">🌙</span>
          <span class="theme-text">Dark</span>
        </button>
      </div>
    </div>
  </header>
