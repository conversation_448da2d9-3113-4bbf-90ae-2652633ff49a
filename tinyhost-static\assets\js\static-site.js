// ELOH Processing Static Site JavaScript
class ELOHStaticSite {
  constructor() {
    this.currentTheme = localStorage.getItem('theme') || 'light';
    this.mobileMenuOpen = false;
    this.init();
  }

  init() {
    console.log('ELOH Processing Static Site: Initializing...');
    
    // Apply saved theme
    this.applyTheme(this.currentTheme);
    
    // Setup event listeners
    this.setupEventListeners();
    
    // Setup PWA install prompt
    this.setupPWAInstall();
    
    // Setup animations
    this.setupAnimations();
    
    // Setup active navigation
    this.setupActiveNavigation();
    
    console.log('ELOH Processing Static Site: Initialized successfully');
  }

  setupEventListeners() {
    // Theme toggle
    const themeToggle = document.querySelector('.theme-toggle');
    if (themeToggle) {
      themeToggle.addEventListener('click', () => this.toggleTheme());
    }

    // Mobile menu toggle
    const mobileToggle = document.querySelector('.mobile-menu-toggle');
    if (mobileToggle) {
      mobileToggle.addEventListener('click', () => this.toggleMobileMenu());
    }

    // Close mobile menu when clicking nav links
    const navLinks = document.querySelectorAll('.nav-link');
    navLinks.forEach(link => {
      link.addEventListener('click', () => {
        if (this.mobileMenuOpen) {
          this.toggleMobileMenu();
        }
      });
    });

    // Close mobile menu when clicking outside
    document.addEventListener('click', (e) => {
      const navContainer = document.querySelector('.nav-container');
      const mobileToggle = document.querySelector('.mobile-menu-toggle');
      
      if (this.mobileMenuOpen && 
          !navContainer.contains(e.target) && 
          !mobileToggle.contains(e.target)) {
        this.toggleMobileMenu();
      }
    });

    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
      anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
          target.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
          });
        }
      });
    });

    // Form submissions (for contact forms)
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
      form.addEventListener('submit', (e) => this.handleFormSubmission(e));
    });
  }

  toggleTheme() {
    this.currentTheme = this.currentTheme === 'light' ? 'dark' : 'light';
    this.applyTheme(this.currentTheme);
    localStorage.setItem('theme', this.currentTheme);
  }

  applyTheme(theme) {
    const body = document.body;
    const themeIcon = document.querySelector('.theme-icon');
    const themeText = document.querySelector('.theme-text');

    if (theme === 'dark') {
      body.classList.add('dark-theme');
      if (themeIcon) themeIcon.textContent = '☀️';
      if (themeText) themeText.textContent = 'Light';
    } else {
      body.classList.remove('dark-theme');
      if (themeIcon) themeIcon.textContent = '🌙';
      if (themeText) themeText.textContent = 'Dark';
    }
  }

  toggleMobileMenu() {
    const navContainer = document.querySelector('.nav-container');
    const hamburger = document.querySelector('.hamburger');
    
    this.mobileMenuOpen = !this.mobileMenuOpen;
    
    if (navContainer) {
      navContainer.classList.toggle('active', this.mobileMenuOpen);
    }
    
    if (hamburger) {
      hamburger.classList.toggle('active', this.mobileMenuOpen);
    }

    // Prevent body scroll when menu is open
    document.body.style.overflow = this.mobileMenuOpen ? 'hidden' : '';
  }

  setupPWAInstall() {
    // PWA install banner
    const installBanner = document.getElementById('pwa-install-banner');
    const installBtn = document.getElementById('install-pwa-btn');
    const dismissBtn = document.getElementById('dismiss-banner');

    // Show install banner if PWA is installable
    if ('serviceWorker' in navigator && installBanner) {
      // Check if app is already installed
      if (!window.matchMedia('(display-mode: standalone)').matches) {
        // Show banner after a delay
        setTimeout(() => {
          if (!localStorage.getItem('pwa-install-dismissed')) {
            installBanner.style.display = 'block';
          }
        }, 3000);
      }
    }

    // Install button click
    if (installBtn) {
      installBtn.addEventListener('click', () => {
        if (window.deferredPrompt) {
          window.deferredPrompt.prompt();
          window.deferredPrompt.userChoice.then((choiceResult) => {
            if (choiceResult.outcome === 'accepted') {
              console.log('User accepted the install prompt');
            }
            window.deferredPrompt = null;
            installBanner.style.display = 'none';
          });
        } else {
          // Fallback for browsers that don't support install prompt
          this.showInstallInstructions();
        }
      });
    }

    // Dismiss button click
    if (dismissBtn) {
      dismissBtn.addEventListener('click', () => {
        installBanner.style.display = 'none';
        localStorage.setItem('pwa-install-dismissed', 'true');
      });
    }

    // Listen for beforeinstallprompt event
    window.addEventListener('beforeinstallprompt', (e) => {
      e.preventDefault();
      window.deferredPrompt = e;
    });

    // Listen for app installed event
    window.addEventListener('appinstalled', () => {
      console.log('PWA was installed');
      if (installBanner) {
        installBanner.style.display = 'none';
      }
    });
  }

  showInstallInstructions() {
    const userAgent = navigator.userAgent.toLowerCase();
    let instructions = '';

    if (userAgent.includes('iphone') || userAgent.includes('ipad')) {
      instructions = 'To install this app on your iOS device:\n\n1. Tap the Share button\n2. Scroll down and tap "Add to Home Screen"\n3. Tap "Add" to confirm';
    } else if (userAgent.includes('android')) {
      instructions = 'To install this app on your Android device:\n\n1. Tap the menu button (⋮)\n2. Tap "Add to Home screen"\n3. Tap "Add" to confirm';
    } else {
      instructions = 'To install this app:\n\n1. Look for the install icon in your browser\'s address bar\n2. Click it and follow the prompts\n3. Or check your browser\'s menu for "Install" or "Add to Home Screen"';
    }

    alert(instructions);
  }

  setupAnimations() {
    // Intersection Observer for scroll animations
    const observerOptions = {
      threshold: 0.1,
      rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.classList.add('animate-in');
        }
      });
    }, observerOptions);

    // Observe elements for animation
    const animateElements = document.querySelectorAll('.stat-card, .service-card, .investment-card');
    animateElements.forEach(el => {
      observer.observe(el);
    });

    // Add CSS for animations
    const style = document.createElement('style');
    style.textContent = `
      .stat-card, .service-card, .investment-card {
        opacity: 0;
        transform: translateY(30px);
        transition: all 0.6s ease-out;
      }
      
      .animate-in {
        opacity: 1 !important;
        transform: translateY(0) !important;
      }
      
      .mining-visualization {
        animation: pulse 3s ease-in-out infinite;
      }
      
      @keyframes pulse {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.05); }
      }
      
      .energy-particle {
        width: 8px;
        height: 8px;
        background: var(--primary-color);
        border-radius: 50%;
        position: absolute;
        animation: float-particle 4s ease-in-out infinite;
      }
      
      .energy-particle:nth-child(1) { animation-delay: 0s; top: 20%; left: 20%; }
      .energy-particle:nth-child(2) { animation-delay: 1.3s; top: 60%; left: 70%; }
      .energy-particle:nth-child(3) { animation-delay: 2.6s; top: 80%; left: 30%; }
      
      @keyframes float-particle {
        0%, 100% { transform: translateY(0px) scale(1); opacity: 0.7; }
        50% { transform: translateY(-20px) scale(1.2); opacity: 1; }
      }
      
      .rig-component {
        display: inline-block;
        font-size: 2rem;
        margin: 0.5rem;
        transition: all 0.3s ease;
      }
      
      .rig-component.active {
        animation: glow 2s ease-in-out infinite alternate;
      }
      
      @keyframes glow {
        from { filter: drop-shadow(0 0 5px var(--primary-color)); }
        to { filter: drop-shadow(0 0 15px var(--primary-color)); }
      }
    `;
    document.head.appendChild(style);
  }

  setupActiveNavigation() {
    // Set active navigation based on current page
    const currentPage = window.location.pathname.split('/').pop() || 'index.html';
    const navLinks = document.querySelectorAll('.nav-link');
    
    navLinks.forEach(link => {
      const href = link.getAttribute('href');
      if (href === currentPage || (currentPage === '' && href === 'index.html')) {
        link.classList.add('active');
      } else {
        link.classList.remove('active');
      }
    });
  }

  handleFormSubmission(e) {
    e.preventDefault();
    
    const form = e.target;
    const formData = new FormData(form);
    const data = Object.fromEntries(formData);
    
    // Show loading state
    const submitBtn = form.querySelector('button[type="submit"]');
    const originalText = submitBtn.textContent;
    submitBtn.textContent = 'Sending...';
    submitBtn.disabled = true;
    
    // Simulate form submission (replace with actual endpoint)
    setTimeout(() => {
      // Reset form
      form.reset();
      
      // Show success message
      this.showNotification('Message sent successfully! We\'ll get back to you soon.', 'success');
      
      // Reset button
      submitBtn.textContent = originalText;
      submitBtn.disabled = false;
    }, 2000);
  }

  showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
      <div class="notification-content">
        <span class="notification-icon">${type === 'success' ? '✅' : type === 'error' ? '❌' : 'ℹ️'}</span>
        <span class="notification-message">${message}</span>
        <button class="notification-close" onclick="this.parentElement.parentElement.remove()">×</button>
      </div>
    `;
    
    // Add notification styles
    const style = document.createElement('style');
    style.textContent = `
      .notification {
        position: fixed;
        top: 20px;
        right: 20px;
        background: var(--surface-color);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-lg);
        z-index: 1000;
        animation: slideInRight 0.3s ease-out;
      }
      
      .notification-success { border-left: 4px solid #10b981; }
      .notification-error { border-left: 4px solid #ef4444; }
      .notification-info { border-left: 4px solid var(--primary-color); }
      
      .notification-content {
        display: flex;
        align-items: center;
        gap: var(--space-sm);
        padding: var(--space-md);
      }
      
      .notification-close {
        background: none;
        border: none;
        cursor: pointer;
        font-size: 1.2rem;
        color: var(--text-muted);
        margin-left: auto;
      }
      
      @keyframes slideInRight {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
      }
    `;
    
    if (!document.querySelector('#notification-styles')) {
      style.id = 'notification-styles';
      document.head.appendChild(style);
    }
    
    document.body.appendChild(notification);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
      if (notification.parentElement) {
        notification.remove();
      }
    }, 5000);
  }

  // Utility methods
  scrollToTop() {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  }

  trackEvent(eventName, data = {}) {
    // Analytics tracking (replace with your analytics service)
    console.log('Event tracked:', eventName, data);
    
    // Example: Google Analytics 4
    if (typeof gtag !== 'undefined') {
      gtag('event', eventName, {
        event_category: 'Static Site',
        ...data
      });
    }
  }
}

// Global functions for inline event handlers
function toggleMobileMenu() {
  if (window.elohSite) {
    window.elohSite.toggleMobileMenu();
  }
}

function toggleTheme() {
  if (window.elohSite) {
    window.elohSite.toggleTheme();
  }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  window.elohSite = new ELOHStaticSite();
});

// Handle page visibility changes
document.addEventListener('visibilitychange', () => {
  if (!document.hidden) {
    console.log('Page became visible');
    // Refresh any time-sensitive data
  }
});

// Handle online/offline status
window.addEventListener('online', () => {
  console.log('Back online');
  if (window.elohSite) {
    window.elohSite.showNotification('Connection restored!', 'success');
  }
});

window.addEventListener('offline', () => {
  console.log('Gone offline');
  if (window.elohSite) {
    window.elohSite.showNotification('You are now offline. Some features may be limited.', 'info');
  }
});

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
  module.exports = ELOHStaticSite;
}
