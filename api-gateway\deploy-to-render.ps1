# ELOH Processing Payment Gateway - Quick Deploy to Render Script (PowerShell)

Write-Host "🚀 ELOH Processing Payment Gateway - Render Deployment" -ForegroundColor Cyan
Write-Host "=" * 60 -ForegroundColor Cyan

# Function to print colored output
function Write-Success {
    param($Message)
    Write-Host "✅ $Message" -ForegroundColor Green
}

function Write-Warning {
    param($Message)
    Write-Host "⚠️  $Message" -ForegroundColor Yellow
}

function Write-Error {
    param($Message)
    Write-Host "❌ $Message" -ForegroundColor Red
}

function Write-Info {
    param($Message)
    Write-Host "ℹ️  $Message" -ForegroundColor Blue
}

Write-Info "Preparing ELOH Processing Payment Gateway for Render deployment..."

# 1. Check if all required files exist
Write-Host ""
Write-Host "📋 Checking required files..." -ForegroundColor Cyan

$requiredFiles = @(
    "requirements.txt",
    "main.py", 
    "render.yaml",
    "build.sh",
    "Dockerfile",
    ".env.render",
    "setup_database.py"
)

foreach ($file in $requiredFiles) {
    if (Test-Path $file) {
        Write-Success "$file exists"
    } else {
        Write-Error "$file is missing"
        exit 1
    }
}

# 2. Check Python
Write-Host ""
Write-Host "🐍 Checking Python..." -ForegroundColor Cyan
try {
    $pythonVersion = python --version 2>&1
    Write-Success "Python found: $pythonVersion"
} catch {
    Write-Error "Python not found. Please install Python 3.11+"
    exit 1
}

# 3. Test local setup
Write-Host ""
Write-Host "🧪 Testing local setup..." -ForegroundColor Cyan
try {
    python -c "
import sys
sys.path.insert(0, '.')
try:
    from app.core.config import get_settings
    from app.database.connection import get_database_manager
    print('✅ Core imports successful')
except ImportError as e:
    print(f'❌ Import error: {e}')
    sys.exit(1)
"
    Write-Success "Local setup validation passed"
} catch {
    Write-Error "Local setup validation failed"
    exit 1
}

# 4. Check git status
Write-Host ""
Write-Host "📝 Checking git status..." -ForegroundColor Cyan
try {
    $gitStatus = git status --porcelain 2>&1
    if ($gitStatus) {
        Write-Warning "You have uncommitted changes"
        Write-Host "Uncommitted files:" -ForegroundColor Yellow
        git status --porcelain
        Write-Host ""
        $commit = Read-Host "Do you want to commit these changes? (y/N)"
        if ($commit -eq "y" -or $commit -eq "Y") {
            git add .
            git commit -m "Prepare for Render deployment"
            Write-Success "Changes committed"
        } else {
            Write-Warning "Proceeding with uncommitted changes"
        }
    } else {
        Write-Success "Git working directory is clean"
    }
} catch {
    Write-Warning "Git not available or not in a git repository"
}

# 5. Display deployment instructions
Write-Host ""
Write-Host "🌐 Ready for Render Deployment!" -ForegroundColor Green
Write-Host "=" * 50 -ForegroundColor Green

Write-Host ""
Write-Host "📋 Next Steps:" -ForegroundColor Cyan
Write-Host ""
Write-Host "1. 🔗 Push to GitHub:" -ForegroundColor White
Write-Host "   git push origin main" -ForegroundColor Gray
Write-Host ""
Write-Host "2. 🌐 Go to Render Dashboard:" -ForegroundColor White
Write-Host "   https://dashboard.render.com" -ForegroundColor Gray
Write-Host ""
Write-Host "3. 📊 Create PostgreSQL Database:" -ForegroundColor White
Write-Host "   - Click 'New +' → 'PostgreSQL'" -ForegroundColor Gray
Write-Host "   - Name: eloh-gateway-db" -ForegroundColor Gray
Write-Host "   - Database: eloh_gateway" -ForegroundColor Gray
Write-Host "   - User: eloh_user" -ForegroundColor Gray
Write-Host "   - Plan: Free (can upgrade later)" -ForegroundColor Gray
Write-Host ""
Write-Host "4. 🚀 Create Web Service:" -ForegroundColor White
Write-Host "   - Click 'New +' → 'Web Service'" -ForegroundColor Gray
Write-Host "   - Connect your GitHub repository" -ForegroundColor Gray
Write-Host "   - Name: eloh-payment-gateway-api" -ForegroundColor Gray
Write-Host "   - Environment: Python 3" -ForegroundColor Gray
Write-Host "   - Build Command: chmod +x build.sh && ./build.sh" -ForegroundColor Gray
Write-Host "   - Start Command: uvicorn main:app --host 0.0.0.0 --port `$PORT" -ForegroundColor Gray
Write-Host ""
Write-Host "5. ⚙️  Set Environment Variables:" -ForegroundColor White
Write-Host "   Copy from .env.render and set in Render dashboard" -ForegroundColor Gray

Write-Host ""
Write-Host "🔑 Required Environment Variables:" -ForegroundColor Yellow
Write-Host "   APP_NAME=ELOH Processing Payment Gateway" -ForegroundColor Gray
Write-Host "   ENVIRONMENT=production" -ForegroundColor Gray
Write-Host "   DEBUG=false" -ForegroundColor Gray
Write-Host "   LOG_LEVEL=INFO" -ForegroundColor Gray
Write-Host ""
Write-Host "🔐 Gateway Credentials (set these manually):" -ForegroundColor Yellow
Write-Host "   BTCPAY_SERVER_URL=https://your-btcpay-server.com" -ForegroundColor Gray
Write-Host "   BTCPAY_API_KEY=your_btcpay_api_key" -ForegroundColor Gray
Write-Host "   BTCPAY_STORE_ID=your_btcpay_store_id" -ForegroundColor Gray
Write-Host "   NOWPAYMENTS_API_KEY=your_nowpayments_api_key" -ForegroundColor Gray
Write-Host ""

Write-Host "6. 🎯 After Deployment:" -ForegroundColor White
Write-Host "   - Test: https://your-app.onrender.com/health" -ForegroundColor Gray
Write-Host "   - API Docs: https://your-app.onrender.com/docs" -ForegroundColor Gray
Write-Host "   - Portal: https://your-app.onrender.com/v1/portal" -ForegroundColor Gray
Write-Host ""

Write-Host "💡 Pro Tips:" -ForegroundColor Magenta
Write-Host "   - Use Render's auto-deploy from GitHub" -ForegroundColor Gray
Write-Host "   - Set up custom domain for production" -ForegroundColor Gray
Write-Host "   - Monitor logs in Render dashboard" -ForegroundColor Gray
Write-Host "   - Upgrade to paid plan for production use" -ForegroundColor Gray
Write-Host ""

Write-Host "🆘 Need Help?" -ForegroundColor Red
Write-Host "   - Render Docs: https://render.com/docs" -ForegroundColor Gray
Write-Host "   - Deployment Guide: ./RENDER_DEPLOYMENT.md" -ForegroundColor Gray
Write-Host "   - Support: <EMAIL>" -ForegroundColor Gray
Write-Host ""

Write-Success "Deployment preparation complete!"
Write-Info "Your ELOH Processing Payment Gateway is ready for Render! 🚀"

# Optional: Open Render dashboard
$openBrowser = Read-Host "Open Render dashboard in browser? (y/N)"
if ($openBrowser -eq "y" -or $openBrowser -eq "Y") {
    Start-Process "https://dashboard.render.com"
}

Write-Host ""
Write-Host "🎉 Happy deploying!" -ForegroundColor Green
