<?php
/**
 * Domain Update Script for TinyHost Deployment
 * Updates all domain references from InfinityFree to TinyHost
 */

// Configuration
$oldDomain = 'elohprocessing.infy.uk';
$newDomain = 'your-tinyhost-domain.com'; // <PERSON>AN<PERSON> THIS TO YOUR ACTUAL TINYHOST DOMAIN

echo "<h1>Domain Update Script</h1>";
echo "<p><strong>Old Domain:</strong> $oldDomain</p>";
echo "<p><strong>New Domain:</strong> $newDomain</p>";

if ($newDomain === 'your-tinyhost-domain.com') {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>⚠️ Configuration Required</h3>";
    echo "<p>Please edit this file and change <code>\$newDomain</code> to your actual TinyHost domain before running.</p>";
    echo "</div>";
    exit;
}

// Files to update
$filesToUpdate = [
    'includes/btcpay-config.php',
    'includes/btcpay-gateway.php',
    'includes/nowpayments-gateway.php',
    'btcpay-webhook.php',
    'nowpayments-checkout.php',
    'multi-gateway-process-payment.php'
];

$updatedFiles = [];
$errors = [];

foreach ($filesToUpdate as $file) {
    if (file_exists($file)) {
        $content = file_get_contents($file);
        $originalContent = $content;
        
        // Replace domain references
        $content = str_replace($oldDomain, $newDomain, $content);
        
        if ($content !== $originalContent) {
            if (file_put_contents($file, $content)) {
                $updatedFiles[] = $file;
                echo "<p>✅ Updated: $file</p>";
            } else {
                $errors[] = "Failed to write: $file";
                echo "<p>❌ Failed to update: $file</p>";
            }
        } else {
            echo "<p>ℹ️ No changes needed: $file</p>";
        }
    } else {
        $errors[] = "File not found: $file";
        echo "<p>⚠️ File not found: $file</p>";
    }
}

echo "<hr>";
echo "<h2>Summary</h2>";
echo "<p><strong>Files Updated:</strong> " . count($updatedFiles) . "</p>";
echo "<p><strong>Errors:</strong> " . count($errors) . "</p>";

if (!empty($updatedFiles)) {
    echo "<h3>Updated Files:</h3>";
    echo "<ul>";
    foreach ($updatedFiles as $file) {
        echo "<li>$file</li>";
    }
    echo "</ul>";
}

if (!empty($errors)) {
    echo "<h3>Errors:</h3>";
    echo "<ul>";
    foreach ($errors as $error) {
        echo "<li style='color: red;'>$error</li>";
    }
    echo "</ul>";
}

echo "<hr>";
echo "<h2>Next Steps:</h2>";
echo "<ol>";
echo "<li>Verify all domain references have been updated</li>";
echo "<li>Upload files to TinyHost</li>";
echo "<li>Update BTCPay Server webhook URL in dashboard</li>";
echo "<li>Test payment system functionality</li>";
echo "</ol>";

echo "<p><a href='nowpayments-test.php'>Test NowPayments Configuration</a> | <a href='btcpay-test.php'>Test BTCPay Configuration</a></p>";
?>
