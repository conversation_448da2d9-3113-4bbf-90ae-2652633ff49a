"""
ELOH Processing - Multi-Tenant Payment Gateway API (Render Optimized)
FastAPI-based payment gateway with regional gateway availability and tenant management
"""

from fastapi import FastAP<PERSON>, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn
import logging
import os
from datetime import datetime
from contextlib import asynccontextmanager

# Import application modules
from app.api.v1 import payments, customers, webhooks, tenants, tenant_portal
from app.core.config import get_settings
from app.core.exceptions import PaymentGatewayException, handle_payment_exception
from app.core.logging import setup_logging, get_logger

# Initialize settings and logging
settings = get_settings()
setup_logging()
logger = get_logger(__name__)

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan events"""
    # Startup
    logger.info("🚀 ELOH Processing Payment Gateway API starting up...")
    logger.info(f"Environment: {settings.environment}")
    logger.info(f"Debug mode: {settings.debug}")
    
    # Initialize database
    try:
        from app.database.connection import get_database_manager
        db_manager = get_database_manager()
        if db_manager.health_check():
            logger.info("✅ Database connection healthy")
        else:
            logger.warning("⚠️ Database connection issues detected")
    except Exception as e:
        logger.error(f"❌ Database initialization failed: {e}")
    
    # Log available gateways
    try:
        from app.core.adapter_registry import get_adapter_registry
        registry = get_adapter_registry()
        available_gateways = registry.list_gateways()
        logger.info(f"🌐 Available gateways: {available_gateways}")
    except Exception as e:
        logger.error(f"❌ Gateway registry initialization failed: {e}")
    
    # Log Render deployment info
    render_service = os.getenv("RENDER_SERVICE_NAME")
    if render_service:
        logger.info(f"🌐 Running on Render service: {render_service}")
        logger.info(f"🔗 External URL: {os.getenv('RENDER_EXTERNAL_URL', 'Not set')}")
    
    yield
    
    # Shutdown
    logger.info("👋 ELOH Processing Payment Gateway API shutting down...")

# Create FastAPI application
app = FastAPI(
    title=settings.app_name,
    description="Multi-tenant payment gateway with regional gateway availability for the Caribbean",
    version=settings.app_version,
    docs_url="/docs",
    redoc_url="/redoc",
    openapi_url="/openapi.json",
    lifespan=lifespan
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Global exception handler for payment gateway exceptions
@app.exception_handler(PaymentGatewayException)
async def payment_gateway_exception_handler(request: Request, exc: PaymentGatewayException):
    return await handle_payment_exception(request, exc)

# Health check endpoint (optimized for Render)
@app.get("/health", tags=["Health"])
async def health_check():
    """Health check endpoint for Render and monitoring"""
    # Check database health
    db_healthy = False
    try:
        from app.database.connection import get_database_manager
        db_manager = get_database_manager()
        db_healthy = db_manager.health_check()
    except Exception as e:
        logger.error(f"Database health check failed: {e}")
    
    # Check available gateways
    available_gateways = []
    try:
        from app.core.adapter_registry import get_adapter_registry
        registry = get_adapter_registry()
        available_gateways = registry.list_gateways()
    except Exception as e:
        logger.error(f"Gateway registry check failed: {e}")
    
    # Overall health status
    overall_status = "healthy" if db_healthy else "degraded"
    
    health_data = {
        "status": overall_status,
        "service": "ELOH Processing Payment Gateway",
        "version": settings.app_version,
        "environment": settings.environment,
        "database": "healthy" if db_healthy else "unhealthy",
        "gateways": {
            "available": available_gateways,
            "count": len(available_gateways)
        },
        "deployment": {
            "platform": "render" if os.getenv("RENDER_SERVICE_NAME") else "local",
            "service_name": os.getenv("RENDER_SERVICE_NAME"),
            "external_url": os.getenv("RENDER_EXTERNAL_URL")
        },
        "timestamp": datetime.now().isoformat()
    }
    
    # Return appropriate HTTP status for Render health checks
    status_code = 200 if overall_status == "healthy" else 503
    return JSONResponse(content=health_data, status_code=status_code)

# API version info
@app.get("/", tags=["Info"])
async def root():
    """API information and status"""
    return {
        "message": "ELOH Processing Payment Gateway API",
        "version": settings.app_version,
        "docs": "/docs",
        "health": "/health",
        "api_v1": "/v1",
        "tenant_portal": "/v1/portal",
        "features": {
            "multi_tenant": True,
            "regional_gateway_availability": True,
            "crypto_payments": True,
            "caribbean_optimized": True
        },
        "supported_gateways": [
            "btcpay",
            "nowpayments", 
            "stripe",
            "square"
        ],
        "deployment": {
            "platform": "render",
            "region": "global",
            "optimized_for": "Caribbean/Dominica"
        }
    }

# Include API routers
app.include_router(
    payments.router,
    prefix="/v1",
    tags=["Payments"]
)

app.include_router(
    customers.router,
    prefix="/v1",
    tags=["Customers"]
)

app.include_router(
    webhooks.router,
    prefix="/v1",
    tags=["Webhooks"]
)

app.include_router(
    tenants.router,
    prefix="/v1",
    tags=["Tenants"]
)

app.include_router(
    tenant_portal.router,
    prefix="/v1",
    tags=["Tenant Portal"]
)

# Development server (not used on Render)
if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=int(os.getenv("PORT", 8000)),
        reload=False,  # Disabled for production
        log_level="info"
    )
