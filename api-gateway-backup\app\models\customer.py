"""
Customer-related Pydantic models for the ELOH Processing Payment Gateway API
"""

from pydantic import BaseModel, Field, EmailStr, validator
from typing import Optional, Dict, Any, List
from datetime import datetime

class CustomerAddress(BaseModel):
    """Customer address model"""
    line1: str = Field(..., max_length=100, description="Address line 1")
    line2: Optional[str] = Field(None, max_length=100, description="Address line 2")
    city: str = Field(..., max_length=50, description="City")
    state: Optional[str] = Field(None, max_length=50, description="State/Province")
    postal_code: str = Field(..., max_length=20, description="Postal/ZIP code")
    country: str = Field(..., min_length=2, max_length=2, description="ISO 3166-1 alpha-2 country code")

class CustomerRequest(BaseModel):
    """Standard customer creation request model"""
    
    # Required fields
    email: EmailStr = Field(..., description="Customer email address")
    
    # Optional personal information
    first_name: Optional[str] = Field(None, max_length=50, description="First name")
    last_name: Optional[str] = Field(None, max_length=50, description="Last name")
    phone: Optional[str] = Field(None, max_length=20, description="Phone number")
    
    # Address information
    address: Optional[CustomerAddress] = Field(None, description="Customer address")
    
    # Business information
    company: Optional[str] = Field(None, max_length=100, description="Company name")
    tax_id: Optional[str] = Field(None, max_length=50, description="Tax ID/VAT number")
    
    # Gateway preferences
    preferred_gateways: Optional[List[str]] = Field(None, description="Preferred payment gateways")
    default_currency: Optional[str] = Field("USD", description="Default currency")
    
    # Metadata
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")
    
    @validator('phone')
    def validate_phone(cls, v):
        """Basic phone validation"""
        if v and not v.replace('+', '').replace('-', '').replace(' ', '').replace('(', '').replace(')', '').isdigit():
            raise ValueError('Invalid phone number format')
        return v
    
    @validator('preferred_gateways')
    def validate_gateways(cls, v):
        """Validate gateway names"""
        if v:
            valid_gateways = {'stripe', 'square', 'btcpay', 'nowpayments'}
            invalid = set(v) - valid_gateways
            if invalid:
                raise ValueError(f'Invalid gateways: {invalid}')
        return v

class CustomerResponse(BaseModel):
    """Standard customer response model"""
    
    # Customer identifiers
    customer_id: str = Field(..., description="Our internal customer ID")
    gateway_customer_ids: Dict[str, str] = Field(default_factory=dict, description="Gateway-specific customer IDs")
    
    # Personal information
    email: EmailStr = Field(..., description="Customer email address")
    first_name: Optional[str] = Field(None, description="First name")
    last_name: Optional[str] = Field(None, description="Last name")
    phone: Optional[str] = Field(None, description="Phone number")
    
    # Address information
    address: Optional[CustomerAddress] = Field(None, description="Customer address")
    
    # Business information
    company: Optional[str] = Field(None, description="Company name")
    tax_id: Optional[str] = Field(None, description="Tax ID/VAT number")
    
    # Gateway preferences
    preferred_gateways: List[str] = Field(default_factory=list, description="Preferred payment gateways")
    default_currency: str = Field("USD", description="Default currency")
    
    # Status and timestamps
    status: str = Field("active", description="Customer status")
    created_at: datetime = Field(..., description="Customer creation timestamp")
    updated_at: Optional[datetime] = Field(None, description="Last update timestamp")
    
    # Statistics
    total_payments: int = Field(0, description="Total number of payments")
    total_amount: float = Field(0.0, description="Total payment amount")
    
    # Metadata
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")

class CustomerUpdateRequest(BaseModel):
    """Customer update request model"""
    
    # Optional personal information updates
    first_name: Optional[str] = Field(None, max_length=50, description="First name")
    last_name: Optional[str] = Field(None, max_length=50, description="Last name")
    phone: Optional[str] = Field(None, max_length=20, description="Phone number")
    
    # Address information
    address: Optional[CustomerAddress] = Field(None, description="Customer address")
    
    # Business information
    company: Optional[str] = Field(None, max_length=100, description="Company name")
    tax_id: Optional[str] = Field(None, max_length=50, description="Tax ID/VAT number")
    
    # Gateway preferences
    preferred_gateways: Optional[List[str]] = Field(None, description="Preferred payment gateways")
    default_currency: Optional[str] = Field(None, description="Default currency")
    
    # Metadata
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")

class CustomerListRequest(BaseModel):
    """Request model for listing customers"""
    email: Optional[str] = Field(None, description="Filter by email")
    company: Optional[str] = Field(None, description="Filter by company")
    status: Optional[str] = Field(None, description="Filter by status")
    limit: int = Field(50, ge=1, le=100, description="Number of results to return")
    offset: int = Field(0, ge=0, description="Number of results to skip")
    start_date: Optional[datetime] = Field(None, description="Filter by creation start date")
    end_date: Optional[datetime] = Field(None, description="Filter by creation end date")

class CustomerListResponse(BaseModel):
    """Response model for listing customers"""
    customers: List[CustomerResponse] = Field(..., description="List of customers")
    total: int = Field(..., description="Total number of customers")
    limit: int = Field(..., description="Limit used")
    offset: int = Field(..., description="Offset used")
    has_more: bool = Field(..., description="Whether there are more results")
