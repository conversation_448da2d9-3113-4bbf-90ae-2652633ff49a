"""
Gateway Adapter Registry for ELOH Processing Payment Gateway API

This module provides a centralized registry for all payment gateway adapters,
enabling dynamic gateway selection and easy addition/removal of payment providers.

The registry supports both rule-based routing and future AI-driven routing systems.
"""

from typing import Dict, Type, List, Optional
import logging

from ..adapters.gateway_adapter import GatewayAdapter
from ..adapters.stripe_adapter import StripeAdapter
from ..adapters.btcpay_adapter import BTCPayAdapter
# from ..adapters.square_adapter import SquareAdapter  # To be implemented
# from ..adapters.nowpayments_adapter import NowPaymentsAdapter  # To be implemented

logger = logging.getLogger(__name__)


class AdapterRegistry:
    """
    Registry for payment gateway adapters.
    
    This class manages the mapping between gateway identifiers and their
    corresponding adapter classes. It provides methods for registering,
    retrieving, and managing gateway adapters.
    
    The registry is designed to be:
    - Thread-safe for concurrent access
    - Extensible for adding new gateways
    - AI-friendly for future intelligent routing
    """
    
    def __init__(self):
        """Initialize the adapter registry with default gateways"""
        self._adapters: Dict[str, Type[GatewayAdapter]] = {}
        self._gateway_info: Dict[str, Dict] = {}
        
        # Register default adapters
        self._register_default_adapters()
        
        logger.info(f"Adapter registry initialized with {len(self._adapters)} gateways")
    
    def _register_default_adapters(self):
        """Register the default payment gateway adapters"""
        
        # Stripe adapter
        self.register_adapter("stripe", StripeAdapter, {
            "name": "Stripe",
            "description": "Credit cards, ACH, international payments",
            "supported_currencies": ["USD", "EUR", "GBP", "CAD", "AUD", "JPY"],
            "supported_methods": ["card", "bank_transfer", "digital_wallet"],
            "features": ["subscriptions", "marketplace", "connect"],
            "regions": ["global"],
            "processing_time": "instant",
            "settlement_time": "2-7 days",
            "fees": {"card": "2.9% + 30¢", "ach": "0.8%"},
            "ai_routing_weight": 0.8,  # High preference for AI routing
            "reliability_score": 0.95
        })
        
        # BTCPay Server adapter
        self.register_adapter("btcpay", BTCPayAdapter, {
            "name": "BTCPay Server", 
            "description": "Bitcoin and Lightning Network payments",
            "supported_currencies": ["BTC", "USD", "EUR"],
            "supported_methods": ["bitcoin", "lightning"],
            "features": ["self_hosted", "no_fees", "privacy"],
            "regions": ["global"],
            "processing_time": "instant (Lightning), 10-60 min (Bitcoin)",
            "settlement_time": "instant",
            "fees": {"bitcoin": "network fees only", "lightning": "minimal"},
            "ai_routing_weight": 0.7,  # Good for crypto-focused users
            "reliability_score": 0.90
        })
        
        # Placeholder for Square (to be implemented)
        # self.register_adapter("square", SquareAdapter, {...})
        
        # Placeholder for NowPayments (to be implemented)  
        # self.register_adapter("nowpayments", NowPaymentsAdapter, {...})
    
    def register_adapter(self, gateway_id: str, adapter_class: Type[GatewayAdapter], info: Dict) -> None:
        """
        Register a new payment gateway adapter.
        
        Args:
            gateway_id: Unique identifier for the gateway (e.g., 'stripe', 'btcpay')
            adapter_class: The adapter class implementing GatewayAdapter interface
            info: Gateway information for routing decisions
        """
        if not issubclass(adapter_class, GatewayAdapter):
            raise ValueError(f"Adapter class must inherit from GatewayAdapter")
        
        self._adapters[gateway_id] = adapter_class
        self._gateway_info[gateway_id] = {
            "gateway_id": gateway_id,
            "adapter_class": adapter_class.__name__,
            **info
        }
        
        logger.info(f"Registered gateway adapter: {gateway_id} ({adapter_class.__name__})")
    
    def unregister_adapter(self, gateway_id: str) -> None:
        """
        Unregister a payment gateway adapter.
        
        Args:
            gateway_id: Gateway identifier to remove
        """
        if gateway_id in self._adapters:
            del self._adapters[gateway_id]
            del self._gateway_info[gateway_id]
            logger.info(f"Unregistered gateway adapter: {gateway_id}")
        else:
            logger.warning(f"Attempted to unregister unknown gateway: {gateway_id}")
    
    def get_adapter_class(self, gateway_id: str) -> Type[GatewayAdapter]:
        """
        Get the adapter class for a specific gateway.
        
        Args:
            gateway_id: Gateway identifier
            
        Returns:
            Type[GatewayAdapter]: The adapter class
            
        Raises:
            KeyError: If gateway is not registered
        """
        if gateway_id not in self._adapters:
            available = list(self._adapters.keys())
            raise KeyError(f"Gateway '{gateway_id}' not found. Available: {available}")
        
        return self._adapters[gateway_id]
    
    def get_gateway_info(self, gateway_id: str) -> Dict:
        """
        Get information about a specific gateway.
        
        Args:
            gateway_id: Gateway identifier
            
        Returns:
            Dict: Gateway information
        """
        if gateway_id not in self._gateway_info:
            raise KeyError(f"Gateway '{gateway_id}' not found")
        
        return self._gateway_info[gateway_id].copy()
    
    def list_gateways(self) -> List[str]:
        """
        Get list of all registered gateway identifiers.
        
        Returns:
            List[str]: List of gateway IDs
        """
        return list(self._adapters.keys())
    
    def list_gateway_info(self) -> Dict[str, Dict]:
        """
        Get information about all registered gateways.
        
        Returns:
            Dict[str, Dict]: Mapping of gateway IDs to their information
        """
        return self._gateway_info.copy()
    
    def is_gateway_registered(self, gateway_id: str) -> bool:
        """
        Check if a gateway is registered.
        
        Args:
            gateway_id: Gateway identifier to check
            
        Returns:
            bool: True if gateway is registered
        """
        return gateway_id in self._adapters
    
    def get_gateways_by_feature(self, feature: str) -> List[str]:
        """
        Get gateways that support a specific feature.
        
        Args:
            feature: Feature to search for (e.g., 'subscriptions', 'lightning')
            
        Returns:
            List[str]: List of gateway IDs supporting the feature
        """
        matching_gateways = []
        
        for gateway_id, info in self._gateway_info.items():
            features = info.get("features", [])
            if feature in features:
                matching_gateways.append(gateway_id)
        
        return matching_gateways
    
    def get_gateways_by_currency(self, currency: str) -> List[str]:
        """
        Get gateways that support a specific currency.
        
        Args:
            currency: Currency code (e.g., 'USD', 'BTC')
            
        Returns:
            List[str]: List of gateway IDs supporting the currency
        """
        matching_gateways = []
        
        for gateway_id, info in self._gateway_info.items():
            supported_currencies = info.get("supported_currencies", [])
            if currency in supported_currencies:
                matching_gateways.append(gateway_id)
        
        return matching_gateways
    
    def get_gateways_by_method(self, payment_method: str) -> List[str]:
        """
        Get gateways that support a specific payment method.
        
        Args:
            payment_method: Payment method (e.g., 'card', 'bitcoin')
            
        Returns:
            List[str]: List of gateway IDs supporting the payment method
        """
        matching_gateways = []
        
        for gateway_id, info in self._gateway_info.items():
            supported_methods = info.get("supported_methods", [])
            if payment_method in supported_methods:
                matching_gateways.append(gateway_id)
        
        return matching_gateways
    
    def get_gateways_for_ai_routing(self) -> List[tuple]:
        """
        Get gateways with their AI routing weights for intelligent routing.
        
        This method provides data that future AI routing systems can use
        to make intelligent gateway selection decisions.
        
        Returns:
            List[tuple]: List of (gateway_id, weight, info) tuples sorted by weight
        """
        gateway_weights = []
        
        for gateway_id, info in self._gateway_info.items():
            weight = info.get("ai_routing_weight", 0.5)
            reliability = info.get("reliability_score", 0.8)
            
            # Combine weight and reliability for AI routing score
            ai_score = (weight * 0.7) + (reliability * 0.3)
            
            gateway_weights.append((gateway_id, ai_score, info))
        
        # Sort by AI score (highest first)
        return sorted(gateway_weights, key=lambda x: x[1], reverse=True)


# Global registry instance
_registry = AdapterRegistry()


def get_adapter_registry() -> AdapterRegistry:
    """
    Get the global adapter registry instance.
    
    Returns:
        AdapterRegistry: The global registry instance
    """
    return _registry


def get_adapter_class(gateway_id: str) -> Type[GatewayAdapter]:
    """
    Convenience function to get an adapter class.
    
    Args:
        gateway_id: Gateway identifier
        
    Returns:
        Type[GatewayAdapter]: The adapter class
    """
    return _registry.get_adapter_class(gateway_id)


def list_available_gateways() -> List[str]:
    """
    Convenience function to list all available gateways.
    
    Returns:
        List[str]: List of gateway IDs
    """
    return _registry.list_gateways()
