<?php
require_once "includes/square-gateway.php";
include "header.php";

$squareGateway = new Square_Gateway();
$gatewayInfo = $squareGateway->getGatewayInfo();
$status = $squareGateway->getStatus();
?>

<main>
  <section class="hero">
    <h1>Square API Integration Test</h1>
  </section>

  <section class="section">
    <div style="max-width: 800px; margin: 0 auto;">
      
      <!-- Integration Status -->
      <div style="background: white; padding: 20px; border-radius: 10px; margin-bottom: 30px; border: 1px solid #ddd;">
        <h2>🔍 Integration Status</h2>
        
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
          <div>
            <h3>Gateway Information</h3>
            <ul style="list-style: none; padding: 0;">
              <li><strong>Name:</strong> <?php echo htmlspecialchars($gatewayInfo['name']); ?></li>
              <li><strong>Description:</strong> <?php echo htmlspecialchars($gatewayInfo['description']); ?></li>
              <li><strong>Icon:</strong> <?php echo $gatewayInfo['icon']; ?></li>
              <li><strong>Available:</strong> <?php echo $gatewayInfo['available'] ? '✅ Yes' : '❌ No'; ?></li>
            </ul>
          </div>
          
          <div>
            <h3>Current Status</h3>
            <div style="background: <?php echo $gatewayInfo['available'] ? '#d4edda' : '#f8d7da'; ?>; 
                        color: <?php echo $gatewayInfo['available'] ? '#155724' : '#721c24'; ?>; 
                        padding: 15px; border-radius: 5px;">
              <strong><?php echo htmlspecialchars($status); ?></strong>
            </div>
          </div>
        </div>
      </div>

      <!-- Features & Capabilities -->
      <div style="background: white; padding: 20px; border-radius: 10px; margin-bottom: 30px; border: 1px solid #ddd;">
        <h2>🚀 Features & Capabilities</h2>
        
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
          <div>
            <h3>Payment Methods</h3>
            <ul>
              <?php foreach ($gatewayInfo['features'] as $feature): ?>
              <li><?php echo htmlspecialchars($feature); ?></li>
              <?php endforeach; ?>
            </ul>
          </div>
          
          <div>
            <h3>Supported Currencies</h3>
            <ul>
              <?php foreach ($gatewayInfo['supported_currencies'] as $currency): ?>
              <li><?php echo htmlspecialchars($currency); ?></li>
              <?php endforeach; ?>
            </ul>
          </div>
        </div>
      </div>

      <!-- Test Payment Forms -->
      <div style="background: white; padding: 20px; border-radius: 10px; margin-bottom: 30px; border: 1px solid #ddd;">
        <h2>🧪 Test Payment Forms</h2>
        
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
          <div>
            <h3>Service Payments</h3>
            <div style="display: flex; flex-direction: column; gap: 10px;">
              <a href="square-payment-form.php?type=service&service=consulting&amount=150" 
                 class="cta-button" style="text-decoration: none; text-align: center; padding: 10px;">
                💼 Consulting ($150)
              </a>
              <a href="square-payment-form.php?type=service&service=mining-pool&amount=200" 
                 class="cta-button" style="text-decoration: none; text-align: center; padding: 10px;">
                ⛏️ Mining Pool ($200)
              </a>
              <a href="square-payment-form.php?type=service&service=mining-services&amount=500" 
                 class="cta-button" style="text-decoration: none; text-align: center; padding: 10px;">
                🏭 Mining Services ($500)
              </a>
            </div>
          </div>
          
          <div>
            <h3>Donation Payments</h3>
            <div style="display: flex; flex-direction: column; gap: 10px;">
              <a href="square-payment-form.php?type=donation&amount=100" 
                 class="cta-button" style="text-decoration: none; text-align: center; padding: 10px; background: #28a745;">
                🎁 $100 Donation
              </a>
              <a href="square-payment-form.php?type=donation&amount=1000" 
                 class="cta-button" style="text-decoration: none; text-align: center; padding: 10px; background: #28a745;">
                💰 $1,000 Donation
              </a>
              <a href="square-payment-form.php?type=donation&amount=5000" 
                 class="cta-button" style="text-decoration: none; text-align: center; padding: 10px; background: #28a745;">
                🚀 $5,000 Donation
              </a>
            </div>
          </div>
        </div>
      </div>

      <!-- Test Card Numbers -->
      <div style="background: #fff3cd; color: #856404; padding: 20px; border-radius: 10px; margin-bottom: 30px;">
        <h2>💳 Test Card Numbers (Sandbox)</h2>
        <p><strong>Use these test card numbers in sandbox mode:</strong></p>
        
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; font-family: monospace;">
          <div>
            <h4>Successful Payments:</h4>
            <ul>
              <li><strong>Visa:</strong> 4111 1111 1111 1111</li>
              <li><strong>Mastercard:</strong> 5555 5555 5555 4444</li>
              <li><strong>Amex:</strong> 3782 822463 10005</li>
              <li><strong>Discover:</strong> 6011 1111 1111 1117</li>
            </ul>
          </div>
          
          <div>
            <h4>Test Scenarios:</h4>
            <ul>
              <li><strong>Declined:</strong> 4000 0000 0000 0002</li>
              <li><strong>Insufficient Funds:</strong> 4000 0000 0000 9995</li>
              <li><strong>Expired Card:</strong> 4000 0000 0000 0069</li>
              <li><strong>Invalid CVV:</strong> 4000 0000 0000 0127</li>
            </ul>
          </div>
        </div>
        
        <p><strong>Additional Test Data:</strong></p>
        <ul>
          <li><strong>Expiry Date:</strong> Any future date (e.g., 12/25)</li>
          <li><strong>CVV:</strong> Any 3-4 digit number (e.g., 123)</li>
          <li><strong>ZIP Code:</strong> Any valid ZIP (e.g., 12345)</li>
        </ul>
      </div>

      <!-- API Configuration -->
      <div style="background: white; padding: 20px; border-radius: 10px; margin-bottom: 30px; border: 1px solid #ddd;">
        <h2>⚙️ API Configuration</h2>
        
        <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace; font-size: 0.9em;">
          <h4>Current Configuration:</h4>
          <ul style="list-style: none; padding: 0;">
            <li><strong>Environment:</strong> <?php echo $gatewayInfo['available'] ? 'Configured' : 'Not configured'; ?></li>
            <li><strong>Application ID:</strong> <?php echo $gatewayInfo['available'] ? 'Set' : 'Not set'; ?></li>
            <li><strong>Access Token:</strong> <?php echo $gatewayInfo['available'] ? 'Set' : 'Not set'; ?></li>
            <li><strong>Location ID:</strong> <?php echo $gatewayInfo['available'] ? 'Set' : 'Not set'; ?></li>
            <li><strong>Webhook Key:</strong> <?php echo $gatewayInfo['available'] ? 'Set' : 'Not set'; ?></li>
          </ul>
        </div>
        
        <?php if (!$gatewayInfo['available']): ?>
        <div style="background: #d1ecf1; color: #0c5460; padding: 15px; border-radius: 5px; margin-top: 15px;">
          <h4>🔧 Setup Instructions:</h4>
          <ol>
            <li>Create a Square Developer account at <a href="https://developer.squareup.com" target="_blank">developer.squareup.com</a></li>
            <li>Create a new application and get your credentials</li>
            <li>Install Square PHP SDK: <code>composer require square/square</code></li>
            <li>Update <code>includes/square-config.php</code> with your credentials</li>
            <li>Test in sandbox mode before going live</li>
          </ol>
        </div>
        <?php endif; ?>
      </div>

      <!-- Webhook Testing -->
      <div style="background: white; padding: 20px; border-radius: 10px; margin-bottom: 30px; border: 1px solid #ddd;">
        <h2>🔔 Webhook Testing</h2>
        
        <div style="background: #f8f9fa; padding: 15px; border-radius: 5px;">
          <h4>Webhook Endpoint:</h4>
          <p style="font-family: monospace; background: white; padding: 10px; border-radius: 3px;">
            https://<?php echo $_SERVER['HTTP_HOST']; ?>/square-webhook.php
          </p>
          
          <h4>Supported Events:</h4>
          <ul>
            <li><code>payment.created</code> - New payment created</li>
            <li><code>payment.updated</code> - Payment status changed</li>
            <li><code>refund.created</code> - Refund initiated</li>
            <li><code>refund.updated</code> - Refund status changed</li>
          </ul>
        </div>
      </div>

      <!-- Navigation -->
      <div style="text-align: center; margin: 30px 0;">
        <a href="square-admin-dashboard.php" class="cta-button" style="text-decoration: none; margin-right: 10px;">
          ← Admin Dashboard
        </a>
        <a href="multi-gateway-payment-form.php" class="cta-button" style="text-decoration: none; margin-right: 10px; background: #28a745;">
          💳 Multi-Gateway Form
        </a>
        <a href="simple-payment-test.php" class="cta-button" style="text-decoration: none; background: #6c757d;">
          🧪 Payment Tests
        </a>
      </div>
    </div>
  </section>
</main>

<script>
console.log('Square Test Page loaded');
console.log('Gateway Info:', <?php echo json_encode($gatewayInfo); ?>);

// Add click tracking for test links
document.addEventListener('click', function(e) {
  if (e.target.tagName === 'A' && e.target.href.includes('square-payment-form.php')) {
    console.log('Square payment form link clicked:', e.target.href);
  }
});
</script>

<?php include "footer.php"; ?>
