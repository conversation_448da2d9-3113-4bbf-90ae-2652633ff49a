"""
Payment-related Pydantic models for the ELOH Processing Payment Gateway API
These models define the standard interface that customers use
"""

from pydantic import BaseModel, Field, validator
from typing import Optional, Dict, Any, List
from decimal import Decimal
from datetime import datetime
from enum import Enum

class PaymentStatus(str, Enum):
    """Standard payment status across all gateways"""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    REFUNDED = "refunded"
    PARTIALLY_REFUNDED = "partially_refunded"

class PaymentMethod(str, Enum):
    """Supported payment methods"""
    CARD = "card"
    BANK_TRANSFER = "bank_transfer"
    BITCOIN = "bitcoin"
    LIGHTNING = "lightning"
    CRYPTOCURRENCY = "cryptocurrency"
    DIGITAL_WALLET = "digital_wallet"

class Currency(str, Enum):
    """Supported currencies"""
    USD = "USD"
    EUR = "EUR"
    GBP = "GBP"
    BTC = "BTC"
    ETH = "ETH"
    USDT = "USDT"
    LTC = "LTC"

class PaymentRequest(BaseModel):
    """Standard payment request model - what customers send to our API"""
    
    # Required fields
    amount: Decimal = Field(..., gt=0, description="Payment amount")
    currency: Currency = Field(..., description="Payment currency")
    
    # Optional fields
    customer_id: Optional[str] = Field(None, description="Customer identifier")
    payment_method: Optional[PaymentMethod] = Field(None, description="Preferred payment method")
    description: Optional[str] = Field(None, max_length=500, description="Payment description")
    reference: Optional[str] = Field(None, max_length=100, description="External reference")
    
    # Gateway routing hints (optional - used by rule engine)
    preferred_gateway: Optional[str] = Field(None, description="Preferred gateway (stripe, square, btcpay, nowpayments)")
    gateway_priority: Optional[List[str]] = Field(None, description="Gateway priority list")
    
    # Metadata
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")
    
    # Webhook and redirect URLs
    success_url: Optional[str] = Field(None, description="Success redirect URL")
    cancel_url: Optional[str] = Field(None, description="Cancel redirect URL")
    webhook_url: Optional[str] = Field(None, description="Webhook notification URL")
    
    @validator('amount')
    def validate_amount(cls, v):
        """Validate amount precision and range"""
        if v <= 0:
            raise ValueError('Amount must be greater than 0')
        if v > Decimal('1000000'):
            raise ValueError('Amount exceeds maximum limit')
        return v
    
    @validator('metadata')
    def validate_metadata(cls, v):
        """Validate metadata size"""
        if v and len(str(v)) > 2000:
            raise ValueError('Metadata too large')
        return v

class PaymentResponse(BaseModel):
    """Standard payment response model - what customers receive from our API"""
    
    # Payment identifiers
    payment_id: str = Field(..., description="Our internal payment ID")
    gateway_payment_id: Optional[str] = Field(None, description="Gateway-specific payment ID")
    reference: Optional[str] = Field(None, description="External reference")
    
    # Payment details
    amount: Decimal = Field(..., description="Payment amount")
    currency: Currency = Field(..., description="Payment currency")
    status: PaymentStatus = Field(..., description="Payment status")
    
    # Gateway information
    gateway_used: str = Field(..., description="Gateway that processed the payment")
    payment_method: Optional[PaymentMethod] = Field(None, description="Payment method used")
    
    # URLs and actions
    checkout_url: Optional[str] = Field(None, description="Checkout URL for customer")
    redirect_url: Optional[str] = Field(None, description="Redirect URL after payment")
    
    # Timestamps
    created_at: datetime = Field(..., description="Payment creation timestamp")
    updated_at: Optional[datetime] = Field(None, description="Last update timestamp")
    expires_at: Optional[datetime] = Field(None, description="Payment expiration timestamp")
    
    # Additional data
    gateway_response: Optional[Dict[str, Any]] = Field(None, description="Raw gateway response")
    fees: Optional[Dict[str, Decimal]] = Field(None, description="Fee breakdown")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")

class PaymentStatusRequest(BaseModel):
    """Request model for payment status checks"""
    payment_id: str = Field(..., description="Payment ID to check")

class RefundRequest(BaseModel):
    """Request model for payment refunds"""
    payment_id: str = Field(..., description="Payment ID to refund")
    amount: Optional[Decimal] = Field(None, gt=0, description="Refund amount (full refund if not specified)")
    reason: Optional[str] = Field(None, max_length=500, description="Refund reason")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")

class RefundResponse(BaseModel):
    """Response model for payment refunds"""
    refund_id: str = Field(..., description="Refund ID")
    payment_id: str = Field(..., description="Original payment ID")
    amount: Decimal = Field(..., description="Refund amount")
    status: PaymentStatus = Field(..., description="Refund status")
    gateway_used: str = Field(..., description="Gateway that processed the refund")
    created_at: datetime = Field(..., description="Refund creation timestamp")
    gateway_response: Optional[Dict[str, Any]] = Field(None, description="Raw gateway response")

class PaymentListRequest(BaseModel):
    """Request model for listing payments"""
    customer_id: Optional[str] = Field(None, description="Filter by customer ID")
    status: Optional[PaymentStatus] = Field(None, description="Filter by status")
    gateway: Optional[str] = Field(None, description="Filter by gateway")
    limit: int = Field(50, ge=1, le=100, description="Number of results to return")
    offset: int = Field(0, ge=0, description="Number of results to skip")
    start_date: Optional[datetime] = Field(None, description="Filter by start date")
    end_date: Optional[datetime] = Field(None, description="Filter by end date")

class PaymentListResponse(BaseModel):
    """Response model for listing payments"""
    payments: List[PaymentResponse] = Field(..., description="List of payments")
    total: int = Field(..., description="Total number of payments")
    limit: int = Field(..., description="Limit used")
    offset: int = Field(..., description="Offset used")
    has_more: bool = Field(..., description="Whether there are more results")
