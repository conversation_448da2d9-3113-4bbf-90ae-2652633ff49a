# ELOH Processing Multisig Wallet Setup Guide

## Bitcoin (BTC) Multisig Setup with BTCPay Server Vault

1. Log into BTCPay Server admin panel
2. Navigate to Wallets > Create a new wallet
3. Select "Create a new wallet" and choose "Multi-signature wallet"
4. Configure as 2-of-3 multisig
5. Add each hardware wallet device (Ledger/Trezor) for the 3 key holders
6. Complete setup and test with a small transaction

## Ethereum (ETH) Multisig with Gnosis Safe

1. Visit https://gnosis-safe.io/
2. Connect hardware wallet of first key holder
3. Select "Create new Safe"
4. Add the Ethereum addresses of all 3 key holders
5. Set threshold to 2 (for 2-of-3)
6. Deploy the Safe contract (requires ETH for gas)
7. Test with a small transaction

## TRON (TRX) Multisig with TronLink + TronScan

1. Each key holder installs TronLink and sets up with hardware wallet
2. First key holder creates multisig account on TronScan
3. Add the other two key holders' addresses
4. Configure as 2-of-3 multisig
5. Test with a small transaction

## Security Procedures

1. Transaction approval requires documented request via secure channel
2. Two authorized signers must verify transaction details independently
3. Monthly audit of all multisig transactions
4. Quarterly key holder verification and access testing