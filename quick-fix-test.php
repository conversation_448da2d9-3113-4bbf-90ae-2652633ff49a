<?php
// Quick Fix Test for BTCPay URL Issue
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>BTCPay URL Fix Test</h1>";

echo "<h2>Step 1: Test Current Configuration</h2>";
try {
    require_once "includes/btcpay-gateway.php";
    $gateway = new BTCPay_Gateway();
    $debug = $gateway->getDebugInfo();
    
    echo "<p><strong>Current Host:</strong> '" . htmlspecialchars($debug['host']) . "'</p>";
    echo "<p><strong>Host Length:</strong> " . strlen($debug['host']) . " characters</p>";
    
    // Test URL construction
    $testUrl = $debug['host'] . '/api/v1/stores/' . urlencode('DzeKFNoYV91vVvp2wf5PXUp33P4EvurrfVGvbPWd8RQS') . '/invoices';
    echo "<p><strong>Test URL:</strong> '" . htmlspecialchars($testUrl) . "'</p>";
    
    if (filter_var($testUrl, FILTER_VALIDATE_URL)) {
        echo "<p style='color: green;'>✅ URL is now valid!</p>";
    } else {
        echo "<p style='color: red;'>❌ URL is still invalid</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<h2>Step 2: Test Clean Configuration</h2>";
echo "<p>If the above still shows issues, we can use the clean configuration file.</p>";

if (isset($_POST['use_clean_config'])) {
    // Backup current config and use clean one
    if (copy('includes/btcpay-config.php', 'includes/btcpay-config-backup.php')) {
        echo "<p>✅ Backed up current config to btcpay-config-backup.php</p>";
    }
    
    if (copy('includes/btcpay-config-clean.php', 'includes/btcpay-config.php')) {
        echo "<p>✅ Replaced config with clean version</p>";
        echo "<p><strong>Please refresh this page to test the new configuration.</strong></p>";
    } else {
        echo "<p style='color: red;'>❌ Failed to replace config file</p>";
    }
} else {
    echo "<form method='POST'>";
    echo "<button type='submit' name='use_clean_config' style='background: #28a745; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;'>Use Clean Configuration</button>";
    echo "</form>";
}

echo "<h2>Step 3: Manual URL Test</h2>";
echo "<p>Testing URL construction manually:</p>";

$cleanHost = 'https://mainnet.demo.btcpayserver.org';
$cleanStoreId = 'DzeKFNoYV91vVvp2wf5PXUp33P4EvurrfVGvbPWd8RQS';
$manualUrl = $cleanHost . '/api/v1/stores/' . urlencode($cleanStoreId) . '/invoices';

echo "<p><strong>Manual URL:</strong> '" . htmlspecialchars($manualUrl) . "'</p>";

if (filter_var($manualUrl, FILTER_VALIDATE_URL)) {
    echo "<p style='color: green;'>✅ Manual URL is valid!</p>";
} else {
    echo "<p style='color: red;'>❌ Manual URL is invalid</p>";
}

echo "<hr>";
echo "<p><a href='config-debug.php'>View Detailed Debug Info</a> | <a href='btcpay-troubleshoot.php'>Back to Troubleshooting</a></p>";
?>
