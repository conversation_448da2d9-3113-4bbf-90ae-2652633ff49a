"""
Gateway adapter registry for ELOH Processing Payment Gateway
"""

from typing import Dict, List, Optional, Any
from functools import lru_cache
import logging

logger = logging.getLogger(__name__)


class GatewayInfo:
    """Information about a payment gateway"""
    
    def __init__(
        self,
        id: str,
        name: str,
        description: str,
        supported_currencies: List[str],
        supported_regions: List[str],
        features: List[str]
    ):
        self.id = id
        self.name = name
        self.description = description
        self.supported_currencies = supported_currencies
        self.supported_regions = supported_regions
        self.features = features


class AdapterRegistry:
    """Registry for payment gateway adapters"""
    
    def __init__(self):
        self._gateways: Dict[str, GatewayInfo] = {}
        self._initialize_gateways()
    
    def _initialize_gateways(self):
        """Initialize gateway information"""
        
        # BTCPay Server - Available globally
        self._gateways["btcpay"] = GatewayInfo(
            id="btcpay",
            name="BTCPay Server",
            description="Self-hosted Bitcoin payment processor",
            supported_currencies=["BTC", "LTC", "USD"],
            supported_regions=["global"],
            features=["bitcoin", "lightning", "self_hosted", "no_kyc"]
        )
        
        # NowPayments - Available globally
        self._gateways["nowpayments"] = GatewayInfo(
            id="nowpayments",
            name="NowPayments",
            description="Cryptocurrency payment gateway with 300+ coins",
            supported_currencies=["BTC", "ETH", "LTC", "XMR", "USD", "EUR"],
            supported_regions=["global"],
            features=["crypto", "multiple_coins", "instant_settlement"]
        )
        
        # Stripe - Limited availability
        self._gateways["stripe"] = GatewayInfo(
            id="stripe",
            name="Stripe",
            description="Online payment processing platform",
            supported_currencies=["USD", "EUR", "GBP", "CAD"],
            supported_regions=["US", "EU", "CA", "AU"],
            features=["cards", "bank_transfers", "digital_wallets"]
        )
        
        # Square - Limited availability
        self._gateways["square"] = GatewayInfo(
            id="square",
            name="Square",
            description="Payment processing and business management",
            supported_currencies=["USD", "CAD", "GBP", "EUR"],
            supported_regions=["US", "CA", "GB", "AU"],
            features=["cards", "pos", "invoicing", "inventory"]
        )
    
    def list_gateways(self) -> List[str]:
        """Get list of available gateway IDs"""
        return list(self._gateways.keys())
    
    def get_gateway_info(self, gateway_id: str) -> Optional[GatewayInfo]:
        """Get information about a specific gateway"""
        return self._gateways.get(gateway_id)
    
    def get_all_gateway_info(self) -> Dict[str, GatewayInfo]:
        """Get information about all gateways"""
        return self._gateways.copy()
    
    def is_gateway_available_in_region(self, gateway_id: str, region: str) -> bool:
        """Check if a gateway is available in a specific region"""
        gateway = self._gateways.get(gateway_id)
        if not gateway:
            return False
        
        # Global gateways are available everywhere
        if "global" in gateway.supported_regions:
            return True
        
        # Check specific region support
        return region.upper() in [r.upper() for r in gateway.supported_regions]
    
    def get_available_gateways_for_region(self, region: str) -> List[str]:
        """Get list of gateways available in a specific region"""
        available = []
        for gateway_id in self._gateways:
            if self.is_gateway_available_in_region(gateway_id, region):
                available.append(gateway_id)
        return available
    
    def supports_currency(self, gateway_id: str, currency: str) -> bool:
        """Check if a gateway supports a specific currency"""
        gateway = self._gateways.get(gateway_id)
        if not gateway:
            return False
        
        return currency.upper() in [c.upper() for c in gateway.supported_currencies]


@lru_cache()
def get_adapter_registry() -> AdapterRegistry:
    """Get the global adapter registry instance"""
    return AdapterRegistry()
