<?php
require_once "includes/square-gateway.php";
include "header.php";

// Get payment parameters
$payment_type = $_GET["type"] ?? "service";
$is_donation = ($payment_type === "donation");
$preset_amount = $_GET["amount"] ?? "";
$preset_service = $_GET["service"] ?? "";

// Initialize Square gateway to get config
$squareGateway = new Square_Gateway();
$squareConfig = $squareGateway->getWebPaymentsConfig();
?>

<main>
  <section class="hero">
    <div class="hero-content">
      <h1><?php echo $is_donation ? "💳 Make a Donation with Square" : "💳 Pay with Credit Card"; ?></h1>
      <p><?php echo $is_donation ? "Support our mission with secure credit card donations" : "Secure credit card payments powered by Square"; ?></p>
      <div class="flex gap-md justify-center mt-xl">
        <a href="#square-payment-form" class="cta-button">💳 Start Payment</a>
        <a href="#payment-info" class="cta-button secondary">🔍 Payment Info</a>
      </div>
    </div>
  </section>

  <!-- Payment Stats -->
  <section class="section">
    <div class="three-col">
      <div class="card text-center">
        <h3>🔒 100%</h3>
        <p class="text-muted">PCI Compliant</p>
      </div>
      <div class="card text-center">
        <h3>💳 6+</h3>
        <p class="text-muted">Payment Methods</p>
      </div>
      <div class="card text-center">
        <h3>🛡️ 256-bit</h3>
        <p class="text-muted">SSL Encryption</p>
      </div>
    </div>
  </section>

  <section class="section" id="square-payment-form">
    <div class="two-col">
      <div>
        <h2>💳 Square Payment</h2>
        <p>Secure credit card processing powered by Square. Your payment information is encrypted and PCI compliant.</p>
        <div class="alert alert-success">
          <strong>🔒 Enterprise Security:</strong> Bank-level encryption and fraud protection
        </div>
      </div>
      <div class="card">
        <h3>💳 Accepted Payment Methods</h3>
        <div class="two-col">
          <div class="text-center">
            <div style="font-size: 2rem; margin-bottom: var(--space-sm);">💳</div>
            <h5>Credit Cards</h5>
            <p class="text-muted">Visa, Mastercard, Amex, Discover</p>
          </div>
          <div class="text-center">
            <div style="font-size: 2rem; margin-bottom: var(--space-sm);">📱</div>
            <h5>Digital Wallets</h5>
            <p class="text-muted">Apple Pay, Google Pay</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Payment Form -->
    <div class="card mt-xl">
      <form id="payment-form">
        <h3>💳 Payment Details</h3>

        <div class="form-group">
          <label for="amount" class="form-label required">💰 Amount (USD)</label>
          <div class="input-group">
            <div class="input-group-addon">$</div>
            <input type="number" id="amount" name="amount" min="1" step="0.01" required
                   value="<?php echo htmlspecialchars($preset_amount ?: '10'); ?>"
                   class="form-input" placeholder="0.00">
          </div>
          <small class="text-muted">Minimum: $1.00 USD</small>
        </div>

        <div class="form-group">
          <label for="email" class="form-label required">📧 Email Address</label>
          <input type="email" id="email" name="email" required
                 class="form-input" placeholder="<EMAIL>">
          <small class="text-muted">For payment confirmation and receipt</small>
        </div>

        <?php if (!$is_donation): ?>
        <div class="form-group">
          <label for="service" class="form-label required">💼 Service Type</label>
          <select id="service" name="service" required class="form-input form-select">
            <option value="">Select a service...</option>
            <option value="consulting" <?php echo ($preset_service === 'consulting') ? 'selected' : ''; ?>>💡 Crypto & Forex Consulting ($150/hour)</option>
            <option value="mining-pool" <?php echo ($preset_service === 'mining-pool') ? 'selected' : ''; ?>>⛏️ Mining Pool Membership ($200/year)</option>
            <option value="mining-services" <?php echo ($preset_service === 'mining-services') ? 'selected' : ''; ?>>🔧 Mining Services ($500/month)</option>
            <option value="analysis" <?php echo ($preset_service === 'analysis') ? 'selected' : ''; ?>>📊 Market Analysis Report ($99/report)</option>
            <option value="other" <?php echo ($preset_service === 'other') ? 'selected' : ''; ?>>🔧 Other Services</option>
          </select>
        </div>
        <?php endif; ?>

        <div class="form-group">
          <label for="description" class="form-label">📝 Description/Notes</label>
          <textarea id="description" name="description" rows="4"
                    class="form-input form-textarea"
                    placeholder="<?php echo $is_donation ? "Optional message with your donation..." : "Describe the service you are paying for..."; ?>"></textarea>
        </div>

        <!-- Hidden field for payment type -->
        <input type="hidden" name="payment_type" value="<?php echo htmlspecialchars($payment_type); ?>">

        <!-- Square Payment Section -->
        <div class="form-group">
          <h4>💳 Card Information</h4>
          <?php
          $amount = floatval($preset_amount ?: 10);
          echo $squareGateway->generatePaymentForm($amount, 'USD', [
              'payment_type' => $payment_type,
              'show_customer_fields' => false // We have our own fields above
          ]);
          ?>
        </div>
      </form>
    </div>

    <!-- Security Information -->
    <div class="alert alert-info mt-xl" id="payment-info">
      <h4>🔒 Your Security is Our Priority</h4>
      <div class="two-col">
        <div class="card">
          <h5>🛡️ PCI DSS Compliant</h5>
          <p class="text-muted">All card data is processed securely by Square and never stored on our servers.</p>
        </div>
        <div class="card">
          <h5>🔐 256-bit SSL Encryption</h5>
          <p class="text-muted">Your payment information is encrypted during transmission using bank-level security.</p>
        </div>
      </div>
    </div>

    <!-- Navigation -->
    <div class="text-center mt-xl">
      <div class="flex gap-md justify-center">
        <a href="multi-gateway-payment-form.php" class="text-muted">← Back to Payment Options</a>
        <span class="text-muted">|</span>
        <a href="index.php" class="text-muted">Homepage</a>
      </div>
    </div>
  </section>
</main>

<!-- Square Web Payments SDK and JavaScript are now handled by the gateway generatePaymentForm() method -->

<?php include "footer.php"; ?>
