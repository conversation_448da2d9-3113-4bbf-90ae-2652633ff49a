"""
SQLAlchemy database models for ELOH Processing Multi-Tenant Payment Gateway

This module defines the database schema for storing tenant information,
gateway configurations, payments, and usage statistics.
"""

from sqlalchemy import Column, String, Integer, Float, DateTime, Boolean, Text, JSON, ForeignKey, Index
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from datetime import datetime
import uuid

Base = declarative_base()


class Tenant(Base):
    """Tenant (customer) table"""
    __tablename__ = "tenants"
    
    # Primary identifiers
    id = Column(String(50), primary_key=True, default=lambda: f"tenant_{uuid.uuid4().hex[:12]}")
    api_key = Column(String(100), unique=True, nullable=False, index=True)
    
    # Company information
    company_name = Column(String(100), nullable=False)
    business_type = Column(String(50), nullable=False)
    website_url = Column(String(255), nullable=True)
    
    # Contact information
    contact_email = Column(String(100), nullable=False, index=True)
    contact_name = Column(String(100), nullable=False)
    contact_phone = Column(String(20), nullable=True)
    
    # Address information
    address_line1 = Column(String(100), nullable=False)
    address_line2 = Column(String(100), nullable=True)
    city = Column(String(50), nullable=False)
    state = Column(String(50), nullable=True)
    postal_code = Column(String(20), nullable=False)
    country = Column(String(2), nullable=False)  # ISO 3166-1 alpha-2
    
    # Service information
    plan = Column(String(20), nullable=False, default="starter")  # starter, professional, enterprise, custom
    status = Column(String(20), nullable=False, default="active")  # active, suspended, pending, cancelled
    expected_monthly_volume = Column(Float, nullable=True)
    
    # Preferences
    preferred_gateways = Column(JSON, nullable=True)  # List of preferred gateway IDs
    required_currencies = Column(JSON, nullable=True)  # List of required currencies
    
    # Usage tracking
    total_transactions = Column(Integer, default=0)
    total_volume = Column(Float, default=0.0)
    monthly_volume = Column(Float, default=0.0)
    monthly_reset_date = Column(DateTime, default=func.now())
    
    # Timestamps
    created_at = Column(DateTime, default=func.now(), nullable=False)
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    last_activity = Column(DateTime, nullable=True)
    
    # Metadata
    metadata = Column(JSON, nullable=True)
    
    # Relationships
    gateway_configs = relationship("GatewayConfig", back_populates="tenant", cascade="all, delete-orphan")
    payments = relationship("Payment", back_populates="tenant", cascade="all, delete-orphan")
    
    # Indexes
    __table_args__ = (
        Index('idx_tenant_email', 'contact_email'),
        Index('idx_tenant_status', 'status'),
        Index('idx_tenant_plan', 'plan'),
        Index('idx_tenant_created', 'created_at'),
    )


class GatewayConfig(Base):
    """Gateway configuration per tenant"""
    __tablename__ = "gateway_configs"
    
    # Primary identifiers
    id = Column(String(50), primary_key=True, default=lambda: f"gw_{uuid.uuid4().hex[:12]}")
    tenant_id = Column(String(50), ForeignKey("tenants.id"), nullable=False)
    gateway_id = Column(String(20), nullable=False)  # stripe, square, btcpay, nowpayments
    
    # Configuration
    enabled = Column(Boolean, default=True, nullable=False)
    priority = Column(Integer, default=1, nullable=False)
    
    # Encrypted credentials (stored as JSON)
    encrypted_credentials = Column(Text, nullable=False)
    
    # Gateway-specific configuration
    configuration = Column(JSON, nullable=True)
    
    # Limits and settings
    min_amount = Column(Float, nullable=True)
    max_amount = Column(Float, nullable=True)
    daily_limit = Column(Float, nullable=True)
    monthly_limit = Column(Float, nullable=True)
    
    # Features
    supports_refunds = Column(Boolean, default=True)
    supports_recurring = Column(Boolean, default=False)
    supports_webhooks = Column(Boolean, default=True)
    
    # Webhook configuration
    webhook_url = Column(String(255), nullable=True)
    webhook_events = Column(JSON, nullable=True)  # List of subscribed events
    
    # Usage tracking
    total_transactions = Column(Integer, default=0)
    total_volume = Column(Float, default=0.0)
    last_used = Column(DateTime, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime, default=func.now(), nullable=False)
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # Relationships
    tenant = relationship("Tenant", back_populates="gateway_configs")
    payments = relationship("Payment", back_populates="gateway_config")
    
    # Indexes and constraints
    __table_args__ = (
        Index('idx_gateway_tenant', 'tenant_id'),
        Index('idx_gateway_id', 'gateway_id'),
        Index('idx_gateway_enabled', 'enabled'),
        Index('idx_gateway_priority', 'priority'),
    )


class Payment(Base):
    """Payment transactions table"""
    __tablename__ = "payments"
    
    # Primary identifiers
    id = Column(String(50), primary_key=True, default=lambda: f"pay_{uuid.uuid4().hex[:12]}")
    tenant_id = Column(String(50), ForeignKey("tenants.id"), nullable=False)
    gateway_config_id = Column(String(50), ForeignKey("gateway_configs.id"), nullable=False)
    
    # Payment identifiers
    gateway_payment_id = Column(String(100), nullable=False)  # ID from gateway
    reference = Column(String(100), nullable=True)  # Merchant reference
    
    # Payment details
    amount = Column(Float, nullable=False)
    currency = Column(String(3), nullable=False)
    description = Column(Text, nullable=True)
    
    # Customer information
    customer_email = Column(String(100), nullable=True)
    customer_name = Column(String(100), nullable=True)
    customer_phone = Column(String(20), nullable=True)
    
    # Payment status
    status = Column(String(20), nullable=False)  # pending, processing, completed, failed, cancelled, refunded
    gateway_status = Column(String(50), nullable=True)  # Original gateway status
    
    # Gateway information
    gateway_used = Column(String(20), nullable=False)
    payment_method = Column(String(20), nullable=True)  # card, bitcoin, etc.
    
    # URLs
    checkout_url = Column(String(500), nullable=True)
    success_url = Column(String(500), nullable=True)
    cancel_url = Column(String(500), nullable=True)
    
    # Fees and amounts
    gateway_fee = Column(Float, nullable=True)
    net_amount = Column(Float, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime, default=func.now(), nullable=False)
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    completed_at = Column(DateTime, nullable=True)
    expires_at = Column(DateTime, nullable=True)
    
    # Metadata and gateway response
    metadata = Column(JSON, nullable=True)
    gateway_response = Column(JSON, nullable=True)
    
    # Relationships
    tenant = relationship("Tenant", back_populates="payments")
    gateway_config = relationship("GatewayConfig", back_populates="payments")
    refunds = relationship("Refund", back_populates="payment", cascade="all, delete-orphan")
    
    # Indexes
    __table_args__ = (
        Index('idx_payment_tenant', 'tenant_id'),
        Index('idx_payment_gateway', 'gateway_used'),
        Index('idx_payment_status', 'status'),
        Index('idx_payment_created', 'created_at'),
        Index('idx_payment_gateway_id', 'gateway_payment_id'),
        Index('idx_payment_reference', 'reference'),
        Index('idx_payment_customer_email', 'customer_email'),
    )


class Refund(Base):
    """Refund transactions table"""
    __tablename__ = "refunds"
    
    # Primary identifiers
    id = Column(String(50), primary_key=True, default=lambda: f"ref_{uuid.uuid4().hex[:12]}")
    payment_id = Column(String(50), ForeignKey("payments.id"), nullable=False)
    
    # Refund identifiers
    gateway_refund_id = Column(String(100), nullable=False)
    
    # Refund details
    amount = Column(Float, nullable=False)
    reason = Column(String(255), nullable=True)
    
    # Status
    status = Column(String(20), nullable=False)  # pending, processing, completed, failed
    gateway_status = Column(String(50), nullable=True)
    
    # Timestamps
    created_at = Column(DateTime, default=func.now(), nullable=False)
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    completed_at = Column(DateTime, nullable=True)
    
    # Gateway response
    gateway_response = Column(JSON, nullable=True)
    
    # Relationships
    payment = relationship("Payment", back_populates="refunds")
    
    # Indexes
    __table_args__ = (
        Index('idx_refund_payment', 'payment_id'),
        Index('idx_refund_status', 'status'),
        Index('idx_refund_created', 'created_at'),
        Index('idx_refund_gateway_id', 'gateway_refund_id'),
    )


class Customer(Base):
    """Customer information table"""
    __tablename__ = "customers"
    
    # Primary identifiers
    id = Column(String(50), primary_key=True, default=lambda: f"cust_{uuid.uuid4().hex[:12]}")
    tenant_id = Column(String(50), ForeignKey("tenants.id"), nullable=False)
    
    # Customer identifiers
    email = Column(String(100), nullable=False)
    
    # Gateway customer IDs
    gateway_customer_ids = Column(JSON, nullable=True)  # {gateway: customer_id}
    
    # Customer information
    first_name = Column(String(50), nullable=True)
    last_name = Column(String(50), nullable=True)
    phone = Column(String(20), nullable=True)
    company = Column(String(100), nullable=True)
    
    # Address information
    address_line1 = Column(String(100), nullable=True)
    address_line2 = Column(String(100), nullable=True)
    city = Column(String(50), nullable=True)
    state = Column(String(50), nullable=True)
    postal_code = Column(String(20), nullable=True)
    country = Column(String(2), nullable=True)
    
    # Timestamps
    created_at = Column(DateTime, default=func.now(), nullable=False)
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # Metadata
    metadata = Column(JSON, nullable=True)
    
    # Indexes
    __table_args__ = (
        Index('idx_customer_tenant', 'tenant_id'),
        Index('idx_customer_email', 'email'),
        Index('idx_customer_tenant_email', 'tenant_id', 'email'),
    )


class UsageStats(Base):
    """Usage statistics table for analytics"""
    __tablename__ = "usage_stats"
    
    # Primary identifiers
    id = Column(String(50), primary_key=True, default=lambda: f"stat_{uuid.uuid4().hex[:12]}")
    tenant_id = Column(String(50), ForeignKey("tenants.id"), nullable=False)
    
    # Time period
    period_type = Column(String(10), nullable=False)  # daily, weekly, monthly
    period_start = Column(DateTime, nullable=False)
    period_end = Column(DateTime, nullable=False)
    
    # Transaction statistics
    total_transactions = Column(Integer, default=0)
    successful_transactions = Column(Integer, default=0)
    failed_transactions = Column(Integer, default=0)
    refunded_transactions = Column(Integer, default=0)
    
    # Volume statistics
    total_volume = Column(Float, default=0.0)
    successful_volume = Column(Float, default=0.0)
    refunded_volume = Column(Float, default=0.0)
    
    # Gateway breakdown
    gateway_stats = Column(JSON, nullable=True)  # Per-gateway statistics
    currency_stats = Column(JSON, nullable=True)  # Per-currency statistics
    
    # Timestamps
    created_at = Column(DateTime, default=func.now(), nullable=False)
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # Indexes
    __table_args__ = (
        Index('idx_usage_tenant', 'tenant_id'),
        Index('idx_usage_period', 'period_type', 'period_start'),
        Index('idx_usage_tenant_period', 'tenant_id', 'period_type', 'period_start'),
    )
