# 🚀 ELOH Processing Payment Gateway - Render Deployment Guide

## 📋 Overview

This guide covers deploying the ELOH Processing Multi-Tenant Payment Gateway to Render.com, a modern cloud platform that's perfect for Python applications.

## 🌟 Why Render?

- **Easy Deployment**: Git-based deployments
- **Automatic SSL**: Free SSL certificates
- **PostgreSQL**: Managed database included
- **Global CDN**: Fast worldwide access
- **Affordable**: Great pricing for startups
- **Auto-scaling**: Handles traffic spikes

## 🚀 Quick Deployment (Recommended)

### Option 1: One-Click Deploy

[![Deploy to Render](https://render.com/images/deploy-to-render-button.svg)](https://render.com/deploy?repo=https://github.com/your-username/eloh-payment-gateway)

### Option 2: Manual Deployment

## 📋 Prerequisites

1. **Render Account**: Sign up at [render.com](https://render.com)
2. **GitHub Repository**: Push your code to GitHub
3. **Gateway Credentials**: BTCPay Server and/or NowPayments API keys

## 🛠️ Step-by-Step Deployment

### 1. Prepare Your Repository

```bash
# Ensure all files are committed
git add .
git commit -m "Prepare for Render deployment"
git push origin main
```

### 2. Create Render Services

#### A. Create PostgreSQL Database

1. Go to [Render Dashboard](https://dashboard.render.com)
2. Click **"New +"** → **"PostgreSQL"**
3. Configure:
   - **Name**: `eloh-gateway-db`
   - **Database**: `eloh_gateway`
   - **User**: `eloh_user`
   - **Region**: Choose closest to your users
   - **Plan**: Start with **Free** (can upgrade later)
4. Click **"Create Database"**
5. **Save the connection details** (you'll need them)

#### B. Create Web Service

1. Click **"New +"** → **"Web Service"**
2. Connect your GitHub repository
3. Configure:
   - **Name**: `eloh-payment-gateway-api`
   - **Environment**: `Python 3`
   - **Region**: Same as database
   - **Branch**: `main`
   - **Build Command**: `chmod +x build.sh && ./build.sh`
   - **Start Command**: `uvicorn main:app --host 0.0.0.0 --port $PORT`
   - **Plan**: Start with **Free** (can upgrade later)

### 3. Configure Environment Variables

In your Render web service dashboard, add these environment variables:

#### Required Variables:
```env
APP_NAME=ELOH Processing Payment Gateway
ENVIRONMENT=production
DEBUG=false
LOG_LEVEL=INFO
LOG_FORMAT=json
```

#### Database (Auto-configured):
```env
DATABASE_URL=<auto-populated-by-render>
```

#### Gateway Credentials (Set these manually):

**BTCPay Server** (Recommended):
```env
BTCPAY_SERVER_URL=https://your-btcpay-server.com
BTCPAY_API_KEY=your_btcpay_api_key
BTCPAY_STORE_ID=your_btcpay_store_id
BTCPAY_WEBHOOK_SECRET=your_btcpay_webhook_secret
```

**NowPayments** (Recommended):
```env
NOWPAYMENTS_API_KEY=your_nowpayments_api_key
NOWPAYMENTS_IPN_SECRET=your_nowpayments_ipn_secret
NOWPAYMENTS_ENVIRONMENT=production
```

**Optional Gateways** (Limited in Dominica):
```env
# Stripe (if available)
STRIPE_SECRET_KEY=sk_live_your_stripe_key
STRIPE_PUBLISHABLE_KEY=pk_live_your_stripe_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# Square (not available in Dominica)
# SQUARE_ACCESS_TOKEN=your_square_token
# SQUARE_APPLICATION_ID=your_square_app_id
```

### 4. Deploy

1. Click **"Create Web Service"**
2. Render will automatically:
   - Clone your repository
   - Install dependencies
   - Set up database
   - Create sample data
   - Start your application

### 5. Configure Custom Domain (Optional)

1. In your web service settings
2. Go to **"Settings"** → **"Custom Domains"**
3. Add your domain: `api.elohprocessing.com`
4. Update your DNS records as instructed
5. Render will automatically provision SSL

## 🔧 Post-Deployment Configuration

### 1. Verify Deployment

Visit your deployed URLs:

- **API Docs**: `https://your-app.onrender.com/docs`
- **Tenant Portal**: `https://your-app.onrender.com/v1/portal`
- **Health Check**: `https://your-app.onrender.com/health`
- **Gateway Status**: `https://your-app.onrender.com/v1/portal/status`

### 2. Test Gateway Availability

```bash
# Check available gateways
curl https://your-app.onrender.com/v1/portal/status

# Expected response for Dominica:
{
  "gateways": {
    "btcpay": {"status": "available", "message": "Available globally"},
    "nowpayments": {"status": "available", "message": "Available globally"},
    "stripe": {"status": "limited", "message": "Not available in your region"},
    "square": {"status": "limited", "message": "Not available in your region"}
  },
  "region": "Dominica"
}
```

### 3. Create Your First Tenant

```bash
# Create tenant (admin operation)
curl -X POST "https://your-app.onrender.com/v1/tenants" \
  -H "Authorization: Admin eloh_admin_token_123" \
  -H "Content-Type: application/json" \
  -d '{
    "company_name": "Caribbean E-Store",
    "business_type": "E-commerce",
    "contact_email": "<EMAIL>",
    "contact_name": "Store Admin",
    "country": "DM",
    "plan": "professional"
  }'
```

### 4. Configure Tenant Gateways

```bash
# Configure BTCPay Server
curl -X POST "https://your-app.onrender.com/v1/tenants/me/gateways" \
  -H "Authorization: Bearer tenant_api_key" \
  -H "Content-Type: application/json" \
  -d '{
    "gateway_id": "btcpay",
    "enabled": true,
    "credentials": {
      "server_url": "https://your-btcpay.com",
      "api_key": "your_api_key",
      "store_id": "your_store_id"
    },
    "priority": 1
  }'
```

## 💰 Render Pricing

### Free Tier Limits:
- **Web Service**: 750 hours/month (enough for testing)
- **PostgreSQL**: 1GB storage, 1 month retention
- **Bandwidth**: 100GB/month
- **Build Time**: 500 minutes/month

### Paid Plans (Recommended for Production):
- **Starter**: $7/month - Always on, custom domains
- **Standard**: $25/month - More resources, better performance
- **Pro**: $85/month - High performance, priority support

### Database Pricing:
- **Free**: 1GB storage
- **Starter**: $7/month - 10GB storage
- **Standard**: $20/month - 100GB storage

## 🔒 Security Best Practices

### 1. Environment Variables
- Never commit API keys to Git
- Use Render's environment variable encryption
- Rotate keys regularly

### 2. Database Security
- Use strong passwords
- Enable connection encryption
- Regular backups

### 3. API Security
- Enable rate limiting
- Use HTTPS only
- Implement proper authentication

## 📊 Monitoring & Maintenance

### 1. Render Dashboard
- Monitor application logs
- Check resource usage
- Set up alerts

### 2. Health Checks
```bash
# Automated health check
curl https://your-app.onrender.com/health
```

### 3. Database Maintenance
```bash
# Check database status
curl https://your-app.onrender.com/v1/portal/status
```

## 🚨 Troubleshooting

### Common Issues:

1. **Build Fails**
   - Check `build.sh` permissions
   - Verify `requirements.txt`
   - Check Python version compatibility

2. **Database Connection Issues**
   - Verify `DATABASE_URL` is set
   - Check database service status
   - Review connection logs

3. **Gateway Not Available**
   - Verify credentials in environment variables
   - Check gateway service status
   - Review regional availability

### Debug Commands:

```bash
# Check logs in Render dashboard
# Or use Render CLI
render logs -s your-service-name

# Test database connection
curl https://your-app.onrender.com/health

# Check environment variables
# (View in Render dashboard under Settings)
```

## 🔄 Updates & Maintenance

### Automatic Deployments:
- Push to `main` branch triggers auto-deploy
- Render rebuilds and redeploys automatically
- Zero-downtime deployments

### Manual Deployments:
1. Go to Render dashboard
2. Click **"Manual Deploy"**
3. Select branch/commit
4. Deploy

### Database Migrations:
```bash
# Migrations run automatically during build
# Check build logs for migration status
```

## 📈 Scaling

### Horizontal Scaling:
- Upgrade to Standard/Pro plans
- Enable auto-scaling
- Add load balancing

### Database Scaling:
- Upgrade database plan
- Add read replicas
- Implement caching

## 🎉 Success!

Your ELOH Processing Payment Gateway is now live on Render! 

**Next Steps:**
1. Test all endpoints
2. Configure monitoring
3. Set up custom domain
4. Onboard your first tenants
5. Start processing payments

**Support:**
- Render Documentation: [render.com/docs](https://render.com/docs)
- ELOH Processing Support: [<EMAIL>](mailto:<EMAIL>)

---

**🚀 Your multi-tenant payment gateway is now globally accessible with enterprise-grade infrastructure!**
