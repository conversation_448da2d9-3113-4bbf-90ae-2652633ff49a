<?php
// BTCPay Server Troubleshooting and Setup Guide
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once "includes/btcpay-gateway.php";
include "header.php";
?>

<main>
  <section class="hero">
    <h1>BTCPay Server Troubleshooting</h1>
  </section>

  <section class="section" id="troubleshoot">
    <h2>🔧 Configuration Check</h2>
    
    <?php
    try {
        $gateway = new BTCPay_Gateway();
        $debug = $gateway->getDebugInfo();
        
        echo "<div style='background: #f9f9f9; padding: 20px; border-radius: 10px; margin-bottom: 30px;'>";
        echo "<h3>Current Configuration:</h3>";
        echo "<table style='width: 100%; border-collapse: collapse;'>";
        
        foreach ($debug as $key => $value) {
            $status = is_bool($value) ? ($value ? '✅ Yes' : '❌ No') : htmlspecialchars($value);
            $rowColor = (is_bool($value) && !$value) ? 'background: #f8d7da;' : '';
            echo "<tr style='$rowColor'>";
            echo "<td style='padding: 8px; border: 1px solid #ddd; font-weight: bold;'>" . ucfirst(str_replace('_', ' ', $key)) . ":</td>";
            echo "<td style='padding: 8px; border: 1px solid #ddd;'>$status</td>";
            echo "</tr>";
        }
        echo "</table>";
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin-bottom: 20px;'>";
        echo "<h3>❌ Configuration Error</h3>";
        echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
        echo "</div>";
    }
    ?>

    <h2>🚀 Quick Setup Guide</h2>
    
    <div style="background: #d4edda; color: #155724; padding: 20px; border-radius: 10px; margin-bottom: 30px;">
        <h3>For BTCPay Demo Server (mainnet.demo.btcpayserver.org):</h3>
        <ol>
            <li><strong>Create Account:</strong> Go to <a href="https://mainnet.demo.btcpayserver.org" target="_blank">mainnet.demo.btcpayserver.org</a></li>
            <li><strong>Create Store:</strong> After login, create a new store</li>
            <li><strong>Get Store ID:</strong> Store → Settings → General → Store ID</li>
            <li><strong>Create API Key:</strong> Account → API Keys → Generate Key with permissions:
                <ul style="margin: 10px 0; padding-left: 20px;">
                    <li>✅ View invoices</li>
                    <li>✅ Create invoice</li>
                    <li>✅ Modify invoices</li>
                    <li>✅ Modify stores webhooks</li>
                    <li>✅ View your stores</li>
                </ul>
            </li>
            <li><strong>Update Config:</strong> Edit <code>includes/btcpay-config.php</code> with your details</li>
        </ol>
    </div>

    <h2>🔍 Common Issues & Solutions</h2>
    
    <div style="margin-bottom: 30px;">
        <h3>❌ "Failed to create invoice - false"</h3>
        <div style="background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;">
            <p><strong>Causes:</strong></p>
            <ul>
                <li>Missing <code>https://</code> in host URL</li>
                <li>Invalid API key</li>
                <li>Wrong Store ID</li>
                <li>Insufficient API permissions</li>
            </ul>
            <p><strong>Solutions:</strong></p>
            <ul>
                <li>Ensure host URL starts with <code>https://</code></li>
                <li>Verify API key is correct and active</li>
                <li>Check Store ID matches your BTCPay store</li>
                <li>Regenerate API key with all required permissions</li>
            </ul>
        </div>
    </div>

    <div style="margin-bottom: 30px;">
        <h3>❌ "HTTP Error 401"</h3>
        <div style="background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;">
            <p><strong>Cause:</strong> Invalid or expired API key</p>
            <p><strong>Solution:</strong> Create a new API key in BTCPay Server</p>
        </div>
    </div>

    <div style="margin-bottom: 30px;">
        <h3>❌ "HTTP Error 403"</h3>
        <div style="background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;">
            <p><strong>Cause:</strong> API key lacks required permissions</p>
            <p><strong>Solution:</strong> Add missing permissions to your API key</p>
        </div>
    </div>

    <div style="margin-bottom: 30px;">
        <h3>❌ "Connection error"</h3>
        <div style="background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;">
            <p><strong>Causes:</strong></p>
            <ul>
                <li>BTCPay Server is down</li>
                <li>Network connectivity issues</li>
                <li>Firewall blocking requests</li>
            </ul>
            <p><strong>Solutions:</strong></p>
            <ul>
                <li>Check if BTCPay Server is accessible in browser</li>
                <li>Try from different network</li>
                <li>Contact hosting provider about firewall rules</li>
            </ul>
        </div>
    </div>

    <h2>🔗 Webhook Setup</h2>
    
    <div style="background: #e7f3ff; padding: 20px; border-radius: 10px; margin-bottom: 30px;">
        <h3>Webhook Configuration:</h3>
        <p><strong>Webhook URL:</strong> <code><?php echo htmlspecialchars($debug['webhook_url'] ?? 'Not configured'); ?></code></p>
        <p><strong>Required Events:</strong></p>
        <ul>
            <li>InvoiceCreated</li>
            <li>InvoiceReceivedPayment</li>
            <li>InvoicePaymentSettled</li>
            <li>InvoiceProcessing</li>
            <li>InvoiceExpired</li>
            <li>InvoiceSettled</li>
            <li>InvoiceInvalid</li>
        </ul>
        
        <?php if (isset($debug) && $debug['api_key_set']): ?>
        <form method="POST" style="margin-top: 20px;">
            <button type="submit" name="setup_webhook" class="cta-button">
                🔗 Setup Webhook Automatically
            </button>
        </form>
        
        <?php
        if (isset($_POST['setup_webhook'])) {
            try {
                $webhookResult = $gateway->setupWebhook();
                
                if ($webhookResult['success']) {
                    echo "<div style='background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin-top: 15px;'>";
                    echo "<h4>✅ Webhook Setup Successful!</h4>";
                    echo "<p>Webhook has been registered with BTCPay Server.</p>";
                    echo "</div>";
                } else {
                    echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin-top: 15px;'>";
                    echo "<h4>❌ Webhook Setup Failed</h4>";
                    echo "<p>Please set up the webhook manually in BTCPay Server.</p>";
                    echo "</div>";
                }
                
            } catch (Exception $e) {
                echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin-top: 15px;'>";
                echo "<h4>❌ Webhook Setup Error</h4>";
                echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
                echo "</div>";
            }
        }
        ?>
        <?php endif; ?>
    </div>

    <h2>📋 Manual Webhook Setup</h2>
    
    <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin-bottom: 30px;">
        <p>If automatic setup fails, configure manually in BTCPay Server:</p>
        <ol>
            <li>Go to your BTCPay Server store</li>
            <li>Navigate to <strong>Settings → Webhooks</strong></li>
            <li>Click <strong>Create Webhook</strong></li>
            <li>Enter webhook URL: <code><?php echo htmlspecialchars($debug['webhook_url'] ?? 'https://yoursite.infinityfreeapp.com/btcpay-webhook.php'); ?></code></li>
            <li>Select all invoice events</li>
            <li>Enter webhook secret: <code>6Rriupy3P7X5$j6</code></li>
            <li>Save webhook</li>
        </ol>
    </div>

    <h2>🧪 Test Your Setup</h2>
    
    <div style="text-align: center; margin: 30px 0;">
        <a href="btcpay-test.php" class="cta-button" style="margin-right: 10px;">
            🧪 Run Full Test Suite
        </a>
        <a href="btcpay-payment-form.php?type=donation&amount=0.01" class="cta-button">
            💰 Test $0.01 Payment
        </a>
    </div>

    <div style="text-align: center; margin-top: 30px;">
        <p><a href="index.php">← Back to Homepage</a></p>
    </div>
  </section>
</main>

<?php include "footer.php"; ?>
