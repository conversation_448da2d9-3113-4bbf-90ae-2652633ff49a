<?php
/**
 * NowPayments Configuration for ELOH Processing
 */

return [
    // API URLs
    'sandbox_url' => 'https://api-sandbox.nowpayments.io/v1',
    'live_url' => 'https://api.nowpayments.io/v1',

    // API Keys (Get from account-sandbox.nowpayments.io for testing)
    'sandbox_api_key' => 'your_sandbox_api_key_here', // Replace with sandbox API key
    'live_api_key' => '5S2CP2Q-XF34FM4-QAFZX3D-TF8ZTQV', // Replace with live API key

    // IPN Secret (for webhook validation)
    'ipn_secret' => 'ZnG4K9+sDy5Ls/u1Z33VAnunQBM+h0qv', // Replace with your IPN secret

    // Wallet Settings (where you want to receive payments)
    'outcome_wallet' => [
        'btc' => '******************************************',
        'eth' => '******************************************',
        'usdt' => '******************************************'
    ],

    // Default Settings
    'default_currency' => 'btc',
    'environment' => 'live', // 'sandbox' or 'live'

    // Supported currencies for your business
    'supported_currencies' => [
        'btc' => [
            'name' => 'Bitcoin',
            'symbol' => 'BTC',
            'icon' => '₿',
            'min_amount' => 0.0001
        ],
        'eth' => [
            'name' => 'Ethereum',
            'symbol' => 'ETH',
            'icon' => 'Ξ',
            'min_amount' => 0.001
        ],
        'usdt' => [
            'name' => 'Tether',
            'symbol' => 'USDT',
            'icon' => '₮',
            'min_amount' => 1
        ],
        'usdc' => [
            'name' => 'USD Coin',
            'symbol' => 'USDC',
            'icon' => '$',
            'min_amount' => 1
        ],
        'ltc' => [
            'name' => 'Litecoin',
            'symbol' => 'LTC',
            'icon' => 'Ł',
            'min_amount' => 0.01
        ],
        'bch' => [
            'name' => 'Bitcoin Cash',
            'symbol' => 'BCH',
            'icon' => '₿',
            'min_amount' => 0.001
        ],
        'trx' => [
            'name' => 'Tron',
            'symbol' => 'TRX',
            'icon' => 'T',
            'min_amount' => 10
        ],
        'bnb' => [
            'name' => 'Binance Coin',
            'symbol' => 'BNB',
            'icon' => 'B',
            'min_amount' => 0.01
        ]
    ]
];
