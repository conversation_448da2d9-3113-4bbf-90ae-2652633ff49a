"""
Webhook endpoints for ELOH Processing Payment Gateway API

This module implements webhook handling for payment gateway notifications.
It provides a unified interface for receiving and processing webhooks from
different payment gateways.
"""

from fastapi import APIRouter, Request, HTTPException, Header
from typing import Optional, Dict, Any
import logging
import json

from ...core.adapter_registry import get_adapter_registry
from ...core.exceptions import PaymentGatewayException, create_http_exception
from ...core.config import get_settings
from ...core.logging import log_payment_event

router = APIRouter()
logger = logging.getLogger(__name__)


@router.post("/webhooks/{gateway}")
async def handle_gateway_webhook(
    gateway: str,
    request: Request,
    signature: Optional[str] = Header(None, alias="stripe-signature"),
    square_signature: Optional[str] = Header(None, alias="x-square-signature"),
    btcpay_signature: Optional[str] = Header(None, alias="btcpay-sig")
) -> Dict[str, Any]:
    """
    Handle webhook notifications from payment gateways.
    
    This endpoint receives webhook notifications from different gateways
    and processes them using the appropriate adapter. Each gateway has
    its own signature verification method.
    
    Args:
        gateway: Gateway identifier (stripe, square, btcpay, nowpayments)
        request: FastAPI request object containing webhook payload
        signature: Stripe webhook signature
        square_signature: Square webhook signature
        btcpay_signature: BTCPay webhook signature
        
    Returns:
        Dict[str, Any]: Webhook processing result
    """
    try:
        logger.info(f"Received webhook from {gateway}")
        
        # Get raw webhook data
        webhook_data = await request.body()
        
        # Parse JSON if possible
        try:
            webhook_json = json.loads(webhook_data)
        except json.JSONDecodeError:
            webhook_json = {"raw_data": webhook_data.decode("utf-8")}
        
        # Determine the correct signature based on gateway
        webhook_signature = None
        if gateway == "stripe":
            webhook_signature = signature
        elif gateway == "square":
            webhook_signature = square_signature
        elif gateway == "btcpay":
            webhook_signature = btcpay_signature
        
        # Get adapter and process webhook
        registry = get_adapter_registry()
        
        if not registry.is_gateway_registered(gateway):
            raise PaymentGatewayException(
                f"Unknown gateway: {gateway}",
                error_code="UNKNOWN_GATEWAY",
                gateway=gateway
            )
        
        adapter_class = registry.get_adapter_class(gateway)
        settings = get_settings()
        gateway_credentials = settings.get_gateway_credentials(gateway)
        
        if not gateway_credentials:
            raise PaymentGatewayException(
                f"No credentials configured for gateway: {gateway}",
                error_code="GATEWAY_NOT_CONFIGURED",
                gateway=gateway
            )
        
        # Create adapter
        if gateway == "stripe":
            from ...adapters.stripe_adapter import StripeCredentials
            credentials = StripeCredentials(
                secret_key=gateway_credentials["secret_key"],
                publishable_key=gateway_credentials["publishable_key"],
                webhook_secret=gateway_credentials.get("webhook_secret")
            )
        elif gateway == "btcpay":
            from ...adapters.btcpay_adapter import BTCPayCredentials
            credentials = BTCPayCredentials(
                server_url=gateway_credentials["server_url"],
                api_key=gateway_credentials["api_key"],
                store_id=gateway_credentials["store_id"],
                webhook_secret=gateway_credentials.get("webhook_secret")
            )
        else:
            raise PaymentGatewayException(
                f"Adapter not implemented for gateway: {gateway}",
                error_code="ADAPTER_NOT_IMPLEMENTED",
                gateway=gateway
            )
        
        adapter = adapter_class(credentials)
        
        # Process webhook
        processed_event = await adapter.process_webhook(webhook_json, webhook_signature)
        
        # Log webhook event
        logger.info(
            f"Webhook processed successfully",
            extra={
                "gateway": gateway,
                "event_type": processed_event.get("event_type"),
                "event_id": processed_event.get("event_id")
            }
        )
        
        # Handle specific event types
        await handle_webhook_event(processed_event)
        
        return {
            "success": True,
            "gateway": gateway,
            "event_type": processed_event.get("event_type"),
            "processed_at": processed_event.get("created")
        }
        
    except PaymentGatewayException as e:
        logger.error(f"Webhook processing failed: {e.message}")
        raise create_http_exception(e)
    except Exception as e:
        logger.error(f"Unexpected error processing webhook: {e}")
        raise HTTPException(
            status_code=500,
            detail={"error": {"code": "WEBHOOK_ERROR", "message": "Webhook processing failed"}}
        )


@router.post("/webhooks")
async def handle_generic_webhook(
    request: Request,
    gateway: Optional[str] = Header(None, alias="x-gateway"),
    signature: Optional[str] = Header(None, alias="x-signature")
) -> Dict[str, Any]:
    """
    Handle generic webhook notifications when gateway is specified in header.
    
    This endpoint provides an alternative webhook URL structure where the
    gateway is specified in the header instead of the URL path.
    """
    if not gateway:
        raise HTTPException(
            status_code=400,
            detail={"error": {"code": "MISSING_GATEWAY", "message": "Gateway header required"}}
        )
    
    # Forward to the specific gateway webhook handler
    return await handle_gateway_webhook(gateway, request, signature)


async def handle_webhook_event(event: Dict[str, Any]) -> None:
    """
    Handle processed webhook events and trigger appropriate business logic.
    
    This function processes standardized webhook events and triggers
    appropriate actions based on the event type.
    
    Args:
        event: Processed webhook event data
    """
    gateway = event.get("gateway")
    event_type = event.get("event_type")
    event_data = event.get("data", {})
    
    logger.info(f"Handling webhook event: {event_type} from {gateway}")
    
    try:
        # Handle payment-related events
        if "payment" in event_type.lower():
            await handle_payment_webhook_event(event)
        
        # Handle customer-related events
        elif "customer" in event_type.lower():
            await handle_customer_webhook_event(event)
        
        # Handle invoice-related events (BTCPay)
        elif "invoice" in event_type.lower():
            await handle_invoice_webhook_event(event)
        
        # Handle refund-related events
        elif "refund" in event_type.lower():
            await handle_refund_webhook_event(event)
        
        else:
            logger.info(f"Unhandled webhook event type: {event_type}")
    
    except Exception as e:
        logger.error(f"Error handling webhook event {event_type}: {e}")


async def handle_payment_webhook_event(event: Dict[str, Any]) -> None:
    """Handle payment-related webhook events"""
    gateway = event.get("gateway")
    event_type = event.get("event_type")
    event_data = event.get("data", {})
    
    # Extract payment information based on gateway
    payment_id = None
    status = None
    amount = None
    currency = None
    
    if gateway == "stripe":
        # Handle Stripe payment events
        if "payment_intent" in event_type:
            payment_intent = event_data.get("object", {})
            payment_id = f"stripe_{payment_intent.get('id')}"
            status = payment_intent.get("status")
            amount = payment_intent.get("amount", 0) / 100  # Convert from cents
            currency = payment_intent.get("currency", "").upper()
    
    elif gateway == "btcpay":
        # Handle BTCPay invoice events
        invoice_data = event_data
        payment_id = f"btcpay_{invoice_data.get('invoice_id')}"
        status = invoice_data.get("status")
        amount = invoice_data.get("amount")
        currency = invoice_data.get("currency")
    
    if payment_id:
        # Log payment status change
        log_payment_event(
            logger,
            event_type="payment_status_changed",
            payment_id=payment_id,
            gateway=gateway,
            amount=amount or 0,
            currency=currency or "USD",
            status=status or "unknown",
            webhook_event_type=event_type
        )
        
        # Here you would typically:
        # 1. Update payment status in your database
        # 2. Send notifications to merchants
        # 3. Trigger business logic (fulfillment, etc.)
        # 4. Update analytics and reporting


async def handle_customer_webhook_event(event: Dict[str, Any]) -> None:
    """Handle customer-related webhook events"""
    gateway = event.get("gateway")
    event_type = event.get("event_type")
    
    logger.info(f"Processing customer webhook: {event_type} from {gateway}")
    
    # Handle customer creation, updates, deletion
    # This would typically update customer records in your database


async def handle_invoice_webhook_event(event: Dict[str, Any]) -> None:
    """Handle invoice-related webhook events (primarily BTCPay)"""
    gateway = event.get("gateway")
    event_type = event.get("event_type")
    
    logger.info(f"Processing invoice webhook: {event_type} from {gateway}")
    
    # Handle BTCPay invoice events
    # This is similar to payment events but specific to BTCPay's invoice model


async def handle_refund_webhook_event(event: Dict[str, Any]) -> None:
    """Handle refund-related webhook events"""
    gateway = event.get("gateway")
    event_type = event.get("event_type")
    
    logger.info(f"Processing refund webhook: {event_type} from {gateway}")
    
    # Handle refund status changes
    # Update refund records and notify relevant parties
