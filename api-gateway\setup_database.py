#!/usr/bin/env python3
"""
Database setup script for ELOH Processing Multi-Tenant Payment Gateway

This script initializes the database, creates tables, and optionally
loads sample data for testing.
"""

import asyncio
import sys
import os
from pathlib import Path

# Add the app directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

from app.database.connection import get_database_manager, init_database, reset_database
from app.database.models import Tenant, GatewayConfig, Payment, Customer, UsageStats
from app.services.tenant_service import get_tenant_service
from app.models.tenant import TenantRequest, TenantPlan, GatewayConfigurationRequest
from app.core.logging import setup_logging, get_logger

# Setup logging
setup_logging()
logger = get_logger(__name__)


async def create_sample_tenant():
    """Create a sample tenant for testing"""
    try:
        tenant_service = get_tenant_service()
        
        # Create sample tenant
        tenant_request = TenantRequest(
            company_name="Demo E-commerce Store",
            business_type="E-commerce",
            website_url="https://demo-store.example.com",
            contact_email="<EMAIL>",
            contact_name="Demo Admin",
            contact_phone="******-DEMO-123",
            address_line1="123 Demo Street",
            city="Demo City",
            state="Demo State",
            postal_code="12345",
            country="DM",  # Dominica
            plan=TenantPlan.PROFESSIONAL,
            expected_monthly_volume=25000.0,
            preferred_gateways=["btcpay", "nowpayments"],
            required_currencies=["USD", "BTC", "ETH"],
            metadata={
                "demo": True,
                "created_by": "setup_script"
            }
        )
        
        tenant = await tenant_service.create_tenant(tenant_request)
        logger.info(f"Sample tenant created: {tenant.tenant_id}")
        logger.info(f"API Key: {tenant.api_key}")
        
        # Configure BTCPay Server gateway
        btcpay_config = GatewayConfigurationRequest(
            gateway_id="btcpay",
            enabled=True,
            credentials={
                "server_url": "https://demo.btcpayserver.org",
                "api_key": "demo_api_key_replace_with_real",
                "store_id": "demo_store_id_replace_with_real",
                "webhook_secret": "demo_webhook_secret"
            },
            configuration={
                "speed_policy": "MediumSpeed",
                "expiration_minutes": 60,
                "monitoring_minutes": 1440
            },
            priority=1,
            min_amount=0.01,
            max_amount=10000.0,
            daily_limit=50000.0,
            webhook_url="https://demo-store.example.com/webhooks/btcpay",
            webhook_events=["invoice_settled", "invoice_expired", "invoice_invalid"]
        )
        
        await tenant_service.configure_gateway(tenant.tenant_id, btcpay_config)
        logger.info("BTCPay Server gateway configured")
        
        # Configure NowPayments gateway
        nowpayments_config = GatewayConfigurationRequest(
            gateway_id="nowpayments",
            enabled=True,
            credentials={
                "api_key": "demo_nowpayments_api_key_replace_with_real",
                "ipn_secret": "demo_ipn_secret",
                "environment": "sandbox"
            },
            configuration={
                "default_pay_currency": "btc"
            },
            priority=2,
            min_amount=1.0,
            max_amount=25000.0,
            daily_limit=100000.0,
            webhook_url="https://demo-store.example.com/webhooks/nowpayments",
            webhook_events=["payment.finished", "payment.failed", "payment.refunded"]
        )
        
        await tenant_service.configure_gateway(tenant.tenant_id, nowpayments_config)
        logger.info("NowPayments gateway configured")
        
        return tenant
        
    except Exception as e:
        logger.error(f"Failed to create sample tenant: {e}")
        return None


def create_database_indexes():
    """Create additional database indexes for performance"""
    try:
        db_manager = get_database_manager()
        
        with db_manager.session_scope() as session:
            # Additional indexes for better query performance
            indexes = [
                "CREATE INDEX IF NOT EXISTS idx_payments_tenant_status ON payments(tenant_id, status);",
                "CREATE INDEX IF NOT EXISTS idx_payments_created_desc ON payments(created_at DESC);",
                "CREATE INDEX IF NOT EXISTS idx_payments_amount_range ON payments(amount) WHERE amount > 100;",
                "CREATE INDEX IF NOT EXISTS idx_gateway_configs_tenant_enabled ON gateway_configs(tenant_id, enabled);",
                "CREATE INDEX IF NOT EXISTS idx_tenants_plan_status ON tenants(plan, status);",
                "CREATE INDEX IF NOT EXISTS idx_usage_stats_period ON usage_stats(tenant_id, period_type, period_start DESC);"
            ]
            
            for index_sql in indexes:
                try:
                    session.execute(index_sql)
                    logger.debug(f"Created index: {index_sql}")
                except Exception as e:
                    logger.warning(f"Index creation failed (may already exist): {e}")
        
        logger.info("Database indexes created successfully")
        
    except Exception as e:
        logger.error(f"Failed to create database indexes: {e}")


def setup_database_triggers():
    """Setup database triggers for automatic updates (SQLite specific)"""
    try:
        db_manager = get_database_manager()
        
        # Only create triggers for SQLite
        if not str(db_manager.engine.url).startswith("sqlite"):
            logger.info("Skipping triggers setup (not SQLite)")
            return
        
        with db_manager.session_scope() as session:
            triggers = [
                # Update tenant usage stats when payment is created
                """
                CREATE TRIGGER IF NOT EXISTS update_tenant_stats_on_payment
                AFTER INSERT ON payments
                BEGIN
                    UPDATE tenants 
                    SET total_transactions = total_transactions + 1,
                        total_volume = total_volume + NEW.amount,
                        monthly_volume = monthly_volume + NEW.amount,
                        last_activity = datetime('now')
                    WHERE id = NEW.tenant_id;
                END;
                """,
                
                # Update gateway config stats when payment is created
                """
                CREATE TRIGGER IF NOT EXISTS update_gateway_stats_on_payment
                AFTER INSERT ON payments
                BEGIN
                    UPDATE gateway_configs 
                    SET total_transactions = total_transactions + 1,
                        total_volume = total_volume + NEW.amount,
                        last_used = datetime('now')
                    WHERE id = NEW.gateway_config_id;
                END;
                """,
                
                # Update tenant updated_at timestamp
                """
                CREATE TRIGGER IF NOT EXISTS update_tenant_timestamp
                AFTER UPDATE ON tenants
                BEGIN
                    UPDATE tenants SET updated_at = datetime('now') WHERE id = NEW.id;
                END;
                """
            ]
            
            for trigger_sql in triggers:
                try:
                    session.execute(trigger_sql)
                    logger.debug("Created trigger")
                except Exception as e:
                    logger.warning(f"Trigger creation failed: {e}")
        
        logger.info("Database triggers created successfully")
        
    except Exception as e:
        logger.error(f"Failed to create database triggers: {e}")


def print_database_info():
    """Print database information and statistics"""
    try:
        db_manager = get_database_manager()
        
        with db_manager.session_scope() as session:
            # Count records in each table
            tables = [
                ("Tenants", Tenant),
                ("Gateway Configs", GatewayConfig),
                ("Payments", Payment),
                ("Customers", Customer),
                ("Usage Stats", UsageStats)
            ]
            
            print("\n" + "="*50)
            print("DATABASE INFORMATION")
            print("="*50)
            print(f"Database URL: {db_manager.engine.url}")
            print(f"Database Health: {'✅ Healthy' if db_manager.health_check() else '❌ Unhealthy'}")
            print("\nTable Statistics:")
            print("-"*30)
            
            for table_name, model_class in tables:
                try:
                    count = session.query(model_class).count()
                    print(f"{table_name:15}: {count:>6} records")
                except Exception as e:
                    print(f"{table_name:15}: Error - {e}")
            
            print("="*50)
        
    except Exception as e:
        logger.error(f"Failed to get database info: {e}")


async def main():
    """Main setup function"""
    print("🚀 ELOH Processing Payment Gateway - Database Setup")
    print("="*60)
    
    # Parse command line arguments
    import argparse
    parser = argparse.ArgumentParser(description="Setup ELOH Processing Payment Gateway Database")
    parser.add_argument("--reset", action="store_true", help="Reset database (drop and recreate tables)")
    parser.add_argument("--sample-data", action="store_true", help="Create sample tenant data")
    parser.add_argument("--info", action="store_true", help="Show database information")
    
    args = parser.parse_args()
    
    try:
        # Initialize database
        if args.reset:
            print("⚠️  Resetting database (dropping all tables)...")
            reset_database()
            print("✅ Database reset complete")
        else:
            print("📊 Initializing database...")
            init_database()
            print("✅ Database initialized")
        
        # Create indexes and triggers
        print("🔧 Creating database indexes...")
        create_database_indexes()
        
        print("🔧 Setting up database triggers...")
        setup_database_triggers()
        
        # Create sample data if requested
        if args.sample_data:
            print("📝 Creating sample tenant data...")
            sample_tenant = await create_sample_tenant()
            if sample_tenant:
                print(f"✅ Sample tenant created: {sample_tenant.company_name}")
                print(f"   Tenant ID: {sample_tenant.tenant_id}")
                print(f"   API Key: {sample_tenant.api_key}")
                print(f"   Plan: {sample_tenant.plan}")
                print(f"   Configured Gateways: {len(sample_tenant.gateways)}")
        
        # Show database info
        if args.info or not any([args.reset, args.sample_data]):
            print_database_info()
        
        print("\n🎉 Database setup completed successfully!")
        print("\n📚 Next Steps:")
        print("1. Update .env file with your gateway credentials")
        print("2. Start the API server: uvicorn main:app --reload")
        print("3. Visit http://localhost:8000/v1/portal for tenant portal")
        print("4. Visit http://localhost:8000/docs for API documentation")
        
        if args.sample_data:
            print(f"\n🔑 Sample Tenant API Key: {sample_tenant.api_key}")
            print("   Use this key to test the API endpoints")
        
    except Exception as e:
        logger.error(f"Database setup failed: {e}")
        print(f"\n❌ Database setup failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
