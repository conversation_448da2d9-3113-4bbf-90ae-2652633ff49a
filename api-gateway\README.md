# 🚀 ELOH Processing Multi-Tenant Payment Gateway API

A comprehensive **multi-tenant payment gateway service** built with FastAPI that enables ELOH Processing to offer payment gateway services to multiple customers (tenants). Each tenant gets their own isolated configuration, gateway credentials, and usage tracking while sharing the same robust infrastructure.

## ✨ Features

### 🏢 Multi-Tenant Architecture
- **Tenant Management**: Complete customer onboarding and management system
- **Isolated Configurations**: Each tenant has separate gateway configurations
- **Encrypted Credentials**: Secure storage of tenant gateway credentials
- **Usage Tracking**: Per-tenant transaction volume and analytics
- **Plan-Based Limits**: Configurable limits based on service plans

### 🌐 Payment Gateway Support
- **Stripe**: Credit cards, ACH, international payments, subscriptions
- **Square**: In-person and online payments, invoicing, gift cards
- **BTCPay Server**: Bitcoin, Lightning Network, self-hosted crypto payments
- **NowPayments**: 300+ cryptocurrencies, instant settlements

### 🧠 Intelligent Routing
- **Rule-Based Routing**: Configurable routing based on multiple factors
- **Tenant-Specific Rules**: Custom routing per tenant configuration
- **AI-Ready Architecture**: Built for future AI-driven routing
- **Fallback Mechanisms**: Reliable rule-based fallback for AI systems

### 🔒 Security & Compliance
- **API Key Authentication**: Secure tenant authentication system
- **Credential Encryption**: Military-grade encryption for gateway credentials
- **Webhook Verification**: Secure webhook signature validation
- **Admin Controls**: Separate admin interface for tenant management

### 📊 Analytics & Monitoring
- **Real-Time Usage**: Live transaction tracking and volume monitoring
- **Per-Tenant Analytics**: Detailed statistics for each tenant
- **Gateway Performance**: Health monitoring across all gateways
- **Structured Logging**: Comprehensive audit trails and debugging

## 🏗️ Multi-Tenant Architecture

### System Overview

The multi-tenant architecture enables ELOH Processing to serve multiple customers while maintaining complete isolation:

```
┌─────────────────────────────────────────────────────────────────┐
│                    ELOH Processing Platform                     │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐              │
│  │   Tenant A  │  │   Tenant B  │  │   Tenant C  │   ...        │
│  │   (E-comm)  │  │   (SaaS)    │  │   (Crypto)  │              │
│  └─────────────┘  └─────────────┘  └─────────────┘              │
├─────────────────────────────────────────────────────────────────┤
│                    Tenant Management Layer                      │
│  • Authentication  • Configuration  • Usage Tracking           │
├─────────────────────────────────────────────────────────────────┤
│                    Payment Routing Engine                       │
│  • Rule-Based Routing  • AI-Ready  • Tenant-Specific Rules     │
├─────────────────────────────────────────────────────────────────┤
│                    Gateway Adapter Layer                        │
│  ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌─────────────┐           │
│  │ Stripe  │ │ Square  │ │ BTCPay  │ │ NowPayments │           │
│  └─────────┘ └─────────┘ └─────────┘ └─────────────┘           │
└─────────────────────────────────────────────────────────────────┘
```

### Tenant Isolation

Each tenant operates in complete isolation:

- **Separate API Keys**: Unique authentication per tenant
- **Isolated Credentials**: Encrypted gateway credentials per tenant
- **Custom Routing**: Tenant-specific gateway preferences and rules
- **Usage Tracking**: Independent transaction monitoring and limits
- **Plan-Based Access**: Different feature sets based on service plans

### Rule-Based Routing

The routing system considers multiple factors:

1. **User Preferences**: Preferred gateways and priority lists
2. **Payment Characteristics**: Amount, currency, payment method
3. **Gateway Capabilities**: Supported currencies and features
4. **Performance Metrics**: Health scores and current load
5. **Business Rules**: Amount thresholds and regional restrictions

### Future AI Integration

The system is designed for seamless AI integration:

- **Standardized Data**: All routing decisions use consistent data structures
- **Fallback Mechanism**: Rule-based routing serves as AI fallback
- **Feature Engineering**: Rich context data for ML model training
- **A/B Testing**: Built-in support for routing strategy comparison

## 🚀 Quick Start

### 1. Installation

```bash
# Clone the repository
git clone https://github.com/elohprocessing/payment-gateway-api.git
cd payment-gateway-api

# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt
```

### 2. Configuration

```bash
# Copy environment template
cp .env.example .env

# Edit configuration
nano .env
```

Configure your payment gateway credentials:

```env
# Stripe
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key

# BTCPay Server
BTCPAY_SERVER_URL=https://your-btcpay-server.com
BTCPAY_API_KEY=your_btcpay_api_key
BTCPAY_STORE_ID=your_btcpay_store_id
```

### 3. Run the API

```bash
# Development mode
uvicorn main:app --reload --host 0.0.0.0 --port 8000

# Production mode
uvicorn main:app --host 0.0.0.0 --port 8000 --workers 4
```

### 4. API Documentation

Visit the interactive API documentation:

- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc

## 📖 Multi-Tenant API Usage

### Admin Operations (ELOH Processing Internal)

#### Create New Tenant

```bash
curl -X POST "http://localhost:8000/v1/tenants" \
  -H "Authorization: Admin eloh_admin_token_123" \
  -H "Content-Type: application/json" \
  -d '{
    "company_name": "Acme E-commerce",
    "business_type": "E-commerce",
    "website_url": "https://acme-store.com",
    "contact_email": "<EMAIL>",
    "contact_name": "John Smith",
    "contact_phone": "******-123-4567",
    "address_line1": "123 Business St",
    "city": "San Francisco",
    "state": "CA",
    "postal_code": "94102",
    "country": "US",
    "plan": "professional",
    "expected_monthly_volume": 50000.0,
    "preferred_gateways": ["stripe", "square"],
    "required_currencies": ["USD", "EUR"]
  }'
```

#### Get Tenant Information (Admin)

```bash
curl -X GET "http://localhost:8000/v1/tenants/tenant_abc123" \
  -H "Authorization: Admin eloh_admin_token_123"
```

### Tenant Operations

#### Configure Payment Gateway

```bash
curl -X POST "http://localhost:8000/v1/tenants/me/gateways" \
  -H "Authorization: Bearer eloh_tenant_api_key_xyz789" \
  -H "Content-Type: application/json" \
  -d '{
    "gateway_id": "stripe",
    "enabled": true,
    "credentials": {
      "secret_key": "sk_live_your_stripe_secret_key",
      "publishable_key": "pk_live_your_stripe_publishable_key",
      "webhook_secret": "whsec_your_webhook_secret"
    },
    "priority": 1,
    "min_amount": 0.50,
    "max_amount": 10000.0,
    "daily_limit": 50000.0,
    "webhook_url": "https://your-site.com/webhooks/stripe",
    "webhook_events": ["payment_intent.succeeded", "payment_intent.payment_failed"]
  }'
```

#### Get Tenant Usage Statistics

```bash
curl -X GET "http://localhost:8000/v1/tenants/me/usage?period_days=30" \
  -H "Authorization: Bearer eloh_tenant_api_key_xyz789"
```

### Payment Processing (Tenant-Authenticated)

#### Create Payment

```bash
curl -X POST "http://localhost:8000/v1/payments" \
  -H "Authorization: Bearer eloh_tenant_api_key_xyz789" \
  -H "Content-Type: application/json" \
  -d '{
    "amount": 100.00,
    "currency": "USD",
    "description": "Order #12345",
    "email": "<EMAIL>",
    "metadata": {
      "order_id": "12345",
      "customer_id": "cust_abc123"
    }
  }'
```

#### Get Payment Status

```bash
curl -X GET "http://localhost:8000/v1/payments/stripe_pi_1234567890" \
  -H "Authorization: Bearer eloh_tenant_api_key_xyz789"
```

#### Process Refund

```bash
curl -X POST "http://localhost:8000/v1/payments/stripe_pi_1234567890/refund" \
  -H "Authorization: Bearer eloh_tenant_api_key_xyz789" \
  -H "Content-Type: application/json" \
  -d '{
    "payment_id": "stripe_pi_1234567890",
    "amount": 50.00,
    "reason": "Customer request"
  }'
```

#### List Available Gateways

```bash
curl -X GET "http://localhost:8000/v1/gateways" \
  -H "Authorization: Bearer eloh_tenant_api_key_xyz789"
```

## 🔧 Gateway Configuration

### Stripe

```python
from app.adapters.stripe_adapter import StripeAdapter, StripeCredentials

credentials = StripeCredentials(
    secret_key="sk_test_...",
    publishable_key="pk_test_...",
    webhook_secret="whsec_..."
)

adapter = StripeAdapter(credentials)
```

### BTCPay Server

```python
from app.adapters.btcpay_adapter import BTCPayAdapter, BTCPayCredentials

credentials = BTCPayCredentials(
    server_url="https://btcpay.example.com",
    api_key="your_api_key",
    store_id="your_store_id"
)

adapter = BTCPayAdapter(credentials)
```

## 🔀 Routing Configuration

### Rule-Based Routing

```python
from app.core.routing import UserGatewayConfig, RoutingStrategy

user_config = UserGatewayConfig(
    user_id="user123",
    enabled_gateways=["stripe", "btcpay"],
    preferred_gateway="stripe",
    currency_preferences={
        "USD": "stripe",
        "BTC": "btcpay"
    },
    routing_strategy=RoutingStrategy.RULE_BASED
)
```

### Future AI Routing

```python
# AI routing will be enabled with:
user_config = UserGatewayConfig(
    routing_strategy=RoutingStrategy.AI_DRIVEN
)

# With automatic fallback to rule-based routing
```

## 🔌 Adding New Gateways

### 1. Create Adapter

```python
from app.adapters.gateway_adapter import GatewayAdapter, GatewayCredentials

class NewGatewayCredentials(GatewayCredentials):
    def __init__(self, api_key: str):
        self.api_key = api_key

    def is_valid(self) -> bool:
        return bool(self.api_key)

class NewGatewayAdapter(GatewayAdapter):
    def _get_gateway_name(self) -> str:
        return "newgateway"

    async def process_payment(self, payment_request):
        # Implementation here
        pass
```

### 2. Register Adapter

```python
from app.core.adapter_registry import get_adapter_registry

registry = get_adapter_registry()
registry.register_adapter("newgateway", NewGatewayAdapter, {
    "name": "New Gateway",
    "supported_currencies": ["USD", "EUR"],
    "supported_methods": ["card"]
})
```

## 🔍 Monitoring & Observability

### Health Check

```bash
curl -X GET "http://localhost:8000/health"
```

### Metrics

```bash
curl -X GET "http://localhost:8000/metrics"
```

### Structured Logging

The API provides comprehensive structured logging:

```json
{
  "timestamp": "2024-01-01T12:00:00Z",
  "level": "INFO",
  "logger": "app.api.v1.payments",
  "message": "Payment routed to stripe",
  "extra": {
    "event_type": "routing_decision",
    "payment_id": "pending_1704110400",
    "selected_gateway": "stripe",
    "routing_strategy": "rule_based"
  }
}
```

## 🧪 Testing

```bash
# Run tests
pytest

# Run with coverage
pytest --cov=app --cov-report=html

# Run specific test
pytest tests/test_routing.py::test_rule_based_routing
```

## 🚀 Deployment

### Docker

```dockerfile
FROM python:3.11-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
```

### Environment Variables

Ensure all required environment variables are set in production:

- Gateway credentials (encrypted)
- Database connection strings
- Redis configuration
- Monitoring endpoints

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Implement your changes
4. Add tests
5. Submit a pull request

## 📄 License

MIT License - see LICENSE file for details.

## 🆘 Support

- **Documentation**: https://docs.elohprocessing.com
- **Issues**: https://github.com/elohprocessing/payment-gateway-api/issues
- **Email**: <EMAIL>

---

**Built with ❤️ by ELOH Processing**
