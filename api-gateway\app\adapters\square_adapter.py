"""
Square Payment Gateway Adapter for ELOH Processing

This adapter implements the GatewayAdapter interface for Square payments.
Handles credit card payments, in-person payments, and digital wallets through Square's API.
"""

import uuid
import time
import hmac
import hashlib
import base64
from typing import Dict, Any, Optional, List
from decimal import Decimal
from datetime import datetime
import logging

from squareup import Client
from squareup.models import CreatePaymentRequest, Money
from squareup.exceptions import ApiException

from .gateway_adapter import GatewayAdapter, GatewayCredentials
from ..models.payment import PaymentRequest, PaymentResponse, PaymentStatus, RefundRequest, RefundResponse
from ..models.customer import CustomerRequest, CustomerResponse
from ..core.exceptions import PaymentGatewayException

logger = logging.getLogger(__name__)


class SquareCredentials(GatewayCredentials):
    """Square-specific credentials"""
    
    def __init__(
        self, 
        application_id: str, 
        access_token: str, 
        location_id: str,
        environment: str = "sandbox",
        webhook_signature_key: Optional[str] = None
    ):
        self.application_id = application_id
        self.access_token = access_token
        self.location_id = location_id
        self.environment = environment  # "sandbox" or "production"
        self.webhook_signature_key = webhook_signature_key
    
    def is_valid(self) -> bool:
        """Validate Square credentials"""
        return bool(
            self.application_id and 
            self.access_token and 
            self.location_id and
            self.environment in ["sandbox", "production"]
        )
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for secure storage"""
        return {
            "application_id": self.application_id,
            "access_token": self.access_token,
            "location_id": self.location_id,
            "environment": self.environment,
            "webhook_signature_key": self.webhook_signature_key
        }


class SquareAdapter(GatewayAdapter):
    """
    Square payment gateway adapter.
    
    Handles credit card payments, in-person payments, and digital wallets
    through Square's Payments API. Supports both online and offline transactions.
    """
    
    def __init__(self, credentials: SquareCredentials, config: Optional[Dict[str, Any]] = None):
        super().__init__(credentials, config)
        
        # Initialize Square client
        self.client = Client(
            access_token=credentials.access_token,
            environment=credentials.environment
        )
        
        self.location_id = credentials.location_id
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        
        # API clients
        self.payments_api = self.client.payments
        self.customers_api = self.client.customers
        self.refunds_api = self.client.refunds
    
    def _get_gateway_name(self) -> str:
        """Return gateway name"""
        return "square"
    
    async def validate_credentials(self) -> bool:
        """Validate Square credentials by checking location access"""
        try:
            locations_api = self.client.locations
            result = locations_api.retrieve_location(location_id=self.location_id)
            
            if result.is_success():
                return True
            else:
                self.logger.error(f"Square credential validation failed: {result.errors}")
                return False
                
        except Exception as e:
            self.logger.error(f"Square credential validation error: {e}")
            return False
    
    async def process_payment(self, payment_request: PaymentRequest, customer_id: Optional[str] = None) -> PaymentResponse:
        """Process payment through Square"""
        try:
            self.logger.info(f"Processing Square payment for amount {payment_request.amount} {payment_request.currency}")
            
            # Generate idempotency key
            idempotency_key = f"eloh_{int(time.time())}_{uuid.uuid4().hex[:8]}"
            
            # Convert amount to cents (Square uses smallest currency unit)
            amount_cents = int(payment_request.amount * 100)
            
            # Create Money object
            amount_money = Money(
                amount=amount_cents,
                currency=payment_request.currency
            )
            
            # Prepare payment request
            # Note: In a real implementation, you'd need to handle card nonces from the frontend
            # For now, we'll create a structure that would work with proper card nonces
            create_payment_request = CreatePaymentRequest(
                source_id=payment_request.metadata.get("source_id", "card-nonce-from-frontend"),
                idempotency_key=idempotency_key,
                amount_money=amount_money,
                location_id=self.location_id
            )
            
            # Add optional fields
            if payment_request.reference:
                create_payment_request.reference_id = payment_request.reference
            
            if payment_request.description:
                create_payment_request.note = payment_request.description
            
            if customer_id:
                create_payment_request.customer_id = customer_id
            
            # Create payment
            result = self.payments_api.create_payment(body=create_payment_request)
            
            if result.is_success():
                payment = result.body.get('payment', {})
                return self._convert_square_payment_to_response(payment, payment_request)
            else:
                error_details = result.errors[0] if result.errors else {}
                error_message = error_details.get('detail', 'Payment creation failed')
                raise PaymentGatewayException(f"Square payment failed: {error_message}")
                
        except ApiException as e:
            self.logger.error(f"Square API error: {e}")
            raise PaymentGatewayException(f"Square payment failed: {str(e)}")
        except Exception as e:
            self.logger.error(f"Unexpected error processing Square payment: {e}")
            raise PaymentGatewayException(f"Payment processing failed: {str(e)}")
    
    async def get_payment_status(self, gateway_payment_id: str) -> PaymentResponse:
        """Get payment status from Square"""
        try:
            result = self.payments_api.get_payment(payment_id=gateway_payment_id)
            
            if result.is_success():
                payment = result.body.get('payment', {})
                return self._convert_square_payment_to_response(payment)
            else:
                error_details = result.errors[0] if result.errors else {}
                error_message = error_details.get('detail', 'Payment not found')
                raise PaymentGatewayException(f"Failed to retrieve payment: {error_message}")
                
        except ApiException as e:
            self.logger.error(f"Square API error retrieving payment: {e}")
            raise PaymentGatewayException(f"Failed to retrieve payment: {str(e)}")
    
    async def refund_payment(self, refund_request: RefundRequest, gateway_payment_id: str) -> RefundResponse:
        """Process refund through Square"""
        try:
            # Generate idempotency key for refund
            idempotency_key = f"refund_{int(time.time())}_{uuid.uuid4().hex[:8]}"
            
            # Prepare refund request
            refund_data = {
                "idempotency_key": idempotency_key,
                "payment_id": gateway_payment_id
            }
            
            # Add amount if partial refund
            if refund_request.amount:
                amount_cents = int(refund_request.amount * 100)
                refund_data["amount_money"] = {
                    "amount": amount_cents,
                    "currency": "USD"  # Would need to get from original payment
                }
            
            # Add reason if provided
            if refund_request.reason:
                refund_data["reason"] = refund_request.reason
            
            # Create refund
            result = self.refunds_api.create_refund(body=refund_data)
            
            if result.is_success():
                refund = result.body.get('refund', {})
                return self._convert_square_refund_to_response(refund, refund_request)
            else:
                error_details = result.errors[0] if result.errors else {}
                error_message = error_details.get('detail', 'Refund creation failed')
                raise PaymentGatewayException(f"Square refund failed: {error_message}")
                
        except ApiException as e:
            self.logger.error(f"Square refund error: {e}")
            raise PaymentGatewayException(f"Refund failed: {str(e)}")
    
    async def create_customer(self, customer_request: CustomerRequest) -> CustomerResponse:
        """Create customer in Square"""
        try:
            customer_data = {
                "email_address": customer_request.email
            }
            
            # Add optional fields
            if customer_request.first_name:
                customer_data["given_name"] = customer_request.first_name
            
            if customer_request.last_name:
                customer_data["family_name"] = customer_request.last_name
            
            if customer_request.phone:
                customer_data["phone_number"] = customer_request.phone
            
            if customer_request.company:
                customer_data["company_name"] = customer_request.company
            
            if customer_request.address:
                customer_data["address"] = {
                    "address_line_1": customer_request.address.line1,
                    "address_line_2": customer_request.address.line2,
                    "locality": customer_request.address.city,
                    "administrative_district_level_1": customer_request.address.state,
                    "postal_code": customer_request.address.postal_code,
                    "country": customer_request.address.country
                }
            
            # Create Square customer
            result = self.customers_api.create_customer(body=customer_data)
            
            if result.is_success():
                customer = result.body.get('customer', {})
                return self._convert_square_customer_to_response(customer, customer_request)
            else:
                error_details = result.errors[0] if result.errors else {}
                error_message = error_details.get('detail', 'Customer creation failed')
                raise PaymentGatewayException(f"Square customer creation failed: {error_message}")
                
        except ApiException as e:
            self.logger.error(f"Square customer creation error: {e}")
            raise PaymentGatewayException(f"Customer creation failed: {str(e)}")
    
    async def get_customer(self, gateway_customer_id: str) -> CustomerResponse:
        """Get customer from Square"""
        try:
            result = self.customers_api.retrieve_customer(customer_id=gateway_customer_id)
            
            if result.is_success():
                customer = result.body.get('customer', {})
                return self._convert_square_customer_to_response(customer)
            else:
                error_details = result.errors[0] if result.errors else {}
                error_message = error_details.get('detail', 'Customer not found')
                raise PaymentGatewayException(f"Customer retrieval failed: {error_message}")
                
        except ApiException as e:
            self.logger.error(f"Square customer retrieval error: {e}")
            raise PaymentGatewayException(f"Customer retrieval failed: {str(e)}")
    
    async def process_webhook(self, webhook_data: Dict[str, Any], signature: Optional[str] = None) -> Dict[str, Any]:
        """Process Square webhook"""
        try:
            # Verify webhook signature if available
            if self.credentials.webhook_signature_key and signature:
                if not self._verify_webhook_signature(webhook_data, signature):
                    raise PaymentGatewayException("Invalid webhook signature")
            
            # Extract event information
            event_type = webhook_data.get("type")
            event_id = webhook_data.get("event_id")
            merchant_id = webhook_data.get("merchant_id")
            location_id = webhook_data.get("location_id")
            
            return {
                "gateway": "square",
                "event_type": event_type,
                "event_id": event_id,
                "created": datetime.now(),
                "data": {
                    "merchant_id": merchant_id,
                    "location_id": location_id,
                    "event_data": webhook_data.get("data", {})
                },
                "raw_event": webhook_data
            }
            
        except Exception as e:
            self.logger.error(f"Square webhook processing error: {e}")
            raise PaymentGatewayException(f"Webhook processing failed: {str(e)}")
    
    async def get_supported_currencies(self) -> List[str]:
        """Get Square supported currencies"""
        return ["USD", "CAD", "GBP", "EUR", "AUD", "JPY"]
    
    async def get_supported_payment_methods(self) -> List[str]:
        """Get Square supported payment methods"""
        return ["card", "cash", "bank_transfer", "gift_card", "apple_pay", "google_pay", "afterpay"]
    
    # Helper methods
    
    def _verify_webhook_signature(self, webhook_data: Dict[str, Any], signature: str) -> bool:
        """Verify Square webhook signature"""
        try:
            # Square webhook signature verification
            webhook_body = str(webhook_data).encode('utf-8')
            expected_signature = base64.b64encode(
                hmac.new(
                    self.credentials.webhook_signature_key.encode('utf-8'),
                    webhook_body,
                    hashlib.sha1
                ).digest()
            ).decode('utf-8')
            
            return hmac.compare_digest(signature, expected_signature)
            
        except Exception as e:
            self.logger.error(f"Webhook signature verification error: {e}")
            return False
    
    def _convert_square_payment_to_response(self, payment: Dict[str, Any], original_request: Optional[PaymentRequest] = None) -> PaymentResponse:
        """Convert Square payment to our standard PaymentResponse"""
        
        # Map Square status to our standard status
        status_mapping = {
            "APPROVED": PaymentStatus.COMPLETED,
            "PENDING": PaymentStatus.PROCESSING,
            "COMPLETED": PaymentStatus.COMPLETED,
            "CANCELED": PaymentStatus.CANCELLED,
            "FAILED": PaymentStatus.FAILED
        }
        
        amount_money = payment.get("amount_money", {})
        amount = Decimal(amount_money.get("amount", 0)) / 100
        currency = amount_money.get("currency", "USD")
        
        return PaymentResponse(
            payment_id=f"square_{payment.get('id')}",
            gateway_payment_id=payment.get("id"),
            reference=payment.get("reference_id"),
            amount=amount,
            currency=currency,
            status=status_mapping.get(payment.get("status"), PaymentStatus.PENDING),
            gateway_used="square",
            created_at=datetime.fromisoformat(payment.get("created_at", datetime.now().isoformat()).replace("Z", "+00:00")),
            updated_at=datetime.fromisoformat(payment.get("updated_at", datetime.now().isoformat()).replace("Z", "+00:00")),
            gateway_response=payment
        )
    
    def _convert_square_refund_to_response(self, refund: Dict[str, Any], original_request: RefundRequest) -> RefundResponse:
        """Convert Square refund to our standard RefundResponse"""
        
        status_mapping = {
            "PENDING": PaymentStatus.PROCESSING,
            "APPROVED": PaymentStatus.REFUNDED,
            "REJECTED": PaymentStatus.FAILED,
            "FAILED": PaymentStatus.FAILED
        }
        
        amount_money = refund.get("amount_money", {})
        amount = Decimal(amount_money.get("amount", 0)) / 100
        
        return RefundResponse(
            refund_id=f"square_{refund.get('id')}",
            payment_id=original_request.payment_id,
            amount=amount,
            status=status_mapping.get(refund.get("status"), PaymentStatus.PROCESSING),
            gateway_used="square",
            created_at=datetime.fromisoformat(refund.get("created_at", datetime.now().isoformat()).replace("Z", "+00:00")),
            gateway_response=refund
        )
    
    def _convert_square_customer_to_response(self, customer: Dict[str, Any], original_request: Optional[CustomerRequest] = None) -> CustomerResponse:
        """Convert Square customer to our standard CustomerResponse"""
        
        return CustomerResponse(
            customer_id=f"square_{customer.get('id')}",
            gateway_customer_ids={"square": customer.get("id")},
            email=customer.get("email_address"),
            first_name=customer.get("given_name"),
            last_name=customer.get("family_name"),
            phone=customer.get("phone_number"),
            company=customer.get("company_name"),
            created_at=datetime.fromisoformat(customer.get("created_at", datetime.now().isoformat()).replace("Z", "+00:00")),
            updated_at=datetime.fromisoformat(customer.get("updated_at", datetime.now().isoformat()).replace("Z", "+00:00"))
        )
