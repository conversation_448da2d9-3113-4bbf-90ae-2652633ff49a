"""
NowPayments Gateway Adapter for ELOH Processing

This adapter implements the GatewayAdapter interface for NowPayments.
Handles 300+ cryptocurrency payments through NowPayments API.
"""

import aiohttp
import json
import hmac
import hashlib
from typing import Dict, Any, Optional, List
from decimal import Decimal
from datetime import datetime
import logging

from .gateway_adapter import GatewayAdapter, GatewayCredentials
from ..models.payment import PaymentRequest, PaymentResponse, PaymentStatus, RefundRequest, RefundResponse
from ..models.customer import CustomerRequest, CustomerResponse
from ..core.exceptions import PaymentGatewayException

logger = logging.getLogger(__name__)


class NowPaymentsCredentials(GatewayCredentials):
    """NowPayments-specific credentials"""
    
    def __init__(
        self, 
        api_key: str, 
        ipn_secret: Optional[str] = None,
        environment: str = "sandbox"
    ):
        self.api_key = api_key
        self.ipn_secret = ipn_secret
        self.environment = environment  # "sandbox" or "production"
    
    def is_valid(self) -> bool:
        """Validate NowPayments credentials"""
        return bool(self.api_key and self.environment in ["sandbox", "production"])
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for secure storage"""
        return {
            "api_key": self.api_key,
            "ipn_secret": self.ipn_secret,
            "environment": self.environment
        }


class NowPaymentsAdapter(GatewayAdapter):
    """
    NowPayments gateway adapter.
    
    Handles 300+ cryptocurrency payments through NowPayments API.
    Supports Bitcoin, Ethereum, Litecoin, and many other cryptocurrencies.
    """
    
    def __init__(self, credentials: NowPaymentsCredentials, config: Optional[Dict[str, Any]] = None):
        super().__init__(credentials, config)
        
        # API endpoints
        if credentials.environment == "sandbox":
            self.base_url = "https://api-sandbox.nowpayments.io/v1"
        else:
            self.base_url = "https://api.nowpayments.io/v1"
        
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        
        # Default configuration
        self.default_config = {
            "timeout": 30,
            "default_currency": "USD",
            "default_pay_currency": "btc"
        }
        self.config = {**self.default_config, **(config or {})}
    
    def _get_gateway_name(self) -> str:
        """Return gateway name"""
        return "nowpayments"
    
    async def validate_credentials(self) -> bool:
        """Validate NowPayments credentials by checking API status"""
        try:
            async with aiohttp.ClientSession() as session:
                headers = {
                    "x-api-key": self.credentials.api_key,
                    "Content-Type": "application/json"
                }
                
                url = f"{self.base_url}/status"
                async with session.get(url, headers=headers) as response:
                    return response.status == 200
                    
        except Exception as e:
            self.logger.error(f"NowPayments credential validation failed: {e}")
            return False
    
    async def process_payment(self, payment_request: PaymentRequest, customer_id: Optional[str] = None) -> PaymentResponse:
        """Process payment through NowPayments"""
        try:
            self.logger.info(f"Processing NowPayments payment for amount {payment_request.amount} {payment_request.currency}")
            
            # Determine pay currency (cryptocurrency to receive)
            pay_currency = payment_request.metadata.get("pay_currency", "btc")
            
            # Prepare payment data
            payment_data = {
                "price_amount": float(payment_request.amount),
                "price_currency": payment_request.currency,
                "pay_currency": pay_currency,
                "order_id": payment_request.reference or f"eloh_{int(datetime.now().timestamp())}",
                "order_description": payment_request.description or "Payment"
            }
            
            # Add optional fields
            if payment_request.success_url:
                payment_data["success_url"] = payment_request.success_url
            
            if payment_request.cancel_url:
                payment_data["cancel_url"] = payment_request.cancel_url
            
            if payment_request.webhook_url:
                payment_data["ipn_callback_url"] = payment_request.webhook_url
            
            if customer_id:
                payment_data["customer_email"] = customer_id
            
            # Create payment
            payment = await self._make_nowpayments_request(
                "POST",
                "/payment",
                data=payment_data
            )
            
            return self._convert_nowpayments_payment_to_response(payment, payment_request)
            
        except Exception as e:
            self.logger.error(f"NowPayments payment processing error: {e}")
            raise PaymentGatewayException(f"NowPayments payment failed: {str(e)}")
    
    async def get_payment_status(self, gateway_payment_id: str) -> PaymentResponse:
        """Get payment status from NowPayments"""
        try:
            payment = await self._make_nowpayments_request(
                "GET",
                f"/payment/{gateway_payment_id}"
            )
            
            return self._convert_nowpayments_payment_to_response(payment)
            
        except Exception as e:
            self.logger.error(f"NowPayments status check error: {e}")
            raise PaymentGatewayException(f"Failed to retrieve payment status: {str(e)}")
    
    async def refund_payment(self, refund_request: RefundRequest, gateway_payment_id: str) -> RefundResponse:
        """Process refund through NowPayments"""
        # NowPayments doesn't support automatic refunds for cryptocurrencies
        # This would require manual intervention
        raise PaymentGatewayException(
            "Cryptocurrency payments cannot be automatically refunded. "
            "Manual refund process required through NowPayments dashboard."
        )
    
    async def create_customer(self, customer_request: CustomerRequest) -> CustomerResponse:
        """Create customer in NowPayments (limited customer support)"""
        # NowPayments has limited customer management
        # We'll create a basic customer record with available information
        
        customer_id = f"nowpayments_{customer_request.email}_{int(datetime.now().timestamp())}"
        
        return CustomerResponse(
            customer_id=customer_id,
            gateway_customer_ids={"nowpayments": customer_id},
            email=customer_request.email,
            first_name=customer_request.first_name,
            last_name=customer_request.last_name,
            phone=customer_request.phone,
            address=customer_request.address,
            company=customer_request.company,
            created_at=datetime.now(),
            metadata=customer_request.metadata
        )
    
    async def get_customer(self, gateway_customer_id: str) -> CustomerResponse:
        """Get customer from NowPayments (limited support)"""
        # NowPayments doesn't have traditional customer management
        # This would need to be implemented with external storage
        raise PaymentGatewayException("Customer retrieval not supported by NowPayments")
    
    async def process_webhook(self, webhook_data: Dict[str, Any], signature: Optional[str] = None) -> Dict[str, Any]:
        """Process NowPayments IPN webhook"""
        try:
            # Verify IPN signature if available
            if self.credentials.ipn_secret and signature:
                if not self._verify_ipn_signature(webhook_data, signature):
                    raise PaymentGatewayException("Invalid IPN signature")
            
            # Extract payment information
            payment_id = webhook_data.get("payment_id")
            payment_status = webhook_data.get("payment_status")
            order_id = webhook_data.get("order_id")
            
            return {
                "gateway": "nowpayments",
                "event_type": f"payment.{payment_status}",
                "event_id": payment_id,
                "created": datetime.now(),
                "data": {
                    "payment_id": payment_id,
                    "order_id": order_id,
                    "status": payment_status,
                    "price_amount": webhook_data.get("price_amount"),
                    "price_currency": webhook_data.get("price_currency"),
                    "pay_amount": webhook_data.get("pay_amount"),
                    "pay_currency": webhook_data.get("pay_currency")
                },
                "raw_event": webhook_data
            }
            
        except Exception as e:
            self.logger.error(f"NowPayments webhook processing error: {e}")
            raise PaymentGatewayException(f"Webhook processing failed: {str(e)}")
    
    async def get_supported_currencies(self) -> List[str]:
        """Get NowPayments supported currencies"""
        try:
            # Get available currencies from API
            currencies = await self._make_nowpayments_request("GET", "/currencies")
            return currencies.get("currencies", [])
        except Exception:
            # Fallback to common cryptocurrencies
            return [
                "BTC", "ETH", "LTC", "BCH", "XMR", "DASH", "ZEC", "XRP", "ADA", "DOT",
                "LINK", "UNI", "USDT", "USDC", "DAI", "BUSD", "TRX", "BNB", "MATIC"
            ]
    
    async def get_supported_payment_methods(self) -> List[str]:
        """Get NowPayments supported payment methods"""
        return ["cryptocurrency", "bitcoin", "ethereum", "litecoin", "stablecoin"]
    
    async def get_minimum_payment_amount(self, currency: str) -> Optional[float]:
        """Get minimum payment amount for a currency"""
        try:
            min_amount = await self._make_nowpayments_request(
                "GET", 
                f"/min-amount?currency_from=usd&currency_to={currency.lower()}"
            )
            return float(min_amount.get("min_amount", 0))
        except Exception:
            return None
    
    async def get_estimated_price(self, amount: float, from_currency: str, to_currency: str) -> Optional[Dict]:
        """Get estimated price for currency conversion"""
        try:
            estimate = await self._make_nowpayments_request(
                "GET",
                f"/estimate?amount={amount}&currency_from={from_currency.lower()}&currency_to={to_currency.lower()}"
            )
            return estimate
        except Exception:
            return None
    
    # Helper methods
    
    async def _make_nowpayments_request(self, method: str, endpoint: str, data: Optional[Dict] = None) -> Dict[str, Any]:
        """Make async request to NowPayments API"""
        try:
            async with aiohttp.ClientSession() as session:
                headers = {
                    "x-api-key": self.credentials.api_key,
                    "Content-Type": "application/json"
                }
                
                url = f"{self.base_url}{endpoint}"
                
                kwargs = {"headers": headers}
                if data:
                    kwargs["data"] = json.dumps(data)
                
                async with session.request(method, url, **kwargs) as response:
                    if response.status >= 400:
                        error_text = await response.text()
                        raise PaymentGatewayException(f"NowPayments API error {response.status}: {error_text}")
                    
                    return await response.json()
                    
        except aiohttp.ClientError as e:
            self.logger.error(f"NowPayments API request failed: {e}")
            raise PaymentGatewayException(f"NowPayments API request failed: {str(e)}")
    
    def _verify_ipn_signature(self, webhook_data: Dict[str, Any], signature: str) -> bool:
        """Verify NowPayments IPN signature"""
        try:
            # Create sorted query string from webhook data
            sorted_data = sorted(webhook_data.items())
            query_string = "&".join([f"{k}={v}" for k, v in sorted_data])
            
            # Calculate HMAC signature
            expected_signature = hmac.new(
                self.credentials.ipn_secret.encode('utf-8'),
                query_string.encode('utf-8'),
                hashlib.sha512
            ).hexdigest()
            
            return hmac.compare_digest(signature, expected_signature)
            
        except Exception as e:
            self.logger.error(f"IPN signature verification error: {e}")
            return False
    
    def _convert_nowpayments_payment_to_response(self, payment: Dict[str, Any], original_request: Optional[PaymentRequest] = None) -> PaymentResponse:
        """Convert NowPayments payment to our standard PaymentResponse"""
        
        # Map NowPayments status to our standard status
        status_mapping = {
            "waiting": PaymentStatus.PENDING,
            "confirming": PaymentStatus.PROCESSING,
            "confirmed": PaymentStatus.COMPLETED,
            "sending": PaymentStatus.PROCESSING,
            "partially_paid": PaymentStatus.PROCESSING,
            "finished": PaymentStatus.COMPLETED,
            "failed": PaymentStatus.FAILED,
            "refunded": PaymentStatus.REFUNDED,
            "expired": PaymentStatus.FAILED
        }
        
        # Extract payment URL
        payment_url = None
        if "invoice_url" in payment:
            payment_url = payment["invoice_url"]
        elif "payment_url" in payment:
            payment_url = payment["payment_url"]
        
        return PaymentResponse(
            payment_id=f"nowpayments_{payment.get('payment_id')}",
            gateway_payment_id=str(payment.get("payment_id")),
            reference=payment.get("order_id"),
            amount=Decimal(str(payment.get("price_amount", 0))),
            currency=payment.get("price_currency", "USD"),
            status=status_mapping.get(payment.get("payment_status"), PaymentStatus.PENDING),
            gateway_used="nowpayments",
            checkout_url=payment_url,
            created_at=datetime.fromisoformat(payment.get("created_at", datetime.now().isoformat()).replace("Z", "+00:00")) if "created_at" in payment else datetime.now(),
            updated_at=datetime.fromisoformat(payment.get("updated_at", datetime.now().isoformat()).replace("Z", "+00:00")) if "updated_at" in payment else None,
            gateway_response=payment,
            metadata={
                "pay_currency": payment.get("pay_currency"),
                "pay_amount": payment.get("pay_amount"),
                "network": payment.get("network"),
                "pay_address": payment.get("pay_address")
            }
        )
