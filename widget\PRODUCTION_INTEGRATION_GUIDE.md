# 🚀 ELOH Processing Production Widget Integration Guide

## 📋 Overview

This guide covers the production-ready, stateless ELOH Processing payment widget that works across all platforms without session dependencies.

## ✨ Key Features

- **🔒 Stateless Operation** - No PHP sessions or cookies required
- **🌐 Cross-Platform** - Works on web, mobile apps, desktop apps
- **⚡ Production Ready** - Real payment processing with live gateways
- **📱 Responsive** - Mobile-first design with auto-resize
- **🎨 Customizable** - Configurable themes and branding
- **🔧 Multiple Integration Methods** - iframe, JavaScript API, data attributes

## 🚀 Quick Start

### 1. Direct iframe (Simplest)

```html
<iframe 
  src="https://elohprocessing.infy.uk/widget/production-widget.php?widget_id=production&amount=100.00" 
  width="400" 
  height="600" 
  frameborder="0"
  style="border-radius: 12px; box-shadow: 0 4px 20px rgba(0,0,0,0.1);">
</iframe>
```

### 2. JavaScript API (Recommended)

```html
<script src="https://elohprocessing.infy.uk/widget/production-embed.js"></script>
<div id="payment-widget"></div>
<script>
  ELOHWidget.create({
    widgetId: 'production',
    container: '#payment-widget',
    amount: 100.00,
    email: '<EMAIL>',
    description: 'Product Purchase',
    onSuccess: function(widget, data) {
      console.log('Payment successful:', data);
      // Handle success
    },
    onError: function(widget, error) {
      console.log('Payment error:', error);
      // Handle error
    }
  });
</script>
```

### 3. Data Attributes (Auto-init)

```html
<script src="https://elohprocessing.infy.uk/widget/production-embed.js"></script>
<div 
  data-eloh-widget="production"
  data-amount="100.00"
  data-email="<EMAIL>"
  data-description="Product Purchase"
  data-theme="light">
</div>
```

## 🔧 Configuration Options

### Widget Parameters

| Parameter | Type | Description | Example |
|-----------|------|-------------|---------|
| `widget_id` | string | Widget configuration ID | `production` |
| `amount` | number | Payment amount in USD | `100.00` |
| `email` | string | Customer email address | `<EMAIL>` |
| `description` | string | Payment description | `Product Purchase` |
| `gateway` | string | Preferred payment gateway | `btcpay`, `nowpayments` |
| `theme` | string | Widget theme | `light`, `dark`, `auto` |
| `currency` | string | Currency code | `USD`, `BTC`, `ETH` |

### JavaScript API Options

```javascript
ELOHWidget.create({
  // Required
  widgetId: 'production',
  container: '#payment-widget',
  
  // Payment Options
  amount: 100.00,
  email: '<EMAIL>',
  description: 'Product Purchase',
  currency: 'USD',
  gateway: 'btcpay',
  
  // Appearance
  theme: 'light',
  width: '100%',
  height: 'auto',
  
  // Behavior
  autoResize: true,
  showLoader: true,
  timeout: 30000,
  
  // Event Callbacks
  onLoad: function(widget) {
    console.log('Widget loaded');
  },
  onSuccess: function(widget, data) {
    console.log('Payment successful:', data);
  },
  onError: function(widget, error) {
    console.log('Payment error:', error);
  },
  onCancel: function(widget) {
    console.log('Payment cancelled');
  }
});
```

## 🌍 Platform-Specific Integration

### Web Applications (PHP, Node.js, Python, etc.)

```html
<!-- Standard iframe embed -->
<iframe 
  src="https://elohprocessing.infy.uk/widget/production-widget.php?widget_id=production&amount=<?php echo $amount; ?>&email=<?php echo urlencode($email); ?>" 
  width="400" 
  height="600" 
  frameborder="0">
</iframe>
```

### Static HTML Sites

```html
<!-- No server-side processing required -->
<script src="https://elohprocessing.infy.uk/widget/production-embed.js"></script>
<div data-eloh-widget="production" data-amount="100.00"></div>
```

### Mobile Apps (React Native, Flutter, etc.)

```javascript
// React Native WebView
import { WebView } from 'react-native-webview';

<WebView
  source={{ 
    uri: 'https://elohprocessing.infy.uk/widget/production-widget.php?widget_id=production&amount=100.00' 
  }}
  style={{ flex: 1 }}
  onMessage={(event) => {
    const data = JSON.parse(event.nativeEvent.data);
    if (data.type === 'eloh_widget_success') {
      // Handle payment success
    }
  }}
/>
```

### Desktop Applications (Electron, etc.)

```javascript
// Electron BrowserView
const { BrowserView } = require('electron');

const view = new BrowserView();
mainWindow.setBrowserView(view);
view.setBounds({ x: 0, y: 0, width: 400, height: 600 });
view.webContents.loadURL('https://elohprocessing.infy.uk/widget/production-widget.php?widget_id=production&amount=100.00');
```

## 🎨 Customization

### Creating Custom Widget Configurations

1. Create a new JSON configuration file in `widget/configs/`:

```json
{
  "theme": "dark",
  "primary_color": "#your-brand-color",
  "accent_color": "#your-accent-color",
  "company_name": "Your Company",
  "enabled_gateways": ["btcpay", "nowpayments"],
  "min_amount": 10.0,
  "max_amount": 5000.0,
  "require_email": true,
  "title": "Complete Your Purchase",
  "description": "Secure payment processing",
  "button_text": "Pay Now",
  "webhook_url": "https://yoursite.com/webhook",
  "production_mode": true
}
```

2. Use your custom widget ID:

```html
<iframe src="https://elohprocessing.infy.uk/widget/production-widget.php?widget_id=your-custom-id&amount=100.00"></iframe>
```

## 🔔 Webhook Integration

Configure webhooks to receive real-time payment notifications:

### Webhook Configuration

Add to your widget config:
```json
{
  "webhook_url": "https://yoursite.com/webhook/eloh-payments"
}
```

### Webhook Payload

```json
{
  "event": "payment_created",
  "order_id": "WIDGET_PRODUCTION_1234567890_5678",
  "amount": 100.00,
  "gateway": "btcpay",
  "email": "<EMAIL>",
  "description": "Product Purchase",
  "payment_data": {
    "invoice_id": "...",
    "payment_url": "..."
  }
}
```

### Webhook Handler Example (PHP)

```php
<?php
$payload = json_decode(file_get_contents('php://input'), true);

if ($payload['event'] === 'payment_created') {
    $order_id = $payload['order_id'];
    $amount = $payload['amount'];
    $gateway = $payload['gateway'];
    
    // Process the payment notification
    // Update your database, send emails, etc.
    
    http_response_code(200);
    echo 'OK';
}
?>
```

## 🔒 Security Features

- **Stateless Operation** - No server-side sessions required
- **Cross-Origin Protection** - CORS headers configured
- **Input Sanitization** - All inputs validated and sanitized
- **Domain Validation** - Optional domain restrictions
- **Webhook Verification** - Secure webhook notifications

## 🚀 Deployment

### Production Checklist

1. **Configure Payment Gateways**
   - Set up BTCPay Server API keys
   - Configure NowPayments API credentials
   - Test payment flows

2. **Create Widget Configurations**
   - Customize branding and colors
   - Set appropriate amount limits
   - Configure webhook URLs

3. **Test Integration**
   - Test on target platforms
   - Verify webhook delivery
   - Test error scenarios

4. **Go Live**
   - Update widget URLs to production
   - Monitor payment processing
   - Set up logging and monitoring

## 📱 Mobile App Integration

### iOS (Swift)

```swift
import WebKit

let webView = WKWebView()
let url = URL(string: "https://elohprocessing.infy.uk/widget/production-widget.php?widget_id=production&amount=100.00")!
webView.load(URLRequest(url: url))
```

### Android (Java/Kotlin)

```java
WebView webView = findViewById(R.id.webview);
webView.getSettings().setJavaScriptEnabled(true);
webView.loadUrl("https://elohprocessing.infy.uk/widget/production-widget.php?widget_id=production&amount=100.00");
```

## 🔧 API Reference

### Widget Methods

```javascript
// Create widget
const widget = ELOHWidget.create(options);

// Update configuration
widget.updateConfig({ amount: 200.00 });

// Destroy widget
widget.destroy();

// Get widget instance
const widget = ELOHWidget.getInstance(instanceId);
```

### Event Types

- `eloh_widget_resize` - Widget height changed
- `eloh_widget_success` - Payment completed successfully
- `eloh_widget_error` - Payment failed
- `eloh_widget_cancel` - Payment cancelled

## 🆘 Support

For technical support and integration assistance:
- **Email**: <EMAIL>
- **Documentation**: https://elohprocessing.infy.uk/widget/
- **Demo**: https://elohprocessing.infy.uk/widget/production-widget.php?widget_id=demo

---

**Ready for production use across all platforms!** 🚀
