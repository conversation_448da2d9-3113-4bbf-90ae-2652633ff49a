<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ELOH Processing - Universal Payment Widget</title>
    <style>
        :root {
            --primary: #667eea;
            --accent: #764ba2;
            --success: #10b981;
            --error: #ef4444;
            --warning: #f59e0b;
            --radius: 12px;
            --shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
        }
        
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: transparent;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .widget-container {
            background: white;
            border-radius: var(--radius);
            box-shadow: var(--shadow);
            padding: 32px;
            width: 100%;
            max-width: 420px;
            position: relative;
            overflow: hidden;
        }
        
        .widget-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary), var(--accent));
        }
        
        .widget-header {
            text-align: center;
            margin-bottom: 28px;
        }
        
        .widget-title {
            font-size: 1.75rem;
            font-weight: 700;
            background: linear-gradient(135deg, var(--primary), var(--accent));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 8px;
        }
        
        .live-badge {
            display: inline-flex;
            align-items: center;
            gap: 6px;
            background: var(--error);
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .live-dot {
            width: 6px;
            height: 6px;
            background: white;
            border-radius: 50%;
            animation: pulse 1.5s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.6; }
        }
        
        .widget-description {
            color: #6b7280;
            font-size: 0.9rem;
            margin-top: 8px;
        }
        
        .alert {
            padding: 12px 16px;
            border-radius: var(--radius);
            margin-bottom: 20px;
            font-size: 0.85rem;
            display: none;
        }
        
        .alert-error {
            background: #fef2f2;
            color: var(--error);
            border: 1px solid #fecaca;
        }
        
        .alert-success {
            background: #f0fdf4;
            color: var(--success);
            border: 1px solid #bbf7d0;
        }
        
        .alert-warning {
            background: #fffbeb;
            color: #d97706;
            border: 1px solid #fed7aa;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-label {
            display: block;
            font-weight: 600;
            color: #374151;
            margin-bottom: 6px;
            font-size: 0.9rem;
        }
        
        .form-input {
            width: 100%;
            padding: 14px 16px;
            border: 2px solid #e5e7eb;
            border-radius: var(--radius);
            font-size: 1rem;
            transition: all 0.2s ease;
            background: #fafafa;
        }
        
        .form-input:focus {
            outline: none;
            border-color: var(--primary);
            background: white;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .gateway-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
            margin-bottom: 20px;
        }
        
        .gateway-card {
            position: relative;
        }
        
        .gateway-card input[type="radio"] {
            position: absolute;
            opacity: 0;
        }
        
        .gateway-card label {
            display: block;
            padding: 16px 12px;
            border: 2px solid #e5e7eb;
            border-radius: var(--radius);
            text-align: center;
            cursor: pointer;
            transition: all 0.2s ease;
            background: #fafafa;
        }
        
        .gateway-card input[type="radio"]:checked + label {
            border-color: var(--primary);
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
            transform: translateY(-1px);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.2);
        }
        
        .gateway-icon {
            font-size: 2rem;
            margin-bottom: 6px;
        }
        
        .gateway-name {
            font-size: 0.85rem;
            font-weight: 600;
            color: #374151;
        }
        
        .amount-group {
            position: relative;
        }
        
        .currency-symbol {
            position: absolute;
            left: 16px;
            top: 50%;
            transform: translateY(-50%);
            color: #6b7280;
            font-weight: 600;
            font-size: 1.1rem;
        }
        
        .amount-group .form-input {
            padding-left: 44px;
            font-size: 1.1rem;
            font-weight: 600;
        }
        
        .pay-button {
            width: 100%;
            background: linear-gradient(135deg, var(--primary) 0%, var(--accent) 100%);
            color: white;
            border: none;
            padding: 16px 24px;
            border-radius: var(--radius);
            font-size: 1rem;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.2s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            position: relative;
        }
        
        .pay-button:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }
        
        .pay-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .loading-spinner {
            display: none;
            width: 20px;
            height: 20px;
            border: 2px solid transparent;
            border-top: 2px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 8px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .security-footer {
            text-align: center;
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #e5e7eb;
            font-size: 0.8rem;
            color: #9ca3af;
        }
        
        .security-footer a {
            color: var(--primary);
            text-decoration: none;
            font-weight: 600;
        }
        
        @media (max-width: 480px) {
            body {
                padding: 12px;
            }
            
            .widget-container {
                padding: 24px;
            }
            
            .gateway-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="widget-container">
        <div class="widget-header">
            <h1 class="widget-title">ELOH Processing</h1>
            <div class="live-badge">
                <div class="live-dot"></div>
                Universal Widget
            </div>
            <p class="widget-description">Cross-platform payment processing</p>
        </div>
        
        <div id="alert-container"></div>
        
        <form id="payment-form">
            <div class="form-group">
                <label class="form-label">Payment Gateway</label>
                <div class="gateway-grid">
                    <div class="gateway-card">
                        <input type="radio" name="gateway" value="btcpay" id="gateway_btcpay" checked>
                        <label for="gateway_btcpay">
                            <div class="gateway-icon">⚡</div>
                            <div class="gateway-name">BTCPay Server</div>
                        </label>
                    </div>
                    <div class="gateway-card">
                        <input type="radio" name="gateway" value="nowpayments" id="gateway_nowpayments">
                        <label for="gateway_nowpayments">
                            <div class="gateway-icon">🌐</div>
                            <div class="gateway-name">NowPayments</div>
                        </label>
                    </div>
                </div>
            </div>
            
            <div class="form-group">
                <label for="amount" class="form-label">Amount</label>
                <div class="amount-group">
                    <span class="currency-symbol">$</span>
                    <input type="number" id="amount" name="amount" 
                           min="5" max="10000" step="0.01" required class="form-input"
                           placeholder="0.00">
                </div>
            </div>
            
            <div class="form-group">
                <label for="email" class="form-label">Email Address</label>
                <input type="email" id="email" name="email" required class="form-input"
                       placeholder="<EMAIL>">
            </div>
            
            <div class="form-group">
                <label for="description" class="form-label">Description</label>
                <input type="text" id="description" name="description" class="form-input"
                       placeholder="Payment description">
            </div>
            
            <button type="submit" class="pay-button" id="pay-button">
                <span class="loading-spinner" id="loading-spinner"></span>
                <span id="button-text">Create Payment</span>
            </button>
        </form>
        
        <div class="security-footer">
            🔒 Secured by <a href="https://elohprocessing.infy.uk" target="_blank">ELOH Processing</a>
        </div>
    </div>
    
    <script>
        class ELOHPaymentWidget {
            constructor(options = {}) {
                this.config = {
                    apiEndpoint: options.apiEndpoint || 'https://elohprocessing.infy.uk/api',
                    onSuccess: options.onSuccess || this.defaultSuccessHandler,
                    onError: options.onError || this.defaultErrorHandler,
                    onPaymentCreated: options.onPaymentCreated || this.defaultPaymentCreatedHandler,
                    debug: options.debug || false
                };
                
                this.init();
            }
            
            init() {
                this.form = document.getElementById('payment-form');
                this.payButton = document.getElementById('pay-button');
                this.buttonText = document.getElementById('button-text');
                this.loadingSpinner = document.getElementById('loading-spinner');
                this.alertContainer = document.getElementById('alert-container');
                
                this.bindEvents();
                this.loadURLParams();
                this.sendResize();
                
                if (this.config.debug) {
                    console.log('🚀 ELOH Payment Widget initialized');
                }
            }
            
            bindEvents() {
                this.form.addEventListener('submit', (e) => this.handleSubmit(e));
                
                // Auto-resize on changes
                ['change', 'input', 'focus', 'blur'].forEach(event => {
                    this.form.addEventListener(event, () => {
                        setTimeout(() => this.sendResize(), 100);
                    });
                });
            }
            
            loadURLParams() {
                const params = new URLSearchParams(window.location.search);
                
                if (params.get('amount')) {
                    document.getElementById('amount').value = params.get('amount');
                }
                if (params.get('email')) {
                    document.getElementById('email').value = params.get('email');
                }
                if (params.get('description')) {
                    document.getElementById('description').value = params.get('description');
                }
                if (params.get('gateway')) {
                    const gatewayRadio = document.getElementById('gateway_' + params.get('gateway'));
                    if (gatewayRadio) gatewayRadio.checked = true;
                }
            }
            
            async handleSubmit(e) {
                e.preventDefault();
                
                const formData = new FormData(this.form);
                const paymentData = {
                    gateway: formData.get('gateway'),
                    amount: parseFloat(formData.get('amount')),
                    email: formData.get('email'),
                    description: formData.get('description') || 'Payment'
                };
                
                // Validate
                const validation = this.validatePayment(paymentData);
                if (!validation.valid) {
                    this.showAlert(validation.error, 'error');
                    return;
                }
                
                this.setLoading(true);
                
                try {
                    const result = await this.createPayment(paymentData);
                    
                    if (result.success) {
                        this.config.onPaymentCreated(result);
                        
                        if (result.payment_url || result.checkout_link) {
                            // Redirect to payment gateway
                            window.top.location.href = result.payment_url || result.checkout_link;
                        } else {
                            this.showAlert('✅ Payment created successfully!', 'success');
                            this.config.onSuccess(result);
                        }
                    } else {
                        throw new Error(result.error || 'Payment creation failed');
                    }
                } catch (error) {
                    this.showAlert('❌ ' + error.message, 'error');
                    this.config.onError(error);
                } finally {
                    this.setLoading(false);
                }
            }
            
            validatePayment(data) {
                if (data.amount < 5) {
                    return { valid: false, error: 'Minimum amount is $5.00' };
                }
                if (data.amount > 10000) {
                    return { valid: false, error: 'Maximum amount is $10,000.00' };
                }
                if (!data.email || !data.email.includes('@')) {
                    return { valid: false, error: 'Valid email address is required' };
                }
                return { valid: true };
            }
            
            async createPayment(paymentData) {
                const orderId = 'WIDGET_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
                
                const payload = {
                    ...paymentData,
                    order_id: orderId,
                    currency: 'USD',
                    source: 'universal_widget'
                };
                
                const endpoint = `${this.config.apiEndpoint}/${paymentData.gateway}-create.php`;
                
                const response = await fetch(endpoint, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify(payload)
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                return await response.json();
            }
            
            setLoading(loading) {
                this.payButton.disabled = loading;
                this.loadingSpinner.style.display = loading ? 'inline-block' : 'none';
                this.buttonText.textContent = loading ? 'Processing...' : 'Create Payment';
            }
            
            showAlert(message, type) {
                this.alertContainer.innerHTML = `
                    <div class="alert alert-${type}" style="display: block;">
                        ${message}
                    </div>
                `;
                this.sendResize();
                
                // Auto-hide success messages
                if (type === 'success') {
                    setTimeout(() => {
                        this.alertContainer.innerHTML = '';
                        this.sendResize();
                    }, 5000);
                }
            }
            
            sendResize() {
                if (window.parent && window.parent !== window) {
                    const height = document.body.scrollHeight;
                    window.parent.postMessage({
                        type: 'eloh_widget_resize',
                        height: height
                    }, '*');
                }
            }
            
            // Default event handlers
            defaultSuccessHandler(result) {
                console.log('Payment successful:', result);
            }
            
            defaultErrorHandler(error) {
                console.error('Payment error:', error);
            }
            
            defaultPaymentCreatedHandler(result) {
                console.log('Payment created:', result);
            }
        }
        
        // Initialize widget
        window.ELOHWidget = new ELOHPaymentWidget({
            debug: true,
            onSuccess: (result) => {
                // Send success message to parent window
                if (window.parent && window.parent !== window) {
                    window.parent.postMessage({
                        type: 'eloh_payment_success',
                        data: result
                    }, '*');
                }
            },
            onError: (error) => {
                // Send error message to parent window
                if (window.parent && window.parent !== window) {
                    window.parent.postMessage({
                        type: 'eloh_payment_error',
                        data: { error: error.message }
                    }, '*');
                }
            },
            onPaymentCreated: (result) => {
                // Send payment created message to parent window
                if (window.parent && window.parent !== window) {
                    window.parent.postMessage({
                        type: 'eloh_payment_created',
                        data: result
                    }, '*');
                }
            }
        });
    </script>
</body>
</html>
