"""
Tenant models for ELOH Processing Payment Gateway
"""

from pydantic import BaseModel, Field, EmailStr
from typing import Optional, List, Dict, Any
from enum import Enum
from datetime import datetime


class TenantPlan(str, Enum):
    """Tenant subscription plans"""
    STARTER = "starter"
    PROFESSIONAL = "professional"
    ENTERPRISE = "enterprise"


class TenantStatus(str, Enum):
    """Tenant account status"""
    ACTIVE = "active"
    SUSPENDED = "suspended"
    INACTIVE = "inactive"


class TenantRequest(BaseModel):
    """Request model for creating a tenant"""
    company_name: str = Field(..., min_length=1, max_length=200)
    business_type: Optional[str] = Field(None, max_length=100)
    website_url: Optional[str] = Field(None, max_length=500)
    
    contact_email: EmailStr
    contact_name: Optional[str] = Field(None, max_length=200)
    contact_phone: Optional[str] = Field(None, max_length=50)
    
    address_line1: Optional[str] = Field(None, max_length=200)
    city: Optional[str] = Field(None, max_length=100)
    state: Optional[str] = Field(None, max_length=100)
    postal_code: Optional[str] = Field(None, max_length=20)
    country: str = Field(default="DM", max_length=10)
    
    plan: TenantPlan = Field(default=TenantPlan.STARTER)
    expected_monthly_volume: Optional[float] = Field(None, ge=0)
    preferred_gateways: Optional[List[str]] = Field(default_factory=list)
    required_currencies: Optional[List[str]] = Field(default_factory=list)
    
    metadata: Optional[Dict[str, Any]] = Field(default_factory=dict)


class TenantResponse(BaseModel):
    """Response model for tenant information"""
    tenant_id: str
    api_key: str
    company_name: str
    business_type: Optional[str]
    contact_email: str
    contact_name: Optional[str]
    country: str
    plan: TenantPlan
    status: TenantStatus
    total_transactions: int
    total_volume: float
    monthly_volume: float
    created_at: datetime
    last_activity: Optional[datetime]


class GatewayConfigurationRequest(BaseModel):
    """Request model for configuring a gateway"""
    gateway_id: str = Field(..., min_length=1)
    enabled: bool = Field(default=True)
    
    credentials: Dict[str, Any] = Field(..., min_items=1)
    configuration: Optional[Dict[str, Any]] = Field(default_factory=dict)
    
    priority: int = Field(default=1, ge=1, le=10)
    min_amount: Optional[float] = Field(default=0.01, ge=0)
    max_amount: Optional[float] = Field(default=10000.0, ge=0)
    daily_limit: Optional[float] = Field(default=50000.0, ge=0)
    
    webhook_url: Optional[str] = Field(None, max_length=500)
    webhook_events: Optional[List[str]] = Field(default_factory=list)


class GatewayConfigurationResponse(BaseModel):
    """Response model for gateway configuration"""
    id: int
    gateway_id: str
    enabled: bool
    priority: int
    min_amount: float
    max_amount: float
    daily_limit: float
    total_transactions: int
    total_volume: float
    last_used: Optional[datetime]
    created_at: datetime
