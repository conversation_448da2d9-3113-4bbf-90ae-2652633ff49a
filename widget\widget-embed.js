/**
 * ELOH Processing Checkout Widget Embed Script
 * Easy integration for third-party websites
 * 
 * Usage:
 * <script src="https://your-domain.com/widget/widget-embed.js"></script>
 * <script>
 *   ELOHWidget.create({
 *     widgetId: 'your-widget-id',
 *     container: '#payment-widget',
 *     amount: 100.00,
 *     email: '<EMAIL>'
 *   });
 * </script>
 */

(function(window, document) {
    'use strict';
    
    // Widget configuration
    const WIDGET_BASE_URL = 'https://elohprocessing.infy.uk/widget/';
    
    // Main widget object
    const ELOHWidget = {
        version: '1.0.0',
        instances: new Map(),
        
        /**
         * Create a new widget instance
         */
        create: function(options) {
            const config = this.validateConfig(options);
            const widgetId = this.generateInstanceId();
            
            const widget = new WidgetInstance(widgetId, config);
            this.instances.set(widgetId, widget);
            
            return widget;
        },
        
        /**
         * Validate widget configuration
         */
        validateConfig: function(options) {
            if (!options || typeof options !== 'object') {
                throw new Error('Widget options must be an object');
            }
            
            if (!options.widgetId) {
                throw new Error('widgetId is required');
            }
            
            if (!options.container) {
                throw new Error('container is required');
            }
            
            // Default configuration
            const defaults = {
                width: '100%',
                height: 'auto',
                theme: 'auto',
                autoResize: true,
                showLoader: true,
                timeout: 30000,
                onLoad: null,
                onSuccess: null,
                onError: null,
                onCancel: null
            };
            
            return Object.assign({}, defaults, options);
        },
        
        /**
         * Generate unique instance ID
         */
        generateInstanceId: function() {
            return 'eloh_widget_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        },
        
        /**
         * Get widget instance by ID
         */
        getInstance: function(instanceId) {
            return this.instances.get(instanceId);
        },
        
        /**
         * Remove widget instance
         */
        destroy: function(instanceId) {
            const widget = this.instances.get(instanceId);
            if (widget) {
                widget.destroy();
                this.instances.delete(instanceId);
            }
        }
    };
    
    /**
     * Widget Instance Class
     */
    function WidgetInstance(instanceId, config) {
        this.instanceId = instanceId;
        this.config = config;
        this.container = null;
        this.iframe = null;
        this.isLoaded = false;
        this.isDestroyed = false;
        
        this.init();
    }
    
    WidgetInstance.prototype = {
        /**
         * Initialize the widget
         */
        init: function() {
            this.container = this.getContainer();
            if (!this.container) {
                throw new Error('Container not found: ' + this.config.container);
            }
            
            this.createIframe();
            this.setupEventListeners();
            this.loadWidget();
        },
        
        /**
         * Get container element
         */
        getContainer: function() {
            if (typeof this.config.container === 'string') {
                return document.querySelector(this.config.container);
            } else if (this.config.container instanceof Element) {
                return this.config.container;
            }
            return null;
        },
        
        /**
         * Create iframe element
         */
        createIframe: function() {
            this.iframe = document.createElement('iframe');
            this.iframe.id = this.instanceId;
            this.iframe.style.width = this.config.width;
            this.iframe.style.height = this.config.height === 'auto' ? '600px' : this.config.height;
            this.iframe.style.border = 'none';
            this.iframe.style.borderRadius = '12px';
            this.iframe.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.1)';
            this.iframe.style.transition = 'height 0.3s ease';
            this.iframe.setAttribute('allowtransparency', 'true');
            this.iframe.setAttribute('scrolling', 'no');
            
            // Security attributes
            this.iframe.setAttribute('sandbox', 'allow-scripts allow-forms allow-same-origin allow-top-navigation');
            
            this.container.appendChild(this.iframe);
        },
        
        /**
         * Setup event listeners
         */
        setupEventListeners: function() {
            const self = this;
            
            // Listen for messages from iframe
            window.addEventListener('message', function(event) {
                if (event.source !== self.iframe.contentWindow) {
                    return;
                }
                
                self.handleMessage(event.data);
            });
            
            // Handle iframe load
            this.iframe.addEventListener('load', function() {
                self.isLoaded = true;
                self.hideLoader();
                
                if (typeof self.config.onLoad === 'function') {
                    self.config.onLoad(self);
                }
            });
            
            // Handle iframe error
            this.iframe.addEventListener('error', function() {
                self.showError('Failed to load payment widget');
                
                if (typeof self.config.onError === 'function') {
                    self.config.onError(self, 'Failed to load widget');
                }
            });
        },
        
        /**
         * Handle messages from iframe
         */
        handleMessage: function(data) {
            if (!data || typeof data !== 'object') {
                return;
            }
            
            switch (data.type) {
                case 'eloh_widget_resize':
                    if (this.config.autoResize && data.height) {
                        this.iframe.style.height = data.height + 'px';
                    }
                    break;
                    
                case 'eloh_widget_success':
                    if (typeof this.config.onSuccess === 'function') {
                        this.config.onSuccess(this, data.payload);
                    }
                    break;
                    
                case 'eloh_widget_error':
                    if (typeof this.config.onError === 'function') {
                        this.config.onError(this, data.payload);
                    }
                    break;
                    
                case 'eloh_widget_cancel':
                    if (typeof this.config.onCancel === 'function') {
                        this.config.onCancel(this);
                    }
                    break;
            }
        },
        
        /**
         * Load the widget
         */
        loadWidget: function() {
            if (this.config.showLoader) {
                this.showLoader();
            }
            
            const params = new URLSearchParams({
                widget_id: this.config.widgetId,
                theme: this.config.theme
            });
            
            // Add optional parameters
            if (this.config.amount) {
                params.append('amount', this.config.amount);
            }
            if (this.config.email) {
                params.append('email', this.config.email);
            }
            if (this.config.description) {
                params.append('description', this.config.description);
            }
            if (this.config.currency) {
                params.append('currency', this.config.currency);
            }
            if (this.config.gateway) {
                params.append('gateway', this.config.gateway);
            }
            
            const widgetUrl = WIDGET_BASE_URL + 'checkout-widget.php?' + params.toString();
            this.iframe.src = widgetUrl;
            
            // Set timeout
            if (this.config.timeout > 0) {
                setTimeout(() => {
                    if (!this.isLoaded && !this.isDestroyed) {
                        this.showError('Widget loading timeout');
                        
                        if (typeof this.config.onError === 'function') {
                            this.config.onError(this, 'Loading timeout');
                        }
                    }
                }, this.config.timeout);
            }
        },
        
        /**
         * Show loading indicator
         */
        showLoader: function() {
            const loader = document.createElement('div');
            loader.id = this.instanceId + '_loader';
            loader.style.cssText = `
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                text-align: center;
                z-index: 1000;
            `;
            
            loader.innerHTML = `
                <div style="
                    width: 40px;
                    height: 40px;
                    border: 3px solid #e2e8f0;
                    border-top: 3px solid #667eea;
                    border-radius: 50%;
                    animation: eloh-spin 1s linear infinite;
                    margin: 0 auto 12px;
                "></div>
                <div style="color: #718096; font-size: 14px;">Loading payment widget...</div>
            `;
            
            // Add CSS animation if not already added
            if (!document.getElementById('eloh-widget-styles')) {
                const style = document.createElement('style');
                style.id = 'eloh-widget-styles';
                style.textContent = `
                    @keyframes eloh-spin {
                        0% { transform: rotate(0deg); }
                        100% { transform: rotate(360deg); }
                    }
                `;
                document.head.appendChild(style);
            }
            
            this.container.style.position = 'relative';
            this.container.appendChild(loader);
        },
        
        /**
         * Hide loading indicator
         */
        hideLoader: function() {
            const loader = document.getElementById(this.instanceId + '_loader');
            if (loader) {
                loader.remove();
            }
        },
        
        /**
         * Show error message
         */
        showError: function(message) {
            this.hideLoader();
            
            const error = document.createElement('div');
            error.style.cssText = `
                background: #fed7d7;
                color: #c53030;
                padding: 16px;
                border-radius: 8px;
                text-align: center;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
                font-size: 14px;
            `;
            error.textContent = message;
            
            this.container.innerHTML = '';
            this.container.appendChild(error);
        },
        
        /**
         * Update widget configuration
         */
        updateConfig: function(newConfig) {
            Object.assign(this.config, newConfig);
            this.loadWidget();
        },
        
        /**
         * Destroy the widget
         */
        destroy: function() {
            if (this.isDestroyed) {
                return;
            }
            
            this.isDestroyed = true;
            
            if (this.iframe && this.iframe.parentNode) {
                this.iframe.parentNode.removeChild(this.iframe);
            }
            
            this.hideLoader();
            
            // Clean up references
            this.container = null;
            this.iframe = null;
            this.config = null;
        }
    };
    
    // Expose to global scope
    window.ELOHWidget = ELOHWidget;
    
    // Auto-initialize widgets with data attributes
    document.addEventListener('DOMContentLoaded', function() {
        const autoWidgets = document.querySelectorAll('[data-eloh-widget]');
        
        autoWidgets.forEach(function(element) {
            const config = {
                widgetId: element.getAttribute('data-eloh-widget'),
                container: element,
                amount: element.getAttribute('data-amount'),
                email: element.getAttribute('data-email'),
                description: element.getAttribute('data-description'),
                currency: element.getAttribute('data-currency'),
                gateway: element.getAttribute('data-gateway'),
                theme: element.getAttribute('data-theme') || 'auto'
            };
            
            // Remove null/undefined values
            Object.keys(config).forEach(key => {
                if (config[key] === null || config[key] === undefined) {
                    delete config[key];
                }
            });
            
            try {
                ELOHWidget.create(config);
            } catch (error) {
                console.error('Failed to auto-initialize ELOH widget:', error);
            }
        });
    });
    
})(window, document);
