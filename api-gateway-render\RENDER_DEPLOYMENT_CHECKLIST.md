# 🚀 ELOH Processing Payment Gateway - Render Deployment Checklist

## ✅ Pre-Deployment Checklist

### 1. Repository Setup
- [ ] Code is in `api-gateway-render` folder
- [ ] All files are present (main.py, requirements.txt, render.yaml, etc.)
- [ ] Code is committed to GitHub
- [ ] Repository is public or accessible to Render

### 2. Render Account Setup
- [ ] Render account created at [render.com](https://render.com)
- [ ] GitHub account connected to Render
- [ ] Payment method added (for paid plans)

## 🗄️ Database Setup

### 1. Create PostgreSQL Database
- [ ] Go to Render Dashboard
- [ ] Click "New +" → "PostgreSQL"
- [ ] Configure:
  - **Name**: `eloh-gateway-db`
  - **Database**: `eloh_gateway`
  - **User**: `eloh_user`
  - **Region**: Choose closest to your users
  - **Plan**: Free (can upgrade later)
- [ ] Click "Create Database"
- [ ] Save connection details

## 🌐 Web Service Setup

### 1. Create Web Service
- [ ] Click "New +" → "Web Service"
- [ ] Connect GitHub repository
- [ ] Configure:
  - **Name**: `eloh-payment-gateway-api`
  - **Environment**: `Python 3`
  - **Region**: Same as database
  - **Branch**: `main`
  - **Root Directory**: Leave empty (or `api-gateway-render` if in subfolder)
  - **Build Command**: `pip install -r requirements.txt && python setup_database.py --sample-data`
  - **Start Command**: `uvicorn main:app --host 0.0.0.0 --port $PORT`
  - **Plan**: Free (can upgrade later)

### 2. Environment Variables
Set these in Render web service dashboard:

#### Required Variables
- [ ] `APP_NAME` = `ELOH Processing Payment Gateway`
- [ ] `ENVIRONMENT` = `production`
- [ ] `DEBUG` = `false`
- [ ] `LOG_LEVEL` = `INFO`
- [ ] `LOG_FORMAT` = `json`

#### Database (Auto-configured)
- [ ] `DATABASE_URL` = (auto-populated by Render)

#### Gateway Credentials (Set your real values)

**BTCPay Server (Recommended for Dominica):**
- [ ] `BTCPAY_SERVER_URL` = `https://your-btcpay-server.com`
- [ ] `BTCPAY_API_KEY` = `your_btcpay_api_key`
- [ ] `BTCPAY_STORE_ID` = `your_btcpay_store_id`
- [ ] `BTCPAY_WEBHOOK_SECRET` = `your_btcpay_webhook_secret`

**NowPayments (Recommended for Dominica):**
- [ ] `NOWPAYMENTS_API_KEY` = `your_nowpayments_api_key`
- [ ] `NOWPAYMENTS_IPN_SECRET` = `your_nowpayments_ipn_secret`
- [ ] `NOWPAYMENTS_ENVIRONMENT` = `production`

**Optional Gateways:**
- [ ] `STRIPE_SECRET_KEY` = `sk_live_your_stripe_key` (if available)
- [ ] `STRIPE_PUBLISHABLE_KEY` = `pk_live_your_stripe_key` (if available)

## 🚀 Deployment

### 1. Deploy Service
- [ ] Click "Create Web Service"
- [ ] Wait for build to complete (5-10 minutes)
- [ ] Check build logs for errors
- [ ] Verify deployment success

### 2. Test Deployment
- [ ] Visit health endpoint: `https://your-app.onrender.com/health`
- [ ] Check response shows "healthy" status
- [ ] Visit API docs: `https://your-app.onrender.com/docs`
- [ ] Visit tenant portal: `https://your-app.onrender.com/v1/portal`
- [ ] Test gateway status: `https://your-app.onrender.com/v1/portal/status`

## 🔧 Post-Deployment Configuration

### 1. Custom Domain (Optional)
- [ ] Go to service Settings → Custom Domains
- [ ] Add your domain: `api.yourdomain.com`
- [ ] Update DNS records as instructed
- [ ] Wait for SSL certificate provisioning

### 2. Monitoring Setup
- [ ] Enable health checks in Render dashboard
- [ ] Set up alerts for service downtime
- [ ] Monitor resource usage
- [ ] Check logs regularly

### 3. Gateway Configuration
- [ ] Test BTCPay Server connection
- [ ] Test NowPayments connection
- [ ] Verify webhook endpoints
- [ ] Test payment creation

## 🧪 Testing Checklist

### 1. API Health
```bash
curl https://your-app.onrender.com/health
```
Expected: `{"status": "healthy", ...}`

### 2. Gateway Status
```bash
curl https://your-app.onrender.com/v1/portal/status
```
Expected: Gateway availability for Dominica

### 3. Create Test Payment
```bash
curl -X POST "https://your-app.onrender.com/v1/payments" \
  -H "Authorization: Bearer your_api_key" \
  -H "Content-Type: application/json" \
  -d '{
    "amount": 10.00,
    "currency": "USD",
    "description": "Test payment",
    "email": "<EMAIL>"
  }'
```

### 4. Create Test Tenant
```bash
curl -X POST "https://your-app.onrender.com/v1/tenants" \
  -H "Content-Type: application/json" \
  -d '{
    "company_name": "Test Store",
    "contact_email": "<EMAIL>",
    "country": "DM"
  }'
```

## 🔒 Security Checklist

### 1. Environment Variables
- [ ] All sensitive data in environment variables
- [ ] No hardcoded credentials in code
- [ ] Strong secret key generated
- [ ] Database credentials secure

### 2. API Security
- [ ] HTTPS enabled (automatic on Render)
- [ ] Rate limiting configured
- [ ] Input validation working
- [ ] Error handling proper

### 3. Gateway Security
- [ ] Webhook secrets configured
- [ ] API keys properly secured
- [ ] Test vs production environments separated

## 📊 Performance Checklist

### 1. Database Performance
- [ ] Database connection pooling enabled
- [ ] Queries optimized
- [ ] Indexes created
- [ ] Connection limits appropriate

### 2. API Performance
- [ ] Response times acceptable (<500ms)
- [ ] Memory usage reasonable
- [ ] CPU usage normal
- [ ] No memory leaks

## 🆘 Troubleshooting

### Common Issues:
1. **Build Fails**: Check requirements.txt and Python version
2. **Database Connection**: Verify DATABASE_URL is set
3. **Gateway Errors**: Check credentials and network connectivity
4. **Health Check Fails**: Check application startup and database

### Debug Commands:
```bash
# Check logs in Render dashboard
# Test specific endpoints
curl https://your-app.onrender.com/health
curl https://your-app.onrender.com/v1/portal/status
```

## ✅ Final Verification

- [ ] All endpoints responding correctly
- [ ] Database populated with sample data
- [ ] Gateways configured and available
- [ ] Monitoring and alerts set up
- [ ] Documentation accessible
- [ ] Ready for production use

## 🎉 Success!

Your ELOH Processing Payment Gateway is now live on Render!

**Next Steps:**
1. Share API documentation with developers
2. Onboard first real tenants
3. Monitor usage and performance
4. Scale as needed

**Your URLs:**
- **API**: `https://your-app.onrender.com`
- **Docs**: `https://your-app.onrender.com/docs`
- **Portal**: `https://your-app.onrender.com/v1/portal`
- **Health**: `https://your-app.onrender.com/health`

---

**🌴 Ready to process payments in the Caribbean! 🚀💰**
