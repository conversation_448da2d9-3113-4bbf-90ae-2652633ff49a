// ELOH Processing PWA Application
class ELOHProcessingPWA {
  constructor() {
    this.deferredPrompt = null;
    this.isOnline = navigator.onLine;
    this.notificationPermission = 'default';
    this.init();
  }

  async init() {
    console.log('ELOH Processing PWA: Initializing...');
    
    // Register service worker
    await this.registerServiceWorker();
    
    // Setup PWA features
    this.setupInstallPrompt();
    this.setupOfflineDetection();
    this.setupNotifications();
    this.setupBackgroundSync();
    this.setupOfflineStorage();
    
    // Initialize UI components
    this.initializeUI();
    
    console.log('ELOH Processing PWA: Initialized successfully');
  }

  // Service Worker Registration
  async registerServiceWorker() {
    if ('serviceWorker' in navigator) {
      try {
        const registration = await navigator.serviceWorker.register('/sw.js');
        console.log('PWA: Service Worker registered successfully', registration);
        
        // Handle service worker updates
        registration.addEventListener('updatefound', () => {
          const newWorker = registration.installing;
          newWorker.addEventListener('statechange', () => {
            if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
              this.showUpdateAvailable();
            }
          });
        });
        
        return registration;
      } catch (error) {
        console.error('PWA: Service Worker registration failed', error);
      }
    }
  }

  // PWA Installation
  setupInstallPrompt() {
    window.addEventListener('beforeinstallprompt', (e) => {
      console.log('PWA: Install prompt available');
      e.preventDefault();
      this.deferredPrompt = e;
      this.showInstallButton();
    });

    window.addEventListener('appinstalled', () => {
      console.log('PWA: App installed successfully');
      this.hideInstallButton();
      this.trackEvent('pwa_installed');
    });
  }

  showInstallButton() {
    const installButton = document.getElementById('pwa-install-btn');
    if (installButton) {
      installButton.style.display = 'block';
      installButton.addEventListener('click', () => this.promptInstall());
    } else {
      // Create install button if it doesn't exist
      this.createInstallButton();
    }
  }

  createInstallButton() {
    const installButton = document.createElement('button');
    installButton.id = 'pwa-install-btn';
    installButton.className = 'pwa-install-button';
    installButton.innerHTML = `
      <span class="install-icon">📱</span>
      <span class="install-text">Install App</span>
    `;
    installButton.addEventListener('click', () => this.promptInstall());
    
    // Add to header or create floating button
    const header = document.querySelector('.navbar');
    if (header) {
      header.appendChild(installButton);
    } else {
      document.body.appendChild(installButton);
      installButton.classList.add('floating-install-btn');
    }
  }

  async promptInstall() {
    if (this.deferredPrompt) {
      this.deferredPrompt.prompt();
      const { outcome } = await this.deferredPrompt.userChoice;
      console.log('PWA: Install prompt result:', outcome);
      
      if (outcome === 'accepted') {
        this.trackEvent('pwa_install_accepted');
      } else {
        this.trackEvent('pwa_install_dismissed');
      }
      
      this.deferredPrompt = null;
      this.hideInstallButton();
    }
  }

  hideInstallButton() {
    const installButton = document.getElementById('pwa-install-btn');
    if (installButton) {
      installButton.style.display = 'none';
    }
  }

  // Offline Detection
  setupOfflineDetection() {
    window.addEventListener('online', () => {
      console.log('PWA: Back online');
      this.isOnline = true;
      this.hideOfflineIndicator();
      this.syncOfflineData();
    });

    window.addEventListener('offline', () => {
      console.log('PWA: Gone offline');
      this.isOnline = false;
      this.showOfflineIndicator();
    });
  }

  showOfflineIndicator() {
    let indicator = document.getElementById('offline-indicator');
    if (!indicator) {
      indicator = document.createElement('div');
      indicator.id = 'offline-indicator';
      indicator.className = 'offline-indicator';
      indicator.innerHTML = `
        <span class="offline-icon">⚡</span>
        <span class="offline-text">You're offline. Some features may be limited.</span>
      `;
      document.body.appendChild(indicator);
    }
    indicator.style.display = 'flex';
  }

  hideOfflineIndicator() {
    const indicator = document.getElementById('offline-indicator');
    if (indicator) {
      indicator.style.display = 'none';
    }
  }

  // Push Notifications
  async setupNotifications() {
    if ('Notification' in window && 'serviceWorker' in navigator) {
      this.notificationPermission = await Notification.requestPermission();
      
      if (this.notificationPermission === 'granted') {
        console.log('PWA: Notification permission granted');
        await this.subscribeToPushNotifications();
      }
    }
  }

  async subscribeToPushNotifications() {
    try {
      const registration = await navigator.serviceWorker.ready;
      
      // Check if already subscribed
      let subscription = await registration.pushManager.getSubscription();
      
      if (!subscription) {
        // Subscribe to push notifications
        subscription = await registration.pushManager.subscribe({
          userVisibleOnly: true,
          applicationServerKey: this.urlBase64ToUint8Array(this.getVAPIDPublicKey())
        });
        
        console.log('PWA: Push subscription created', subscription);
      }
      
      // Send subscription to server
      await this.sendSubscriptionToServer(subscription);
      
    } catch (error) {
      console.error('PWA: Push subscription failed', error);
    }
  }

  getVAPIDPublicKey() {
    // Replace with your actual VAPID public key
    return 'BEl62iUYgUivxIkv69yViEuiBIa40HI0DLLuxazjqAKVXTJtkOSHCUFN5ckiUoLwwBLyL0N0bNNhQRqy8TxQeUo';
  }

  urlBase64ToUint8Array(base64String) {
    const padding = '='.repeat((4 - base64String.length % 4) % 4);
    const base64 = (base64String + padding)
      .replace(/-/g, '+')
      .replace(/_/g, '/');

    const rawData = window.atob(base64);
    const outputArray = new Uint8Array(rawData.length);

    for (let i = 0; i < rawData.length; ++i) {
      outputArray[i] = rawData.charCodeAt(i);
    }
    return outputArray;
  }

  async sendSubscriptionToServer(subscription) {
    try {
      const response = await fetch('/api/push-subscription', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(subscription)
      });
      
      if (response.ok) {
        console.log('PWA: Subscription sent to server successfully');
      }
    } catch (error) {
      console.error('PWA: Failed to send subscription to server', error);
    }
  }

  // Background Sync
  setupBackgroundSync() {
    if ('serviceWorker' in navigator && 'sync' in window.ServiceWorkerRegistration.prototype) {
      console.log('PWA: Background sync supported');
    }
  }

  async registerBackgroundSync(tag) {
    try {
      const registration = await navigator.serviceWorker.ready;
      await registration.sync.register(tag);
      console.log('PWA: Background sync registered:', tag);
    } catch (error) {
      console.error('PWA: Background sync registration failed', error);
    }
  }

  // Offline Storage
  setupOfflineStorage() {
    this.initIndexedDB();
  }

  async initIndexedDB() {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open('ELOHProcessingDB', 1);
      
      request.onerror = () => reject(request.error);
      request.onsuccess = () => resolve(request.result);
      
      request.onupgradeneeded = (event) => {
        const db = event.target.result;
        
        // Create object stores
        if (!db.objectStoreNames.contains('payments')) {
          const paymentStore = db.createObjectStore('payments', { keyPath: 'id', autoIncrement: true });
          paymentStore.createIndex('status', 'status', { unique: false });
          paymentStore.createIndex('timestamp', 'timestamp', { unique: false });
        }
        
        if (!db.objectStoreNames.contains('contacts')) {
          const contactStore = db.createObjectStore('contacts', { keyPath: 'id', autoIncrement: true });
          contactStore.createIndex('status', 'status', { unique: false });
          contactStore.createIndex('timestamp', 'timestamp', { unique: false });
        }
      };
    });
  }

  // Offline Data Sync
  async syncOfflineData() {
    if (this.isOnline) {
      await this.registerBackgroundSync('payment-sync');
      await this.registerBackgroundSync('contact-sync');
    }
  }

  // UI Initialization
  initializeUI() {
    this.addPWAStyles();
    this.enhancePaymentForm();
    this.addOfflineCapabilities();
  }

  addPWAStyles() {
    const style = document.createElement('style');
    style.textContent = `
      .pwa-install-button {
        background: var(--primary-gradient);
        color: white;
        border: none;
        padding: 8px 16px;
        border-radius: 8px;
        font-size: 0.9rem;
        cursor: pointer;
        display: flex;
        align-items: center;
        gap: 8px;
        transition: all 0.3s ease;
      }
      
      .pwa-install-button:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
      }
      
      .floating-install-btn {
        position: fixed;
        bottom: 20px;
        right: 20px;
        z-index: 1000;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      }
      
      .offline-indicator {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        background: #f59e0b;
        color: white;
        padding: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
        z-index: 1001;
        font-size: 0.9rem;
        font-weight: 500;
      }
      
      .update-available {
        position: fixed;
        bottom: 20px;
        left: 20px;
        background: var(--primary-gradient);
        color: white;
        padding: 16px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        z-index: 1000;
      }
    `;
    document.head.appendChild(style);
  }

  enhancePaymentForm() {
    const paymentForm = document.querySelector('#payment-form');
    if (paymentForm) {
      paymentForm.addEventListener('submit', (e) => {
        if (!this.isOnline) {
          e.preventDefault();
          this.handleOfflinePayment(new FormData(paymentForm));
        }
      });
    }
  }

  async handleOfflinePayment(formData) {
    const paymentData = {
      gateway: formData.get('gateway'),
      amount: formData.get('amount'),
      email: formData.get('email'),
      service: formData.get('service'),
      timestamp: Date.now(),
      status: 'pending'
    };
    
    try {
      const db = await this.initIndexedDB();
      const transaction = db.transaction(['payments'], 'readwrite');
      const store = transaction.objectStore('payments');
      await store.add(paymentData);
      
      this.showOfflinePaymentMessage();
      await this.registerBackgroundSync('payment-sync');
      
    } catch (error) {
      console.error('PWA: Failed to store offline payment', error);
    }
  }

  showOfflinePaymentMessage() {
    const message = document.createElement('div');
    message.className = 'alert alert-info';
    message.innerHTML = `
      <h4>⚡ Payment Queued</h4>
      <p>Your payment has been saved and will be processed when you're back online.</p>
    `;
    
    const form = document.querySelector('#payment-form');
    if (form) {
      form.insertBefore(message, form.firstChild);
    }
  }

  addOfflineCapabilities() {
    // Add offline indicators to forms and interactive elements
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
      if (!this.isOnline) {
        const offlineNotice = document.createElement('div');
        offlineNotice.className = 'alert alert-warning';
        offlineNotice.innerHTML = `
          <strong>⚡ Offline Mode:</strong> Form data will be saved and submitted when you're back online.
        `;
        form.insertBefore(offlineNotice, form.firstChild);
      }
    });
  }

  showUpdateAvailable() {
    const updateNotice = document.createElement('div');
    updateNotice.className = 'update-available';
    updateNotice.innerHTML = `
      <h4>🚀 Update Available</h4>
      <p>A new version of ELOH Processing is available.</p>
      <button onclick="window.location.reload()" class="btn btn-sm btn-light">Update Now</button>
    `;
    document.body.appendChild(updateNotice);
  }

  // Analytics and tracking
  trackEvent(eventName, data = {}) {
    if (typeof gtag !== 'undefined') {
      gtag('event', eventName, {
        event_category: 'PWA',
        ...data
      });
    }
    console.log('PWA Event:', eventName, data);
  }
}

// Initialize PWA when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  window.elohPWA = new ELOHProcessingPWA();
});

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
  module.exports = ELOHProcessingPWA;
}
