"""
Custom exceptions for ELOH Processing Payment Gateway
"""

from fastapi import Request
from fastapi.responses import JSONResponse
from typing import Optional, Dict, Any
import logging

logger = logging.getLogger(__name__)


class PaymentGatewayException(Exception):
    """Base exception for payment gateway errors"""
    
    def __init__(
        self,
        message: str,
        error_code: str = "GATEWAY_ERROR",
        status_code: int = 400,
        details: Optional[Dict[str, Any]] = None
    ):
        self.message = message
        self.error_code = error_code
        self.status_code = status_code
        self.details = details or {}
        super().__init__(self.message)


class GatewayNotAvailableException(PaymentGatewayException):
    """Raised when a gateway is not available in the current region"""
    
    def __init__(self, gateway: str, region: str = "Unknown"):
        message = f"Gateway '{gateway}' is not available in region '{region}'"
        super().__init__(
            message=message,
            error_code="GATEWAY_NOT_AVAILABLE",
            status_code=422,
            details={"gateway": gateway, "region": region}
        )


class GatewayConfigurationException(PaymentGatewayException):
    """Raised when a gateway is not properly configured"""
    
    def __init__(self, gateway: str, missing_fields: list = None):
        missing_fields = missing_fields or []
        message = f"Gateway '{gateway}' is not properly configured"
        if missing_fields:
            message += f". Missing fields: {', '.join(missing_fields)}"
        
        super().__init__(
            message=message,
            error_code="GATEWAY_NOT_CONFIGURED",
            status_code=422,
            details={"gateway": gateway, "missing_fields": missing_fields}
        )


class PaymentProcessingException(PaymentGatewayException):
    """Raised when payment processing fails"""
    
    def __init__(self, message: str, gateway: str, payment_id: str = None):
        super().__init__(
            message=message,
            error_code="PAYMENT_PROCESSING_FAILED",
            status_code=422,
            details={"gateway": gateway, "payment_id": payment_id}
        )


class TenantNotFoundException(PaymentGatewayException):
    """Raised when a tenant is not found"""
    
    def __init__(self, tenant_id: str):
        message = f"Tenant '{tenant_id}' not found"
        super().__init__(
            message=message,
            error_code="TENANT_NOT_FOUND",
            status_code=404,
            details={"tenant_id": tenant_id}
        )


async def handle_payment_exception(request: Request, exc: PaymentGatewayException) -> JSONResponse:
    """
    Global exception handler for payment gateway exceptions.
    
    Args:
        request: FastAPI request object
        exc: Payment gateway exception
        
    Returns:
        JSONResponse: Formatted error response
    """
    logger.error(
        f"Payment gateway exception: {exc.message}",
        extra={
            "error_code": exc.error_code,
            "status_code": exc.status_code,
            "details": exc.details,
            "path": str(request.url),
            "method": request.method
        }
    )
    
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "error": {
                "code": exc.error_code,
                "message": exc.message,
                "details": exc.details
            },
            "request_id": getattr(request.state, "request_id", None),
            "timestamp": "2024-01-01T00:00:00Z"  # Would use actual timestamp
        }
    )
