name: Deploy to <PERSON><PERSON>

on:
  push:
    branches: [ main, production ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: test_eloh_gateway
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python 3.11
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
    
    - name: Cache pip dependencies
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('**/requirements.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
    
    - name: Set up test environment
      env:
        DATABASE_URL: postgresql://postgres:postgres@localhost:5432/test_eloh_gateway
        SECRET_KEY: test-secret-key-for-github-actions
        ENVIRONMENT: test
        DEBUG: true
      run: |
        python setup_database.py
    
    - name: Run tests
      env:
        DATABASE_URL: postgresql://postgres:postgres@localhost:5432/test_eloh_gateway
        SECRET_KEY: test-secret-key-for-github-actions
        ENVIRONMENT: test
        DEBUG: true
      run: |
        python test_api.py
    
    - name: Test health check
      env:
        DATABASE_URL: postgresql://postgres:postgres@localhost:5432/test_eloh_gateway
        SECRET_KEY: test-secret-key-for-github-actions
        ENVIRONMENT: test
        DEBUG: true
      run: |
        python -c "
        import asyncio
        from main import app
        from fastapi.testclient import TestClient
        
        client = TestClient(app)
        response = client.get('/health')
        print(f'Health check status: {response.status_code}')
        print(f'Health check response: {response.json()}')
        assert response.status_code == 200
        "

  deploy:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    
    steps:
    - name: Deploy to Render
      uses: johnbeynon/render-deploy-action@v0.0.8
      with:
        service-id: ${{ secrets.RENDER_SERVICE_ID }}
        api-key: ${{ secrets.RENDER_API_KEY }}
        
    - name: Wait for deployment
      run: |
        echo "Waiting for Render deployment to complete..."
        sleep 60
        
    - name: Test deployed service
      run: |
        # Test health endpoint
        curl -f ${{ secrets.RENDER_SERVICE_URL }}/health || exit 1
        echo "Deployment health check passed!"
        
        # Test API docs
        curl -f ${{ secrets.RENDER_SERVICE_URL }}/docs || exit 1
        echo "API docs accessible!"
        
        # Test tenant portal
        curl -f ${{ secrets.RENDER_SERVICE_URL }}/v1/portal || exit 1
        echo "Tenant portal accessible!"

  notify:
    needs: [test, deploy]
    runs-on: ubuntu-latest
    if: always()
    
    steps:
    - name: Notify deployment status
      run: |
        if [ "${{ needs.deploy.result }}" == "success" ]; then
          echo "✅ Deployment successful!"
          echo "🌐 Service URL: ${{ secrets.RENDER_SERVICE_URL }}"
          echo "📚 API Docs: ${{ secrets.RENDER_SERVICE_URL }}/docs"
          echo "🏢 Tenant Portal: ${{ secrets.RENDER_SERVICE_URL }}/v1/portal"
        else
          echo "❌ Deployment failed!"
          exit 1
        fi
