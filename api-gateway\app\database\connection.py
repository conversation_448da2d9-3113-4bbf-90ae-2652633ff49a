"""
Database connection and session management for ELOH Processing Payment Gateway

This module handles database connections, session management, and provides
utilities for database operations.
"""

from sqlalchemy import create_engine, event
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import StaticPool
import logging
from typing import Generator
from contextlib import contextmanager

from ..core.config import get_settings
from .models import Base

logger = logging.getLogger(__name__)


class DatabaseManager:
    """
    Database connection and session manager.
    
    Handles database connections, session lifecycle, and provides
    utilities for database operations.
    """
    
    def __init__(self):
        self.settings = get_settings()
        self.engine = None
        self.SessionLocal = None
        self._initialize_database()
    
    def _initialize_database(self):
        """Initialize database connection and session factory"""
        
        # Get database URL from settings
        database_url = self.settings.database_url
        
        if not database_url:
            # Default to SQLite for development
            database_url = "sqlite:///./eloh_gateway.db"
            logger.warning("No DATABASE_URL configured, using SQLite: ./eloh_gateway.db")
        
        # Create engine with appropriate settings
        if database_url.startswith("sqlite"):
            # SQLite-specific settings
            self.engine = create_engine(
                database_url,
                connect_args={"check_same_thread": False},
                poolclass=StaticPool,
                echo=self.settings.debug
            )
        else:
            # PostgreSQL/MySQL settings
            self.engine = create_engine(
                database_url,
                pool_size=10,
                max_overflow=20,
                pool_timeout=30,
                pool_recycle=3600,
                echo=self.settings.debug
            )
        
        # Create session factory
        self.SessionLocal = sessionmaker(
            autocommit=False,
            autoflush=False,
            bind=self.engine
        )
        
        # Add event listeners
        self._add_event_listeners()
        
        logger.info(f"Database initialized: {database_url}")
    
    def _add_event_listeners(self):
        """Add database event listeners for logging and monitoring"""
        
        @event.listens_for(self.engine, "connect")
        def set_sqlite_pragma(dbapi_connection, connection_record):
            """Set SQLite pragmas for better performance"""
            if "sqlite" in str(self.engine.url):
                cursor = dbapi_connection.cursor()
                cursor.execute("PRAGMA foreign_keys=ON")
                cursor.execute("PRAGMA journal_mode=WAL")
                cursor.execute("PRAGMA synchronous=NORMAL")
                cursor.execute("PRAGMA cache_size=1000")
                cursor.execute("PRAGMA temp_store=MEMORY")
                cursor.close()
        
        @event.listens_for(self.engine, "before_cursor_execute")
        def receive_before_cursor_execute(conn, cursor, statement, parameters, context, executemany):
            """Log slow queries in debug mode"""
            if self.settings.debug:
                context._query_start_time = logger.time()
        
        @event.listens_for(self.engine, "after_cursor_execute")
        def receive_after_cursor_execute(conn, cursor, statement, parameters, context, executemany):
            """Log query execution time in debug mode"""
            if self.settings.debug and hasattr(context, '_query_start_time'):
                total = logger.time() - context._query_start_time
                if total > 0.1:  # Log queries taking more than 100ms
                    logger.debug(f"Slow query ({total:.3f}s): {statement[:100]}...")
    
    def create_tables(self):
        """Create all database tables"""
        try:
            Base.metadata.create_all(bind=self.engine)
            logger.info("Database tables created successfully")
        except Exception as e:
            logger.error(f"Failed to create database tables: {e}")
            raise
    
    def drop_tables(self):
        """Drop all database tables (use with caution!)"""
        try:
            Base.metadata.drop_all(bind=self.engine)
            logger.warning("All database tables dropped")
        except Exception as e:
            logger.error(f"Failed to drop database tables: {e}")
            raise
    
    def get_session(self) -> Session:
        """Get a new database session"""
        return self.SessionLocal()
    
    @contextmanager
    def session_scope(self):
        """
        Provide a transactional scope around a series of operations.
        
        Usage:
            with db_manager.session_scope() as session:
                # Database operations
                session.add(obj)
                # Automatically commits on success, rolls back on exception
        """
        session = self.get_session()
        try:
            yield session
            session.commit()
        except Exception:
            session.rollback()
            raise
        finally:
            session.close()
    
    def health_check(self) -> bool:
        """Check database connectivity"""
        try:
            with self.session_scope() as session:
                session.execute("SELECT 1")
            return True
        except Exception as e:
            logger.error(f"Database health check failed: {e}")
            return False


# Global database manager instance
_db_manager = None


def get_database_manager() -> DatabaseManager:
    """Get the global database manager instance"""
    global _db_manager
    if _db_manager is None:
        _db_manager = DatabaseManager()
    return _db_manager


def get_db_session() -> Generator[Session, None, None]:
    """
    FastAPI dependency to get database session.
    
    Usage in FastAPI endpoints:
        @app.get("/items/")
        def read_items(db: Session = Depends(get_db_session)):
            return db.query(Item).all()
    """
    db_manager = get_database_manager()
    session = db_manager.get_session()
    try:
        yield session
    finally:
        session.close()


def init_database():
    """Initialize database and create tables"""
    db_manager = get_database_manager()
    db_manager.create_tables()


def reset_database():
    """Reset database (drop and recreate all tables)"""
    db_manager = get_database_manager()
    db_manager.drop_tables()
    db_manager.create_tables()


# Database utilities

def execute_sql_file(file_path: str):
    """Execute SQL commands from a file"""
    db_manager = get_database_manager()
    
    try:
        with open(file_path, 'r') as file:
            sql_commands = file.read()
        
        with db_manager.session_scope() as session:
            # Split and execute each command
            for command in sql_commands.split(';'):
                command = command.strip()
                if command:
                    session.execute(command)
        
        logger.info(f"SQL file executed successfully: {file_path}")
        
    except Exception as e:
        logger.error(f"Failed to execute SQL file {file_path}: {e}")
        raise


def backup_database(backup_path: str):
    """Create a database backup (SQLite only)"""
    db_manager = get_database_manager()
    
    if not str(db_manager.engine.url).startswith("sqlite"):
        raise ValueError("Database backup only supported for SQLite")
    
    try:
        import shutil
        import os
        
        # Get the database file path
        db_path = str(db_manager.engine.url).replace("sqlite:///", "")
        
        # Create backup
        shutil.copy2(db_path, backup_path)
        logger.info(f"Database backed up to: {backup_path}")
        
    except Exception as e:
        logger.error(f"Database backup failed: {e}")
        raise


def restore_database(backup_path: str):
    """Restore database from backup (SQLite only)"""
    db_manager = get_database_manager()
    
    if not str(db_manager.engine.url).startswith("sqlite"):
        raise ValueError("Database restore only supported for SQLite")
    
    try:
        import shutil
        import os
        
        # Get the database file path
        db_path = str(db_manager.engine.url).replace("sqlite:///", "")
        
        # Close all connections
        db_manager.engine.dispose()
        
        # Restore backup
        shutil.copy2(backup_path, db_path)
        
        # Reinitialize database manager
        db_manager._initialize_database()
        
        logger.info(f"Database restored from: {backup_path}")
        
    except Exception as e:
        logger.error(f"Database restore failed: {e}")
        raise
