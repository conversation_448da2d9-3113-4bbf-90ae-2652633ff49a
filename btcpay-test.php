<?php
// BTCPay Server Integration Test Page
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once "includes/btcpay-gateway.php";

echo "<h1>BTCPay Server Integration Test</h1>";

// Test 1: Configuration Check
echo "<h2>Test 1: Configuration Check</h2>";
try {
    $gateway = new BTCPay_Gateway();
    $debug = $gateway->getDebugInfo();
    
    echo "<h3>Configuration Status:</h3>";
    foreach ($debug as $key => $value) {
        $status = is_bool($value) ? ($value ? '✅ Yes' : '❌ No') : htmlspecialchars($value);
        echo "<strong>$key:</strong> $status<br>";
    }
    
    if (!$debug['api_key_set']) {
        echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h3>⚠️ Configuration Required</h3>";
        echo "<p>Please update the following in <code>includes/btcpay-config.php</code>:</p>";
        echo "<ul>";
        echo "<li><strong>host:</strong> Your BTCPay Server URL (e.g., https://your-btcpay-server.com)</li>";
        echo "<li><strong>api_key:</strong> Your BTCPay Server API key</li>";
        echo "<li><strong>store_id:</strong> Your BTCPay Server Store ID</li>";
        echo "<li><strong>webhook_secret:</strong> A secure random string for webhook validation</li>";
        echo "</ul>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . htmlspecialchars($e->getMessage()) . "<br>";
}

// Test 2: API Permissions Check
echo "<h2>Test 2: API Permissions Check</h2>";
echo "<p>Your API key should have these permissions:</p>";
echo "<ul>";
echo "<li>✅ btcpay.store.canviewinvoices</li>";
echo "<li>✅ btcpay.store.cancreateinvoice</li>";
echo "<li>✅ btcpay.store.canmodifyinvoices</li>";
echo "<li>✅ btcpay.store.webhooks.canmodifywebhooks</li>";
echo "<li>✅ btcpay.store.canmodifystoresettings</li>";
echo "<li>✅ btcpay.store.canviewstoresettings</li>";
echo "</ul>";

// Test 3: Test Invoice Creation (if configured)
if (isset($debug) && $debug['api_key_set']) {
    echo "<h2>Test 3: Test Invoice Creation</h2>";
    
    if (isset($_POST['test_invoice'])) {
        try {
            $testAmount = 0.01; // $0.01 USD test
            $testOrderId = "TEST_" . time();
            
            $invoice = $gateway->createInvoice(
                $testAmount,
                'USD',
                $testOrderId,
                '<EMAIL>',
                'Test Invoice from ELOH Processing'
            );
            
            if ($invoice['success']) {
                echo "<div style='background: #d4edda; color: #155724; padding: 15px; border-radius: 5px;'>";
                echo "<h3>✅ Test Invoice Created Successfully!</h3>";
                echo "<p><strong>Invoice ID:</strong> " . htmlspecialchars($invoice['invoice_id']) . "</p>";
                echo "<p><strong>Amount:</strong> $" . $invoice['amount'] . " " . $invoice['currency'] . "</p>";
                echo "<p><strong>Status:</strong> " . htmlspecialchars($invoice['status']) . "</p>";
                echo "<p><strong>Checkout Link:</strong> <a href='" . htmlspecialchars($invoice['checkout_link']) . "' target='_blank'>Open BTCPay Checkout</a></p>";
                echo "</div>";
            } else {
                echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px;'>";
                echo "<h3>❌ Test Invoice Creation Failed</h3>";
                echo "<p><strong>Error:</strong> " . htmlspecialchars($invoice['error']) . "</p>";
                if (isset($invoice['details'])) {
                    echo "<p><strong>Details:</strong> " . htmlspecialchars(json_encode($invoice['details'])) . "</p>";
                }
                echo "</div>";
            }
            
        } catch (Exception $e) {
            echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px;'>";
            echo "<h3>❌ Exception During Test</h3>";
            echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
            echo "</div>";
        }
    } else {
        echo "<form method='POST'>";
        echo "<button type='submit' name='test_invoice' style='background: #0077cc; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;'>Create Test Invoice ($0.01)</button>";
        echo "</form>";
    }
}

// Test 4: Webhook Setup
echo "<h2>Test 4: Webhook Setup</h2>";
if (isset($debug) && $debug['api_key_set']) {
    if (isset($_POST['setup_webhook'])) {
        try {
            $webhookResult = $gateway->setupWebhook();
            
            if ($webhookResult['success']) {
                echo "<div style='background: #d4edda; color: #155724; padding: 15px; border-radius: 5px;'>";
                echo "<h3>✅ Webhook Setup Successful!</h3>";
                echo "<p>Webhook URL: " . htmlspecialchars($debug['webhook_url']) . "</p>";
                echo "</div>";
            } else {
                echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px;'>";
                echo "<h3>❌ Webhook Setup Failed</h3>";
                echo "<p>Please check your API permissions and try again.</p>";
                echo "</div>";
            }
            
        } catch (Exception $e) {
            echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px;'>";
            echo "<h3>❌ Webhook Setup Error</h3>";
            echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
            echo "</div>";
        }
    } else {
        echo "<p>Webhook URL: <code>" . htmlspecialchars($debug['webhook_url']) . "</code></p>";
        echo "<form method='POST'>";
        echo "<button type='submit' name='setup_webhook' style='background: #28a745; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;'>Setup Webhook</button>";
        echo "</form>";
    }
} else {
    echo "<p>❌ Configure API key first to test webhook setup.</p>";
}

echo "<hr>";
echo "<h2>Next Steps:</h2>";
echo "<ol>";
echo "<li><strong>Configure BTCPay Server:</strong> Update <code>includes/btcpay-config.php</code> with your settings</li>";
echo "<li><strong>Test Invoice Creation:</strong> Use the test button above</li>";
echo "<li><strong>Setup Webhook:</strong> Use the webhook setup button</li>";
echo "<li><strong>Test Payment Form:</strong> <a href='btcpay-payment-form.php'>Try the payment form</a></li>";
echo "<li><strong>Update Your Site:</strong> Replace old payment links with BTCPay forms</li>";
echo "</ol>";

echo "<hr>";
echo "<p><a href='index.php'>← Back to Homepage</a> | <a href='btcpay-payment-form.php'>Try Payment Form</a></p>";
?>
