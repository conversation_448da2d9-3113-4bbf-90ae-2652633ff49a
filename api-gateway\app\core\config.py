"""
Configuration management for ELOH Processing Payment Gateway API

This module handles all configuration settings for the payment gateway,
including environment variables, database settings, and gateway credentials.
"""

from pydantic import BaseSettings, Field
from typing import Optional, Dict, Any, List
import os
from functools import lru_cache


class Settings(BaseSettings):
    """
    Application settings loaded from environment variables.
    
    This class uses Pydantic BaseSettings to automatically load
    configuration from environment variables with type validation.
    """
    
    # Application settings
    app_name: str = Field(default="ELOH Processing Payment Gateway", env="APP_NAME")
    app_version: str = Field(default="1.0.0", env="APP_VERSION")
    debug: bool = Field(default=False, env="DEBUG")
    environment: str = Field(default="development", env="ENVIRONMENT")
    
    # API settings
    api_host: str = Field(default="0.0.0.0", env="API_HOST")
    api_port: int = Field(default=8000, env="API_PORT")
    api_prefix: str = Field(default="/v1", env="API_PREFIX")
    
    # Security settings
    secret_key: str = Field(default="your-secret-key-change-in-production", env="SECRET_KEY")
    access_token_expire_minutes: int = Field(default=30, env="ACCESS_TOKEN_EXPIRE_MINUTES")
    
    # Database settings
    database_url: Optional[str] = Field(default=None, env="DATABASE_URL")
    redis_url: Optional[str] = Field(default=None, env="REDIS_URL")
    
    # Logging settings
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    log_format: str = Field(default="json", env="LOG_FORMAT")  # json or text
    
    # Gateway credentials (encrypted in production)
    # Stripe
    stripe_secret_key: Optional[str] = Field(default=None, env="STRIPE_SECRET_KEY")
    stripe_publishable_key: Optional[str] = Field(default=None, env="STRIPE_PUBLISHABLE_KEY")
    stripe_webhook_secret: Optional[str] = Field(default=None, env="STRIPE_WEBHOOK_SECRET")
    
    # Square
    square_application_id: Optional[str] = Field(default=None, env="SQUARE_APPLICATION_ID")
    square_access_token: Optional[str] = Field(default=None, env="SQUARE_ACCESS_TOKEN")
    square_webhook_signature_key: Optional[str] = Field(default=None, env="SQUARE_WEBHOOK_SIGNATURE_KEY")
    square_environment: str = Field(default="sandbox", env="SQUARE_ENVIRONMENT")  # sandbox or production
    
    # BTCPay Server
    btcpay_server_url: Optional[str] = Field(default=None, env="BTCPAY_SERVER_URL")
    btcpay_api_key: Optional[str] = Field(default=None, env="BTCPAY_API_KEY")
    btcpay_store_id: Optional[str] = Field(default=None, env="BTCPAY_STORE_ID")
    btcpay_webhook_secret: Optional[str] = Field(default=None, env="BTCPAY_WEBHOOK_SECRET")
    
    # NowPayments
    nowpayments_api_key: Optional[str] = Field(default=None, env="NOWPAYMENTS_API_KEY")
    nowpayments_ipn_secret: Optional[str] = Field(default=None, env="NOWPAYMENTS_IPN_SECRET")
    nowpayments_environment: str = Field(default="sandbox", env="NOWPAYMENTS_ENVIRONMENT")  # sandbox or production
    
    # Routing settings
    default_routing_strategy: str = Field(default="rule_based", env="DEFAULT_ROUTING_STRATEGY")
    enable_ai_routing: bool = Field(default=False, env="ENABLE_AI_ROUTING")
    ai_routing_endpoint: Optional[str] = Field(default=None, env="AI_ROUTING_ENDPOINT")
    
    # Rate limiting
    rate_limit_enabled: bool = Field(default=True, env="RATE_LIMIT_ENABLED")
    rate_limit_requests_per_minute: int = Field(default=100, env="RATE_LIMIT_REQUESTS_PER_MINUTE")
    
    # Monitoring and observability
    enable_metrics: bool = Field(default=True, env="ENABLE_METRICS")
    metrics_endpoint: str = Field(default="/metrics", env="METRICS_ENDPOINT")
    enable_tracing: bool = Field(default=False, env="ENABLE_TRACING")
    jaeger_endpoint: Optional[str] = Field(default=None, env="JAEGER_ENDPOINT")
    
    # Webhook settings
    webhook_timeout: int = Field(default=30, env="WEBHOOK_TIMEOUT")
    webhook_retry_attempts: int = Field(default=3, env="WEBHOOK_RETRY_ATTEMPTS")
    webhook_retry_delay: int = Field(default=5, env="WEBHOOK_RETRY_DELAY")
    
    # Cache settings
    cache_ttl: int = Field(default=300, env="CACHE_TTL")  # 5 minutes
    cache_enabled: bool = Field(default=True, env="CACHE_ENABLED")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False
    
    def get_gateway_credentials(self, gateway: str) -> Optional[Dict[str, Any]]:
        """
        Get credentials for a specific gateway.
        
        Args:
            gateway: Gateway identifier (stripe, square, btcpay, nowpayments)
            
        Returns:
            Optional[Dict[str, Any]]: Gateway credentials or None if not configured
        """
        credentials_map = {
            "stripe": {
                "secret_key": self.stripe_secret_key,
                "publishable_key": self.stripe_publishable_key,
                "webhook_secret": self.stripe_webhook_secret
            },
            "square": {
                "application_id": self.square_application_id,
                "access_token": self.square_access_token,
                "webhook_signature_key": self.square_webhook_signature_key,
                "environment": self.square_environment
            },
            "btcpay": {
                "server_url": self.btcpay_server_url,
                "api_key": self.btcpay_api_key,
                "store_id": self.btcpay_store_id,
                "webhook_secret": self.btcpay_webhook_secret
            },
            "nowpayments": {
                "api_key": self.nowpayments_api_key,
                "ipn_secret": self.nowpayments_ipn_secret,
                "environment": self.nowpayments_environment
            }
        }
        
        return credentials_map.get(gateway)
    
    def is_gateway_configured(self, gateway: str) -> bool:
        """
        Check if a gateway is properly configured.
        
        Args:
            gateway: Gateway identifier
            
        Returns:
            bool: True if gateway is configured
        """
        credentials = self.get_gateway_credentials(gateway)
        if not credentials:
            return False
        
        # Check required fields for each gateway
        required_fields = {
            "stripe": ["secret_key"],
            "square": ["application_id", "access_token"],
            "btcpay": ["server_url", "api_key", "store_id"],
            "nowpayments": ["api_key"]
        }
        
        required = required_fields.get(gateway, [])
        return all(credentials.get(field) for field in required)
    
    def get_configured_gateways(self) -> List[str]:
        """
        Get list of properly configured gateways.
        
        Returns:
            List[str]: List of configured gateway identifiers
        """
        gateways = ["stripe", "square", "btcpay", "nowpayments"]
        return [gateway for gateway in gateways if self.is_gateway_configured(gateway)]
    
    def get_database_config(self) -> Dict[str, Any]:
        """
        Get database configuration.
        
        Returns:
            Dict[str, Any]: Database configuration
        """
        return {
            "url": self.database_url,
            "echo": self.debug,
            "pool_size": 10,
            "max_overflow": 20,
            "pool_timeout": 30,
            "pool_recycle": 3600
        }
    
    def get_redis_config(self) -> Dict[str, Any]:
        """
        Get Redis configuration.
        
        Returns:
            Dict[str, Any]: Redis configuration
        """
        return {
            "url": self.redis_url,
            "decode_responses": True,
            "socket_timeout": 5,
            "socket_connect_timeout": 5,
            "retry_on_timeout": True
        }
    
    def get_cors_config(self) -> Dict[str, Any]:
        """
        Get CORS configuration.
        
        Returns:
            Dict[str, Any]: CORS configuration
        """
        if self.environment == "production":
            return {
                "allow_origins": ["https://elohprocessing.infy.uk"],
                "allow_credentials": True,
                "allow_methods": ["GET", "POST", "PUT", "DELETE"],
                "allow_headers": ["*"]
            }
        else:
            return {
                "allow_origins": ["*"],
                "allow_credentials": True,
                "allow_methods": ["*"],
                "allow_headers": ["*"]
            }


@lru_cache()
def get_settings() -> Settings:
    """
    Get cached settings instance.
    
    This function uses LRU cache to ensure settings are loaded only once
    and reused throughout the application lifecycle.
    
    Returns:
        Settings: Application settings
    """
    return Settings()


# Environment-specific configurations
def get_development_config() -> Dict[str, Any]:
    """Get development environment configuration"""
    return {
        "debug": True,
        "log_level": "DEBUG",
        "rate_limit_enabled": False,
        "enable_metrics": False,
        "cache_enabled": False
    }


def get_production_config() -> Dict[str, Any]:
    """Get production environment configuration"""
    return {
        "debug": False,
        "log_level": "INFO",
        "rate_limit_enabled": True,
        "enable_metrics": True,
        "cache_enabled": True,
        "enable_tracing": True
    }


def get_test_config() -> Dict[str, Any]:
    """Get test environment configuration"""
    return {
        "debug": True,
        "log_level": "DEBUG",
        "rate_limit_enabled": False,
        "enable_metrics": False,
        "cache_enabled": False,
        "database_url": "sqlite:///./test.db"
    }
