const BTCPayClient = require('btcpay-nodejs');
const config = require('../../config/btcpay-config.json');

const client = new BTCPayClient(config.serverUrl, config.apiKey);

async function createInvoice(amount, currency, orderId, redirectUrl, merchantId) {
  const invoiceData = {
    price: amount,
    currency: currency,
    orderId: orderId,
    redirectURL: redirectUrl,
    metadata: {
      merchantId: merchantId
    }
  };
  
  const response = await client.createInvoice(invoiceData);
  return {
    invoiceId: response.id,
    paymentUrl: response.url,
    addresses: response.cryptoInfo.map(info => ({
      currency: info.cryptoCode,
      address: info.address,
      paymentLink: info.paymentLink || null
    }))
  };
}

// Additional functions for checking status, etc.

module.exports = { createInvoice, getInvoiceStatus };