#!/usr/bin/env python3
"""
Database setup script for ELOH Processing Payment Gateway (Render Optimized)
"""

import asyncio
import sys
import os
from pathlib import Path

# Add the app directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

from app.database.connection import get_database_manager, init_database
from app.database.models import Tenant, GatewayConfig
from app.core.logging import setup_logging, get_logger

# Setup logging
setup_logging()
logger = get_logger(__name__)


def create_sample_tenant():
    """Create a sample tenant for testing"""
    try:
        db_manager = get_database_manager()
        
        with db_manager.session_scope() as session:
            # Check if sample tenant already exists
            existing_tenant = session.query(Tenant).filter(
                Tenant.company_name == "Demo Caribbean Store"
            ).first()
            
            if existing_tenant:
                logger.info(f"Sample tenant already exists: {existing_tenant.tenant_id}")
                return existing_tenant
            
            # Create sample tenant
            tenant = Tenant(
                company_name="Demo Caribbean Store",
                business_type="E-commerce",
                website_url="https://demo-caribbean-store.com",
                contact_email="<EMAIL>",
                contact_name="Demo Admin",
                contact_phone="******-555-0123",
                address_line1="123 Caribbean Street",
                city="Roseau",
                state="Saint George",
                postal_code="00000",
                country="DM",  # Dominica
                plan="professional"
            )
            
            session.add(tenant)
            session.flush()  # Get the ID
            
            # Create sample gateway configurations
            # BTCPay Server configuration
            btcpay_config = GatewayConfig(
                tenant_id=tenant.id,
                gateway_id="btcpay",
                enabled=True,
                priority=1,
                credentials={
                    "server_url": "https://demo.btcpayserver.org",
                    "api_key": "demo_api_key_replace_with_real",
                    "store_id": "demo_store_id_replace_with_real",
                    "webhook_secret": "demo_webhook_secret"
                },
                configuration={
                    "speed_policy": "MediumSpeed",
                    "expiration_minutes": 60
                },
                min_amount=0.01,
                max_amount=10000.0,
                daily_limit=50000.0,
                webhook_url="https://demo-caribbean-store.com/webhooks/btcpay",
                webhook_events=["invoice_settled", "invoice_expired"]
            )
            
            # NowPayments configuration
            nowpayments_config = GatewayConfig(
                tenant_id=tenant.id,
                gateway_id="nowpayments",
                enabled=True,
                priority=2,
                credentials={
                    "api_key": "demo_nowpayments_api_key_replace_with_real",
                    "ipn_secret": "demo_ipn_secret",
                    "environment": "sandbox"
                },
                configuration={
                    "default_pay_currency": "btc"
                },
                min_amount=1.0,
                max_amount=25000.0,
                daily_limit=100000.0,
                webhook_url="https://demo-caribbean-store.com/webhooks/nowpayments",
                webhook_events=["payment.finished", "payment.failed"]
            )
            
            session.add(btcpay_config)
            session.add(nowpayments_config)
            
            logger.info(f"Sample tenant created: {tenant.tenant_id}")
            logger.info(f"API Key: {tenant.api_key}")
            logger.info(f"Plan: {tenant.plan}")
            logger.info(f"Configured Gateways: BTCPay Server, NowPayments")
            
            return tenant
        
    except Exception as e:
        logger.error(f"Failed to create sample tenant: {e}")
        return None


def print_database_info():
    """Print database information and statistics"""
    try:
        db_manager = get_database_manager()
        
        with db_manager.session_scope() as session:
            # Count records in each table
            tenant_count = session.query(Tenant).count()
            gateway_count = session.query(GatewayConfig).count()
            
            print("\n" + "="*50)
            print("DATABASE INFORMATION")
            print("="*50)
            print(f"Database URL: {db_manager.engine.url}")
            print(f"Database Health: {'✅ Healthy' if db_manager.health_check() else '❌ Unhealthy'}")
            print("\nTable Statistics:")
            print("-"*30)
            print(f"{'Tenants':<15}: {tenant_count:>6} records")
            print(f"{'Gateway Configs':<15}: {gateway_count:>6} records")
            print("="*50)
        
    except Exception as e:
        logger.error(f"Failed to get database info: {e}")


def main():
    """Main setup function"""
    print("🚀 ELOH Processing Payment Gateway - Database Setup (Render)")
    print("="*60)
    
    # Parse command line arguments
    import argparse
    parser = argparse.ArgumentParser(description="Setup ELOH Processing Payment Gateway Database")
    parser.add_argument("--sample-data", action="store_true", help="Create sample tenant data")
    parser.add_argument("--info", action="store_true", help="Show database information")
    
    args = parser.parse_args()
    
    try:
        # Initialize database
        print("📊 Initializing database...")
        init_database()
        print("✅ Database initialized")
        
        # Create sample data if requested
        if args.sample_data:
            print("📝 Creating sample tenant data...")
            sample_tenant = create_sample_tenant()
            if sample_tenant:
                print(f"✅ Sample tenant created: {sample_tenant.company_name}")
                print(f"   Tenant ID: {sample_tenant.tenant_id}")
                print(f"   API Key: {sample_tenant.api_key}")
                print(f"   Plan: {sample_tenant.plan}")
        
        # Show database info
        if args.info or not args.sample_data:
            print_database_info()
        
        print("\n🎉 Database setup completed successfully!")
        
        # Render-specific information
        if os.getenv("RENDER_SERVICE_NAME"):
            print("\n🌐 Render Deployment Detected")
            print("📚 Next Steps:")
            print("1. Set environment variables in Render dashboard")
            print("2. Configure gateway credentials")
            print("3. Test API endpoints")
            print(f"4. Visit your app URL + /v1/portal")
        else:
            print("\n📚 Next Steps:")
            print("1. Start the API server: uvicorn main:app --reload")
            print("2. Visit http://localhost:8000/v1/portal for tenant portal")
            print("3. Visit http://localhost:8000/docs for API documentation")
        
        if args.sample_data and sample_tenant:
            print(f"\n🔑 Sample Tenant API Key: {sample_tenant.api_key}")
            print("   Use this key to test the API endpoints")
        
    except Exception as e:
        logger.error(f"Database setup failed: {e}")
        print(f"\n❌ Database setup failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
