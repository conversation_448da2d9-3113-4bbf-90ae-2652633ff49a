<?php
/**
 * ELOH Processing Widget API
 * Handles payment processing for embedded widgets
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'error' => 'Method not allowed']);
    exit;
}

require_once __DIR__ . '/../includes/payment-gateway-manager.php';
require_once __DIR__ . '/widget-config.php';

try {
    // Get and validate widget ID
    $widget_id = sanitize_text_field($_POST['widget_id'] ?? '');
    if (empty($widget_id)) {
        throw new Exception('Widget ID is required');
    }

    // Load widget configuration
    $widget_config = new Widget_Config($widget_id);

    // Validate referring domain
    $referer = $_SERVER['HTTP_REFERER'] ?? '';
    if ($referer) {
        $referer_domain = parse_url($referer, PHP_URL_HOST);
        if (!$widget_config->isDomainAllowed($referer_domain)) {
            throw new Exception('Domain not allowed');
        }
    }

    // Get action
    $action = sanitize_text_field($_POST['action'] ?? '');

    switch ($action) {
        case 'create_payment':
            handleCreatePayment($widget_config);
            break;

        case 'get_status':
            handleGetStatus($widget_config);
            break;

        default:
            throw new Exception('Invalid action');
    }

} catch (Exception $e) {
    error_log("Widget API Error: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}

/**
 * Handle payment creation
 */
function handleCreatePayment($widget_config) {
    // Validate and sanitize input
    $gateway = sanitize_text_field($_POST['gateway'] ?? '');
    $amount = floatval($_POST['amount'] ?? 0);
    $email = sanitize_email($_POST['email'] ?? '');
    $description = sanitize_text_field($_POST['description'] ?? '');

    // Validation
    $errors = [];

    // Validate gateway
    $enabled_gateways = $widget_config->get('enabled_gateways');
    if (!in_array($gateway, $enabled_gateways)) {
        $errors[] = 'Invalid payment gateway';
    }

    // Validate amount
    $min_amount = $widget_config->get('min_amount');
    $max_amount = $widget_config->get('max_amount');
    if ($amount < $min_amount) {
        $errors[] = "Minimum amount is $" . number_format($min_amount, 2);
    }
    if ($amount > $max_amount) {
        $errors[] = "Maximum amount is $" . number_format($max_amount, 2);
    }

    // Validate email if required
    if ($widget_config->get('require_email') && empty($email)) {
        $errors[] = 'Email address is required';
    }
    if (!empty($email) && !filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = 'Invalid email address';
    }

    // Validate description if required
    if ($widget_config->get('require_description') && empty($description)) {
        $errors[] = 'Description is required';
    }

    if (!empty($errors)) {
        echo json_encode([
            'success' => false,
            'error' => implode(', ', $errors)
        ]);
        return;
    }

    // Generate order ID
    $order_id = 'WIDGET_' . strtoupper($widget_config->get('widget_id', 'DEFAULT')) . '_' . time() . '_' . rand(1000, 9999);

    // Prepare payment description
    $payment_description = $description ?: 'Widget Payment';
    if ($widget_config->get('company_name')) {
        $payment_description = $widget_config->get('company_name') . ' - ' . $payment_description;
    }

    // Create payment gateway manager
    $gatewayManager = new Payment_Gateway_Manager();

    // Get default currency for the gateway
    $currency = 'BTC'; // Default for BTCPay
    if ($gateway === 'nowpayments') {
        $currency = 'btc'; // NowPayments format
    } elseif ($gateway === 'square') {
        $currency = 'USD'; // Square uses USD
    }

    try {
        // Create payment using widget-specific method
        $payment = $gatewayManager->createWidgetPayment(
            $gateway,
            $amount,
            $currency,
            $order_id,
            $payment_description,
            $email,
            $widget_config->getAll()
        );

        if ($payment['success']) {
            // Store payment data for tracking
            storeWidgetPayment($widget_config->get('widget_id'), $order_id, [
                'gateway' => $gateway,
                'amount' => $amount,
                'currency' => $currency,
                'email' => $email,
                'description' => $payment_description,
                'created_at' => time(),
                'payment_data' => $payment,
                'widget_config' => $widget_config->getAll()
            ]);

            // Determine redirect URL
            $redirect_url = '';
            switch ($gateway) {
                case 'btcpay':
                    $redirect_url = $payment['checkout_link'] ?? '';
                    break;
                case 'nowpayments':
                    $redirect_url = "nowpayments-checkout.php?payment_id=" . $payment['payment_id'] . "&order=" . $order_id;
                    break;
                case 'square':
                    // Square payments are processed differently
                    $redirect_url = $widget_config->get('success_redirect') ?: '';
                    break;
            }

            // Send webhook notification if configured
            $webhook_url = $widget_config->get('webhook_url');
            if ($webhook_url) {
                sendWebhookNotification($webhook_url, [
                    'event' => 'payment_created',
                    'order_id' => $order_id,
                    'amount' => $amount,
                    'currency' => $currency,
                    'gateway' => $gateway,
                    'email' => $email,
                    'description' => $payment_description,
                    'payment_data' => $payment
                ]);
            }

            echo json_encode([
                'success' => true,
                'order_id' => $order_id,
                'redirect_url' => $redirect_url,
                'payment_data' => $payment,
                'message' => 'Payment created successfully'
            ]);

        } else {
            throw new Exception($payment['error'] ?? 'Failed to create payment');
        }

    } catch (Exception $e) {
        error_log("Widget payment creation error: " . $e->getMessage());
        echo json_encode([
            'success' => false,
            'error' => 'Payment processing failed: ' . $e->getMessage()
        ]);
    }
}

/**
 * Handle payment status check
 */
function handleGetStatus($widget_config) {
    $order_id = sanitize_text_field($_POST['order_id'] ?? '');

    if (empty($order_id)) {
        echo json_encode([
            'success' => false,
            'error' => 'Order ID is required'
        ]);
        return;
    }

    // Load payment data
    $payment_data = loadWidgetPayment($order_id);

    if (!$payment_data) {
        echo json_encode([
            'success' => false,
            'error' => 'Payment not found'
        ]);
        return;
    }

    // Check payment status with gateway
    $gatewayManager = new Payment_Gateway_Manager();
    $status = $gatewayManager->getPaymentStatus($payment_data['gateway'], $order_id);

    echo json_encode([
        'success' => true,
        'order_id' => $order_id,
        'status' => $status,
        'payment_data' => $payment_data
    ]);
}

/**
 * Store widget payment data
 */
function storeWidgetPayment($widget_id, $order_id, $data) {
    $payments_dir = __DIR__ . "/payments";
    if (!is_dir($payments_dir)) {
        mkdir($payments_dir, 0755, true);
    }

    $payment_file = $payments_dir . "/{$order_id}.json";
    file_put_contents($payment_file, json_encode($data, JSON_PRETTY_PRINT));
}

/**
 * Load widget payment data
 */
function loadWidgetPayment($order_id) {
    $payment_file = __DIR__ . "/payments/{$order_id}.json";

    if (file_exists($payment_file)) {
        return json_decode(file_get_contents($payment_file), true);
    }

    return null;
}

/**
 * Send webhook notification
 */
function sendWebhookNotification($webhook_url, $data) {
    $context = stream_context_create([
        'http' => [
            'method' => 'POST',
            'header' => [
                'Content-Type: application/json',
                'User-Agent: ELOH-Widget/1.0'
            ],
            'content' => json_encode($data),
            'timeout' => 10
        ]
    ]);

    try {
        $result = file_get_contents($webhook_url, false, $context);
        error_log("Webhook sent successfully to: " . $webhook_url);
    } catch (Exception $e) {
        error_log("Webhook failed: " . $e->getMessage());
    }
}

/**
 * Sanitize text field
 */
function sanitize_text_field($value) {
    return htmlspecialchars(strip_tags(trim($value)));
}

/**
 * Sanitize email
 */
function sanitize_email($email) {
    return filter_var(trim($email), FILTER_SANITIZE_EMAIL);
}
?>
