// ELOH Processing PWA Service Worker
const CACHE_NAME = 'eloh-processing-v1.0.0';
const STATIC_CACHE = 'eloh-static-v1.0.0';
const DYNAMIC_CACHE = 'eloh-dynamic-v1.0.0';

// Files to cache immediately
const STATIC_FILES = [
  '/',
  '/index.php',
  '/about.php',
  '/services.php',
  '/operations.php',
  '/investors.php',
  '/contact.php',
  '/multi-gateway-payment-form.php',
  '/assets/css/futuristic-components.css',
  '/assets/js/pwa-app.js',
  '/manifest.json',
  // Add icon files when created
  '/assets/icons/icon-192x192.png',
  '/assets/icons/icon-512x512.png'
];

// Network-first resources (always try network first)
const NETWORK_FIRST = [
  '/multi-gateway-process-payment.php',
  '/btcpay-process-payment.php',
  '/square-process-payment.php',
  '/api/',
  '/webhook'
];

// Cache-first resources (try cache first, fallback to network)
const CACHE_FIRST = [
  '/assets/',
  'https://fonts.googleapis.com/',
  'https://fonts.gstatic.com/'
];

// Install event - cache static resources
self.addEventListener('install', event => {
  console.log('Service Worker: Installing...');
  
  event.waitUntil(
    caches.open(STATIC_CACHE)
      .then(cache => {
        console.log('Service Worker: Caching static files');
        return cache.addAll(STATIC_FILES);
      })
      .then(() => {
        console.log('Service Worker: Static files cached successfully');
        return self.skipWaiting();
      })
      .catch(error => {
        console.error('Service Worker: Error caching static files', error);
      })
  );
});

// Activate event - clean up old caches
self.addEventListener('activate', event => {
  console.log('Service Worker: Activating...');
  
  event.waitUntil(
    caches.keys()
      .then(cacheNames => {
        return Promise.all(
          cacheNames.map(cacheName => {
            if (cacheName !== STATIC_CACHE && cacheName !== DYNAMIC_CACHE) {
              console.log('Service Worker: Deleting old cache', cacheName);
              return caches.delete(cacheName);
            }
          })
        );
      })
      .then(() => {
        console.log('Service Worker: Activated successfully');
        return self.clients.claim();
      })
  );
});

// Fetch event - handle network requests
self.addEventListener('fetch', event => {
  const { request } = event;
  const url = new URL(request.url);
  
  // Skip non-GET requests
  if (request.method !== 'GET') {
    return;
  }
  
  // Skip chrome-extension and other non-http requests
  if (!url.protocol.startsWith('http')) {
    return;
  }
  
  event.respondWith(handleFetch(request));
});

async function handleFetch(request) {
  const url = new URL(request.url);
  
  try {
    // Network-first strategy for dynamic content
    if (NETWORK_FIRST.some(pattern => url.pathname.includes(pattern))) {
      return await networkFirst(request);
    }
    
    // Cache-first strategy for static assets
    if (CACHE_FIRST.some(pattern => url.href.includes(pattern))) {
      return await cacheFirst(request);
    }
    
    // Stale-while-revalidate for pages
    return await staleWhileRevalidate(request);
    
  } catch (error) {
    console.error('Service Worker: Fetch error', error);
    return await handleOffline(request);
  }
}

// Network-first strategy
async function networkFirst(request) {
  try {
    const networkResponse = await fetch(request);
    
    if (networkResponse.ok) {
      const cache = await caches.open(DYNAMIC_CACHE);
      cache.put(request, networkResponse.clone());
    }
    
    return networkResponse;
  } catch (error) {
    const cachedResponse = await caches.match(request);
    return cachedResponse || await handleOffline(request);
  }
}

// Cache-first strategy
async function cacheFirst(request) {
  const cachedResponse = await caches.match(request);
  
  if (cachedResponse) {
    return cachedResponse;
  }
  
  try {
    const networkResponse = await fetch(request);
    
    if (networkResponse.ok) {
      const cache = await caches.open(DYNAMIC_CACHE);
      cache.put(request, networkResponse.clone());
    }
    
    return networkResponse;
  } catch (error) {
    return await handleOffline(request);
  }
}

// Stale-while-revalidate strategy
async function staleWhileRevalidate(request) {
  const cachedResponse = await caches.match(request);
  
  const networkResponsePromise = fetch(request)
    .then(networkResponse => {
      if (networkResponse.ok) {
        const cache = caches.open(DYNAMIC_CACHE);
        cache.then(c => c.put(request, networkResponse.clone()));
      }
      return networkResponse;
    })
    .catch(() => null);
  
  return cachedResponse || await networkResponsePromise || await handleOffline(request);
}

// Handle offline scenarios
async function handleOffline(request) {
  const url = new URL(request.url);
  
  // Return offline page for navigation requests
  if (request.mode === 'navigate') {
    const offlineResponse = await caches.match('/offline.html');
    if (offlineResponse) {
      return offlineResponse;
    }
    
    // Fallback offline page
    return new Response(`
      <!DOCTYPE html>
      <html>
      <head>
        <title>ELOH Processing - Offline</title>
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <style>
          body { font-family: Arial, sans-serif; text-align: center; padding: 50px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; }
          .offline-container { max-width: 400px; margin: 0 auto; }
          .offline-icon { font-size: 4rem; margin-bottom: 1rem; }
          h1 { margin-bottom: 1rem; }
          p { margin-bottom: 2rem; }
          .retry-btn { background: white; color: #667eea; padding: 12px 24px; border: none; border-radius: 8px; cursor: pointer; font-weight: bold; }
        </style>
      </head>
      <body>
        <div class="offline-container">
          <div class="offline-icon">⚡</div>
          <h1>You're Offline</h1>
          <p>ELOH Processing is not available right now. Please check your internet connection and try again.</p>
          <button class="retry-btn" onclick="window.location.reload()">Try Again</button>
        </div>
      </body>
      </html>
    `, {
      headers: { 'Content-Type': 'text/html' }
    });
  }
  
  // Return generic offline response for other requests
  return new Response('Offline', { status: 503, statusText: 'Service Unavailable' });
}

// Background sync for payment processing
self.addEventListener('sync', event => {
  console.log('Service Worker: Background sync triggered', event.tag);
  
  if (event.tag === 'payment-sync') {
    event.waitUntil(syncPendingPayments());
  }
  
  if (event.tag === 'contact-sync') {
    event.waitUntil(syncPendingContacts());
  }
});

// Sync pending payments when back online
async function syncPendingPayments() {
  try {
    const pendingPayments = await getPendingPayments();
    
    for (const payment of pendingPayments) {
      try {
        const response = await fetch('/api/payments/sync', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(payment)
        });
        
        if (response.ok) {
          await removePendingPayment(payment.id);
          console.log('Service Worker: Payment synced successfully', payment.id);
        }
      } catch (error) {
        console.error('Service Worker: Failed to sync payment', payment.id, error);
      }
    }
  } catch (error) {
    console.error('Service Worker: Error syncing payments', error);
  }
}

// Sync pending contact forms
async function syncPendingContacts() {
  try {
    const pendingContacts = await getPendingContacts();
    
    for (const contact of pendingContacts) {
      try {
        const response = await fetch('/api/contacts/sync', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(contact)
        });
        
        if (response.ok) {
          await removePendingContact(contact.id);
          console.log('Service Worker: Contact synced successfully', contact.id);
        }
      } catch (error) {
        console.error('Service Worker: Failed to sync contact', contact.id, error);
      }
    }
  } catch (error) {
    console.error('Service Worker: Error syncing contacts', error);
  }
}

// IndexedDB helpers for offline storage
async function getPendingPayments() {
  // Implementation for retrieving pending payments from IndexedDB
  return [];
}

async function removePendingPayment(id) {
  // Implementation for removing synced payment from IndexedDB
}

async function getPendingContacts() {
  // Implementation for retrieving pending contacts from IndexedDB
  return [];
}

async function removePendingContact(id) {
  // Implementation for removing synced contact from IndexedDB
}

// Push notification handling
self.addEventListener('push', event => {
  console.log('Service Worker: Push notification received');
  
  const options = {
    body: 'You have a new notification from ELOH Processing',
    icon: '/assets/icons/icon-192x192.png',
    badge: '/assets/icons/badge-72x72.png',
    vibrate: [100, 50, 100],
    data: {
      dateOfArrival: Date.now(),
      primaryKey: 1
    },
    actions: [
      {
        action: 'explore',
        title: 'View Details',
        icon: '/assets/icons/checkmark.png'
      },
      {
        action: 'close',
        title: 'Close',
        icon: '/assets/icons/xmark.png'
      }
    ]
  };
  
  if (event.data) {
    const data = event.data.json();
    options.body = data.body || options.body;
    options.title = data.title || 'ELOH Processing';
  }
  
  event.waitUntil(
    self.registration.showNotification('ELOH Processing', options)
  );
});

// Notification click handling
self.addEventListener('notificationclick', event => {
  console.log('Service Worker: Notification clicked');
  
  event.notification.close();
  
  if (event.action === 'explore') {
    event.waitUntil(
      clients.openWindow('/')
    );
  } else if (event.action === 'close') {
    // Just close the notification
  } else {
    // Default action - open the app
    event.waitUntil(
      clients.openWindow('/')
    );
  }
});

console.log('Service Worker: Loaded successfully');
