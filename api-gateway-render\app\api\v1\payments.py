"""
Payment API endpoints for ELOH Processing Payment Gateway
"""

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List, Dict, Any
import logging

from ...database.connection import get_db_session
from ...database.models import Payment, Tenant
from ...core.exceptions import PaymentGatewayException

router = APIRouter()
logger = logging.getLogger(__name__)


@router.post("/payments")
async def create_payment(
    payment_data: Dict[str, Any],
    db: Session = Depends(get_db_session)
):
    """Create a new payment"""
    try:
        # Basic payment creation logic
        payment = Payment(
            amount=payment_data.get("amount"),
            currency=payment_data.get("currency", "USD"),
            description=payment_data.get("description"),
            customer_email=payment_data.get("email"),
            status="pending"
        )
        
        db.add(payment)
        db.commit()
        db.refresh(payment)
        
        return {
            "payment_id": payment.payment_id,
            "status": payment.status,
            "amount": payment.amount,
            "currency": payment.currency,
            "created_at": payment.created_at.isoformat()
        }
        
    except Exception as e:
        logger.error(f"Payment creation failed: {e}")
        raise HTTPException(status_code=400, detail="Payment creation failed")


@router.get("/payments/{payment_id}")
async def get_payment(
    payment_id: str,
    db: Session = Depends(get_db_session)
):
    """Get payment details"""
    payment = db.query(Payment).filter(Payment.payment_id == payment_id).first()
    
    if not payment:
        raise HTTPException(status_code=404, detail="Payment not found")
    
    return {
        "payment_id": payment.payment_id,
        "status": payment.status,
        "amount": payment.amount,
        "currency": payment.currency,
        "description": payment.description,
        "customer_email": payment.customer_email,
        "created_at": payment.created_at.isoformat(),
        "updated_at": payment.updated_at.isoformat()
    }


@router.get("/payments")
async def list_payments(
    limit: int = 10,
    offset: int = 0,
    db: Session = Depends(get_db_session)
):
    """List payments"""
    payments = db.query(Payment).offset(offset).limit(limit).all()
    
    return {
        "payments": [
            {
                "payment_id": p.payment_id,
                "status": p.status,
                "amount": p.amount,
                "currency": p.currency,
                "created_at": p.created_at.isoformat()
            }
            for p in payments
        ],
        "total": db.query(Payment).count()
    }
