# ELOH Processing Payment Gateway API Configuration
# Copy this file to .env and configure your settings

# Application Settings
APP_NAME="ELOH Processing Payment Gateway"
APP_VERSION="1.0.0"
DEBUG=false
ENVIRONMENT=production

# API Settings
API_HOST=0.0.0.0
API_PORT=8000
API_PREFIX=/v1

# Security Settings
SECRET_KEY=your-super-secret-key-change-this-in-production
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Database Settings
# For SQLite (development):
DATABASE_URL=sqlite:///./eloh_gateway.db
# For PostgreSQL (production):
# DATABASE_URL=postgresql+asyncpg://user:password@localhost/eloh_gateway
# For MySQL (production):
# DATABASE_URL=mysql+aiomysql://user:password@localhost/eloh_gateway

REDIS_URL=redis://localhost:6379/0

# Logging Settings
LOG_LEVEL=INFO
LOG_FORMAT=json

# Stripe Configuration
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key
STRIPE_WEBHOOK_SECRET=whsec_your_stripe_webhook_secret

# Square Configuration (when implemented)
SQUARE_APPLICATION_ID=your_square_application_id
SQUARE_ACCESS_TOKEN=your_square_access_token
SQUARE_WEBHOOK_SIGNATURE_KEY=your_square_webhook_signature_key
SQUARE_ENVIRONMENT=sandbox

# BTCPay Server Configuration
BTCPAY_SERVER_URL=https://your-btcpay-server.com
BTCPAY_API_KEY=your_btcpay_api_key
BTCPAY_STORE_ID=your_btcpay_store_id
BTCPAY_WEBHOOK_SECRET=your_btcpay_webhook_secret

# NowPayments Configuration (when implemented)
NOWPAYMENTS_API_KEY=your_nowpayments_api_key
NOWPAYMENTS_IPN_SECRET=your_nowpayments_ipn_secret
NOWPAYMENTS_ENVIRONMENT=sandbox

# Routing Settings
DEFAULT_ROUTING_STRATEGY=rule_based
ENABLE_AI_ROUTING=false
AI_ROUTING_ENDPOINT=https://your-ai-service.com/route

# Rate Limiting
RATE_LIMIT_ENABLED=true
RATE_LIMIT_REQUESTS_PER_MINUTE=100

# Monitoring and Observability
ENABLE_METRICS=true
METRICS_ENDPOINT=/metrics
ENABLE_TRACING=false
JAEGER_ENDPOINT=http://localhost:14268/api/traces

# Webhook Settings
WEBHOOK_TIMEOUT=30
WEBHOOK_RETRY_ATTEMPTS=3
WEBHOOK_RETRY_DELAY=5

# Cache Settings
CACHE_TTL=300
CACHE_ENABLED=true
