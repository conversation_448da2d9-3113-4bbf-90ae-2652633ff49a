<?php
// Payment Form Diagnostic Tool
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Payment Form Diagnostic</h1>";

// Check if required files exist
$requiredFiles = [
    'multi-gateway-payment-form.php',
    'includes/payment-gateway-manager.php',
    'includes/btcpay-gateway.php',
    'includes/nowpayments-gateway.php',
    'includes/btcpay-config.php',
    'includes/nowpayments-config.php'
];

echo "<h2>File Existence Check:</h2>";
echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
foreach ($requiredFiles as $file) {
    $exists = file_exists($file);
    $status = $exists ? '✅ EXISTS' : '❌ MISSING';
    $color = $exists ? 'green' : 'red';
    
    echo "<tr>";
    echo "<td style='padding: 8px; font-family: monospace;'>$file</td>";
    echo "<td style='padding: 8px; color: $color; font-weight: bold;'>$status</td>";
    echo "</tr>";
}
echo "</table>";

// Check if payment gateway manager loads
echo "<h2>Payment Gateway Manager Test:</h2>";
try {
    if (file_exists('includes/payment-gateway-manager.php')) {
        require_once 'includes/payment-gateway-manager.php';
        $manager = new Payment_Gateway_Manager();
        $gateways = $manager->getAvailableGateways();
        
        echo "<p style='color: green;'>✅ Payment Gateway Manager loaded successfully</p>";
        echo "<p><strong>Available Gateways:</strong> " . count($gateways) . "</p>";
        
        foreach ($gateways as $id => $gateway) {
            echo "<p>• <strong>$id:</strong> {$gateway['name']} - {$gateway['description']}</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ Payment Gateway Manager file not found</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error loading Payment Gateway Manager: " . htmlspecialchars($e->getMessage()) . "</p>";
}

// Test URL parameters
echo "<h2>URL Parameter Test:</h2>";
$testUrls = [
    'multi-gateway-payment-form.php',
    'multi-gateway-payment-form.php?type=service',
    'multi-gateway-payment-form.php?type=service&service=consulting&amount=150',
    'multi-gateway-payment-form.php?type=donation&amount=100'
];

foreach ($testUrls as $url) {
    echo "<p><a href='$url' target='_blank'>$url</a></p>";
}

// Check server environment
echo "<h2>Server Environment:</h2>";
echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><td style='padding: 8px;'><strong>PHP Version</strong></td><td style='padding: 8px;'>" . phpversion() . "</td></tr>";
echo "<tr><td style='padding: 8px;'><strong>Server Software</strong></td><td style='padding: 8px;'>" . ($_SERVER['SERVER_SOFTWARE'] ?? 'Unknown') . "</td></tr>";
echo "<tr><td style='padding: 8px;'><strong>Document Root</strong></td><td style='padding: 8px;'>" . ($_SERVER['DOCUMENT_ROOT'] ?? 'Unknown') . "</td></tr>";
echo "<tr><td style='padding: 8px;'><strong>Current Directory</strong></td><td style='padding: 8px;'>" . getcwd() . "</td></tr>";
echo "<tr><td style='padding: 8px;'><strong>cURL Extension</strong></td><td style='padding: 8px;'>" . (extension_loaded('curl') ? '✅ Available' : '❌ Missing') . "</td></tr>";
echo "<tr><td style='padding: 8px;'><strong>JSON Extension</strong></td><td style='padding: 8px;'>" . (extension_loaded('json') ? '✅ Available' : '❌ Missing') . "</td></tr>";
echo "</table>";

// Test basic form loading
echo "<h2>Basic Form Loading Test:</h2>";
if (file_exists('multi-gateway-payment-form.php')) {
    echo "<p style='color: green;'>✅ Payment form file exists</p>";
    echo "<p><strong>Test Links:</strong></p>";
    echo "<ul>";
    echo "<li><a href='multi-gateway-payment-form.php' target='_blank'>Basic Form</a></li>";
    echo "<li><a href='multi-gateway-payment-form.php?type=service&service=consulting&amount=150' target='_blank'>Consulting Service</a></li>";
    echo "<li><a href='multi-gateway-payment-form.php?type=donation&amount=100' target='_blank'>$100 Donation</a></li>";
    echo "</ul>";
} else {
    echo "<p style='color: red;'>❌ Payment form file not found</p>";
}

// JavaScript test
echo "<h2>JavaScript Test:</h2>";
echo "<div id='js-test' style='padding: 10px; border: 1px solid #ccc; background: #f9f9f9;'>JavaScript not loaded</div>";

echo "<script>";
echo "document.getElementById('js-test').innerHTML = '✅ JavaScript is working';";
echo "document.getElementById('js-test').style.color = 'green';";
echo "console.log('Payment form diagnostic page loaded');";
echo "</script>";

echo "<hr>";
echo "<p><a href='simple-payment-test.php'>← Simple Payment Test</a> | <a href='test-payment-selection.php'>Advanced Test</a> | <a href='index.php'>Homepage</a></p>";
?>
