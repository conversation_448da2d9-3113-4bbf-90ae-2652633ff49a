<?php
/**
 * BTCPay Server Webhook Handler for ELOH Processing
 * Handles payment notifications from BTCPay Server
 */

require_once "includes/btcpay-gateway.php";

// Get webhook data
$input = file_get_contents('php://input');
$headers = getallheaders();

// Get BTCPay signature
$signature = null;
foreach ($headers as $key => $value) {
    if (strtolower($key) === 'btcpay-sig') {
        $signature = $value;
        break;
    }
}

if (!$signature) {
    http_response_code(400);
    echo "Missing signature";
    exit;
}

try {
    $gateway = new BTCPay_Gateway();
    
    // Validate webhook signature
    if (!$gateway->validateWebhook($input, $signature)) {
        http_response_code(401);
        echo "Invalid signature";
        exit;
    }
    
    // Parse webhook data
    $data = json_decode($input, true);
    if (!$data) {
        http_response_code(400);
        echo "Invalid JSON";
        exit;
    }
    
    // Log webhook for debugging
    error_log("BTCPay Webhook received: " . $input);
    
    $invoiceId = $data['invoiceId'] ?? '';
    $type = $data['type'] ?? '';
    
    if (!$invoiceId || !$type) {
        http_response_code(400);
        echo "Missing required fields";
        exit;
    }
    
    // Get invoice details from BTCPay
    $invoiceResult = $gateway->getInvoice($invoiceId);
    
    if (!$invoiceResult['success']) {
        http_response_code(404);
        echo "Invoice not found";
        exit;
    }
    
    $invoice = $invoiceResult['invoice'];
    $status = $invoice['status'] ?? '';
    $orderId = $invoice['metadata']['orderId'] ?? '';
    $buyerEmail = $invoice['metadata']['buyerEmail'] ?? '';
    
    // Process different webhook events
    switch ($type) {
        case 'InvoiceCreated':
            // Invoice was created
            processInvoiceCreated($invoiceId, $orderId, $invoice);
            break;
            
        case 'InvoiceReceivedPayment':
            // Payment received but not confirmed
            processPaymentReceived($invoiceId, $orderId, $invoice);
            break;
            
        case 'InvoicePaymentSettled':
        case 'InvoiceProcessing':
            // Payment confirmed
            processPaymentConfirmed($invoiceId, $orderId, $invoice);
            break;
            
        case 'InvoiceSettled':
            // Payment fully settled
            processPaymentSettled($invoiceId, $orderId, $invoice);
            break;
            
        case 'InvoiceExpired':
            // Invoice expired
            processInvoiceExpired($invoiceId, $orderId, $invoice);
            break;
            
        case 'InvoiceInvalid':
            // Invalid payment
            processInvoiceInvalid($invoiceId, $orderId, $invoice);
            break;
    }
    
    http_response_code(200);
    echo "Webhook processed successfully";
    
} catch (Exception $e) {
    error_log("BTCPay webhook error: " . $e->getMessage());
    http_response_code(500);
    echo "Internal server error";
}

function processInvoiceCreated($invoiceId, $orderId, $invoice) {
    // Log invoice creation
    error_log("Invoice created: $invoiceId for order: $orderId");
}

function processPaymentReceived($invoiceId, $orderId, $invoice) {
    // Payment received but not confirmed yet
    error_log("Payment received for invoice: $invoiceId, order: $orderId");
    
    // You could send an email notification here
    sendPaymentNotification($invoice, 'Payment Received', 'Your payment has been received and is being confirmed.');
}

function processPaymentConfirmed($invoiceId, $orderId, $invoice) {
    // Payment confirmed
    error_log("Payment confirmed for invoice: $invoiceId, order: $orderId");
    
    // Update your internal order status
    updateOrderStatus($orderId, 'confirmed');
    
    // Send confirmation email
    sendPaymentNotification($invoice, 'Payment Confirmed', 'Your payment has been confirmed!');
}

function processPaymentSettled($invoiceId, $orderId, $invoice) {
    // Payment fully settled
    error_log("Payment settled for invoice: $invoiceId, order: $orderId");
    
    // Update your internal order status
    updateOrderStatus($orderId, 'completed');
    
    // Send completion email
    sendPaymentNotification($invoice, 'Payment Complete', 'Your payment has been completed successfully.');
}

function processInvoiceExpired($invoiceId, $orderId, $invoice) {
    // Invoice expired
    error_log("Invoice expired: $invoiceId for order: $orderId");
    
    // Update your internal order status
    updateOrderStatus($orderId, 'expired');
}

function processInvoiceInvalid($invoiceId, $orderId, $invoice) {
    // Invalid payment
    error_log("Invalid payment for invoice: $invoiceId, order: $orderId");
    
    // Update your internal order status
    updateOrderStatus($orderId, 'invalid');
}

function updateOrderStatus($orderId, $status) {
    // Update order status in your system
    // This could be a database update, file write, etc.
    $logEntry = [
        'order_id' => $orderId,
        'status' => $status,
        'timestamp' => time(),
        'date' => date('Y-m-d H:i:s')
    ];
    
    // Log to file (in production, use a database)
    $logFile = __DIR__ . '/data/payment_logs.json';
    $logs = [];
    
    if (file_exists($logFile)) {
        $logs = json_decode(file_get_contents($logFile), true) ?: [];
    }
    
    $logs[] = $logEntry;
    
    // Keep only last 1000 entries
    if (count($logs) > 1000) {
        $logs = array_slice($logs, -1000);
    }
    
    file_put_contents($logFile, json_encode($logs, JSON_PRETTY_PRINT));
}

function sendPaymentNotification($invoice, $subject, $message) {
    $buyerEmail = $invoice['metadata']['buyerEmail'] ?? '';
    
    if (!$buyerEmail) {
        return;
    }
    
    // Simple email notification (in production, use a proper email service)
    $to = $buyerEmail;
    $headers = [
        'From: <EMAIL>',
        'Reply-To: <EMAIL>',
        'Content-Type: text/html; charset=UTF-8'
    ];
    
    $emailBody = "
    <h2>$subject</h2>
    <p>$message</p>
    <p><strong>Invoice ID:</strong> {$invoice['id']}</p>
    <p><strong>Amount:</strong> {$invoice['amount']} {$invoice['currency']}</p>
    <p><strong>Order ID:</strong> {$invoice['metadata']['orderId']}</p>
    <p>Thank you for your business!</p>
    <p>ELOH Processing LLC</p>
    ";
    
    // In production, use a proper email service like SendGrid, Mailgun, etc.
    // mail($to, $subject, $emailBody, implode("\r\n", $headers));
    
    // For now, just log the email
    error_log("Email notification: $subject to $buyerEmail");
}
?>
