# TinyHost Deployment Checklist
## ELOH Processing LLC - Step-by-Step Deployment

### ✅ **PRE-DEPLOYMENT CHECKLIST**

#### **1. Domain Configuration**
- [ ] Edit `update-domain.php` with your TinyHost domain
- [ ] Run `update-domain.php` to update all domain references
- [ ] Verify all files show correct domain

#### **2. API Credentials**
- [ ] **BTCPay Server**: Credentials already configured ✅
- [ ] **NowPayments**: Sign up at https://account.nowpayments.io
- [ ] **NowPayments**: Generate API key and IPN secret
- [ ] **NowPayments**: Edit `includes/nowpayments-config.php`

#### **3. File Preparation**
- [ ] All files copied to deployment folder ✅
- [ ] Unnecessary files removed ✅
- [ ] Documentation included ✅
- [ ] Domain update script ready ✅

### 📤 **DEPLOYMENT STEPS**

#### **Step 1: Upload to TinyHost**
- [ ] Compress deployment folder to ZIP
- [ ] Login to TinyHost control panel
- [ ] Upload ZIP to domain root directory
- [ ] Extract files in correct location
- [ ] Set file permissions (755 for folders, 644 for files)

#### **Step 2: Configure Domain**
- [ ] Edit `update-domain.php` with your TinyHost domain
- [ ] Run the script via browser: `your-domain.com/update-domain.php`
- [ ] Verify all domain references updated
- [ ] Delete `update-domain.php` after use (security)

#### **Step 3: Test Basic Functionality**
- [ ] Visit homepage: `your-domain.com/index.php`
- [ ] Check all navigation links work
- [ ] Verify pages load without errors
- [ ] Test mobile responsiveness

#### **Step 4: Configure Payment Gateways**

**BTCPay Server:**
- [ ] Login to BTCPay Server dashboard
- [ ] Update webhook URL to: `your-domain.com/btcpay-webhook.php`
- [ ] Test webhook delivery
- [ ] Run `btcpay-test.php` to verify connection

**NowPayments:**
- [ ] Add API credentials to `includes/nowpayments-config.php`
- [ ] Add wallet addresses for cryptocurrencies you want
- [ ] Run `nowpayments-test.php` to verify connection
- [ ] Test payment creation

#### **Step 5: Test Payment System**
- [ ] Visit `multi-gateway-payment-form.php`
- [ ] Test BTCPay Server payment flow
- [ ] Test NowPayments payment flow
- [ ] Verify payment status updates
- [ ] Check webhook notifications

### 🧪 **TESTING CHECKLIST**

#### **Website Functionality**
- [ ] Homepage loads correctly
- [ ] All navigation links work
- [ ] About page displays properly
- [ ] Services page shows offerings
- [ ] Investors page has donation options
- [ ] Contact page is accessible
- [ ] Mobile version works well

#### **Payment System**
- [ ] Multi-gateway form displays
- [ ] Gateway selection works
- [ ] Currency options populate correctly
- [ ] Form validation works
- [ ] BTCPay payments process
- [ ] NowPayments payments process
- [ ] Success pages display
- [ ] Error handling works

#### **API Integrations**
- [ ] BTCPay Server API connection
- [ ] BTCPay webhook receives notifications
- [ ] NowPayments API connection
- [ ] NowPayments payment creation
- [ ] Payment status checking
- [ ] Real-time updates work

### 🔧 **TROUBLESHOOTING**

#### **Common Issues:**

**File Permissions:**
- Folders: 755
- PHP files: 644
- If issues persist, try 777 temporarily

**Domain References:**
- Check all config files have correct domain
- Verify webhook URLs are updated
- Test API endpoints respond

**API Connections:**
- Verify API keys are correct
- Check firewall/hosting restrictions
- Test with diagnostic pages

**Payment Processing:**
- Ensure HTTPS is enabled
- Check minimum amount requirements
- Verify webhook signatures

### 📞 **SUPPORT RESOURCES**

#### **TinyHost Support:**
- Check TinyHost documentation for PHP requirements
- Verify cURL and JSON extensions enabled
- Confirm HTTPS/SSL certificate active

#### **Payment Gateway Support:**
- **BTCPay Server**: https://docs.btcpayserver.org/
- **NowPayments**: Support available in dashboard

#### **Diagnostic Tools:**
- `btcpay-test.php` - BTCPay Server diagnostics
- `nowpayments-test.php` - NowPayments diagnostics
- `btcpay-troubleshoot.php` - Comprehensive BTCPay testing

### 🎯 **POST-DEPLOYMENT**

#### **Security:**
- [ ] Delete `update-domain.php` after use
- [ ] Verify HTTPS is working
- [ ] Check file permissions are secure
- [ ] Monitor error logs

#### **Monitoring:**
- [ ] Test payments regularly
- [ ] Monitor webhook deliveries
- [ ] Check API rate limits
- [ ] Verify payment confirmations

#### **Maintenance:**
- [ ] Keep API credentials secure
- [ ] Monitor payment gateway status
- [ ] Update webhook URLs if domain changes
- [ ] Regular backup of configuration

### 🎉 **SUCCESS CRITERIA**

Your deployment is successful when:
- ✅ Website loads on TinyHost domain
- ✅ All pages navigate correctly
- ✅ BTCPay Server payments work
- ✅ NowPayments integration functions
- ✅ Webhooks receive notifications
- ✅ Mobile version is responsive
- ✅ Error handling works properly

### 📋 **FINAL VERIFICATION**

1. **Create test payment** with BTCPay Server
2. **Create test payment** with NowPayments
3. **Verify webhook notifications** arrive
4. **Check payment confirmations** work
5. **Test on mobile device**
6. **Verify all links and forms** function

**Your ELOH Processing website is ready for professional cryptocurrency payments on TinyHost! 🚀**
