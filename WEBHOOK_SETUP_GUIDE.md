# BTCPay Server Webhook Setup Guide
## ELOH Processing LLC

### 🎯 Current Configuration Status

Based on your configuration file, you have:
- **Host**: `https://mainnet.demo.btcpayserver.org` ✅
- **Store ID**: `DzeKFNoYV91vVvp2wf5PXUp33P4EvurrfVGvbPWd8RQS` ✅
- **Webhook URL**: `https://elohprocessing.infy.uk/btcpay-webhook.php` ✅
- **Webhook Secret**: `6Rriupy3P7X5$j6` ✅

### 🔧 Step-by-Step Webhook Setup

#### **Step 1: Access BTCPay Server**
1. Go to: https://mainnet.demo.btcpayserver.org
2. Log in to your account
3. Navigate to your store

#### **Step 2: Create/Update API Key**
1. Click **Account** (top right)
2. Go to **API Keys**
3. Click **Generate Key**
4. **Select Permissions**:
   - ✅ `btcpay.store.canviewinvoices`
   - ✅ `btcpay.store.cancreateinvoice`
   - ✅ `btcpay.store.canmodifyinvoices`
   - ✅ `btcpay.store.webhooks.canmodifywebhooks`
   - ✅ `btcpay.store.canmodifystoresettings`
   - ✅ `btcpay.store.canviewstoresettings`
   - ✅ `btcpay.store.canviewreports`
   - ✅ `btcpay.store.canviewpaymentrequests`
   - ✅ `btcpay.store.canmodifypaymentrequests`
   - ✅ `btcpay.user.canmodifyprofile`
   - ✅ `btcpay.user.canviewprofile`
   - ✅ `btcpay.user.canmanagenotificationsforuser`
   - ✅ `btcpay.user.canviewnotificationsforuser`
   - ✅ `unrestricted`
   - ✅ `btcpay.store.canviewlightninginvoice`
   - ✅ `btcpay.store.cancreatelightninginvoice`
   - ✅ `btcpay.store.canmanagepullpayments`
   - ✅ `btcpay.store.canarchivepullpayments`
   - ✅ `btcpay.store.cancreatepullpayments`
   - ✅ `btcpay.store.canviewpullpayments`
   - ✅ `btcpay.store.cancreatenonapprovedpullpayments`
   - ✅ `btcpay.store.canmanagepayouts`
   - ✅ `btcpay.store.canviewpayouts`

5. Click **Generate**
6. **Copy the API key** and update `includes/btcpay-config.php`

#### **Step 3: Setup Webhook**
1. In BTCPay Server, go to your **Store**
2. Click **Settings** → **Webhooks**
3. Click **Create Webhook**
4. Fill in the form:

   **Webhook URL**: `https://elohprocessing.infy.uk/btcpay-webhook.php`
   
   **Secret**: `6Rriupy3P7X5$j6`
   
   **Events to Subscribe**:
   - ✅ InvoiceCreated
   - ✅ InvoiceReceivedPayment
   - ✅ InvoicePaymentSettled
   - ✅ InvoiceProcessing
   - ✅ InvoiceExpired
   - ✅ InvoiceSettled
   - ✅ InvoiceInvalid

5. Click **Add webhook**

#### **Step 4: Test Configuration**
1. Upload all files to your InfinityFree hosting
2. Visit: `https://elohprocessing.infy.uk/btcpay-troubleshoot.php`
3. Check configuration status
4. Try creating a test invoice

### 🚨 Troubleshooting Common Issues

#### **Issue: "Failed to create invoice - false"**

**Possible Causes:**
1. **Missing HTTPS**: Ensure host URL is `https://mainnet.demo.btcpayserver.org`
2. **Invalid API Key**: API key might be wrong or expired
3. **Wrong Store ID**: Store ID doesn't match your BTCPay store
4. **Insufficient Permissions**: API key lacks required permissions

**Solutions:**
1. **Check Configuration**: Verify all values in `btcpay-config.php`
2. **Regenerate API Key**: Create new API key with all permissions
3. **Verify Store ID**: Copy Store ID from BTCPay Server → Store → Settings → General
4. **Test Connection**: Use the troubleshooting page to test

#### **Issue: "HTTP Error 401 Unauthorized"**
- **Cause**: Invalid API key
- **Solution**: Generate new API key in BTCPay Server

#### **Issue: "HTTP Error 403 Forbidden"**
- **Cause**: API key lacks permissions
- **Solution**: Add missing permissions to API key

#### **Issue: "Connection timeout"**
- **Cause**: Network/firewall issues
- **Solution**: Contact InfinityFree support about BTCPay Server access

### 🔍 Debug Information

To get detailed error information:
1. Check your hosting error logs
2. Visit `btcpay-troubleshoot.php` for configuration check
3. Look for BTCPay API request/response logs

### 📝 Configuration File Template

Your `includes/btcpay-config.php` should look like:

```php
<?php
return [
    'host' => 'https://mainnet.demo.btcpayserver.org',
    'api_key' => 'YOUR_ACTUAL_API_KEY_HERE',
    'store_id' => 'DzeKFNoYV91vVvp2wf5PXUp33P4EvurrfVGvbPWd8RQS',
    'webhook_secret' => '6Rriupy3P7X5$j6',
    'webhook_url' => 'https://elohprocessing.infy.uk/btcpay-webhook.php',
    // ... rest of config
];
```

### 🎯 Next Steps

1. **Update API Key**: Replace `your_api_key_here` with actual key
2. **Test Setup**: Use troubleshooting page to verify
3. **Create Test Invoice**: Try $0.01 test payment
4. **Monitor Logs**: Check for any errors
5. **Go Live**: Update your website links to use BTCPay forms

### 📞 Support

If you continue having issues:
1. Check BTCPay Server documentation: https://docs.btcpayserver.org
2. Visit BTCPay Server community: https://chat.btcpayserver.org
3. Use the troubleshooting page for detailed diagnostics

Your BTCPay Server integration is almost ready! Just update the API key and test. 🚀
