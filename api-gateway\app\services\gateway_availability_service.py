"""
Gateway Availability Service for ELOH Processing

This service determines which payment gateways are available based on:
- Geographic region
- Regulatory compliance
- Gateway configuration status
- Business type restrictions
"""

from typing import Dict, List, Optional, Set
from enum import Enum
import logging
from dataclasses import dataclass

from ..core.adapter_registry import get_adapter_registry
from ..core.config import get_settings
from ..models.tenant import TenantResponse

logger = logging.getLogger(__name__)


class AvailabilityStatus(str, Enum):
    """Gateway availability status"""
    AVAILABLE = "available"
    LIMITED = "limited"
    UNAVAILABLE = "unavailable"
    NOT_CONFIGURED = "not_configured"
    REGION_BLOCKED = "region_blocked"
    BUSINESS_RESTRICTED = "business_restricted"


@dataclass
class GatewayAvailability:
    """Gateway availability information"""
    gateway_id: str
    name: str
    status: AvailabilityStatus
    message: str
    supported_currencies: List[str]
    supported_methods: List[str]
    regions: List[str]
    restrictions: List[str]
    configuration_required: bool
    is_configured: bool


class GatewayAvailabilityService:
    """
    Service to determine gateway availability based on various factors.
    
    This service helps tenants understand which gateways they can use
    based on their location, business type, and current configuration.
    """
    
    def __init__(self):
        self.registry = get_adapter_registry()
        self.settings = get_settings()
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        
        # Regional availability mapping
        self.regional_availability = {
            "stripe": {
                "available_countries": [
                    "US", "CA", "GB", "IE", "AU", "NZ", "SG", "HK", "JP", "AT", "BE", "BG", 
                    "HR", "CY", "CZ", "DK", "EE", "FI", "FR", "DE", "GR", "HU", "IT", "LV", 
                    "LT", "LU", "MT", "NL", "NO", "PL", "PT", "RO", "SK", "SI", "ES", "SE", 
                    "CH", "BR", "MX", "MY", "TH", "IN", "PH", "ID"
                ],
                "restricted_countries": ["DM"],  # Dominica not supported
                "business_restrictions": ["adult_content", "gambling", "cannabis", "mlm"]
            },
            "square": {
                "available_countries": ["US", "CA", "AU", "GB", "IE", "ES", "FR", "JP"],
                "restricted_countries": ["DM"],  # Dominica not supported
                "business_restrictions": ["adult_entertainment", "gambling", "cannabis", "cryptocurrency"]
            },
            "btcpay": {
                "available_countries": ["*"],  # Global availability
                "restricted_countries": [],
                "business_restrictions": []  # No business restrictions
            },
            "nowpayments": {
                "available_countries": ["*"],  # Global availability
                "restricted_countries": [],
                "business_restrictions": []  # No business restrictions
            }
        }
        
        # Current region (can be configured or detected)
        self.current_region = "DM"  # Dominica ISO code
    
    def get_available_gateways(self, tenant: Optional[TenantResponse] = None) -> List[GatewayAvailability]:
        """
        Get list of available gateways for a tenant or region.
        
        Args:
            tenant: Optional tenant information for business-specific checks
            
        Returns:
            List[GatewayAvailability]: List of gateway availability information
        """
        available_gateways = []
        
        for gateway_id in self.registry.list_gateways():
            availability = self._check_gateway_availability(gateway_id, tenant)
            available_gateways.append(availability)
        
        # Sort by availability status (available first)
        available_gateways.sort(key=lambda x: (
            x.status != AvailabilityStatus.AVAILABLE,
            x.status != AvailabilityStatus.LIMITED,
            x.gateway_id
        ))
        
        return available_gateways
    
    def get_available_gateway_ids(self, tenant: Optional[TenantResponse] = None) -> List[str]:
        """
        Get list of available gateway IDs only.
        
        Args:
            tenant: Optional tenant information
            
        Returns:
            List[str]: List of available gateway IDs
        """
        available_gateways = self.get_available_gateways(tenant)
        return [
            gw.gateway_id for gw in available_gateways 
            if gw.status in [AvailabilityStatus.AVAILABLE, AvailabilityStatus.LIMITED]
        ]
    
    def is_gateway_available(self, gateway_id: str, tenant: Optional[TenantResponse] = None) -> bool:
        """
        Check if a specific gateway is available.
        
        Args:
            gateway_id: Gateway identifier
            tenant: Optional tenant information
            
        Returns:
            bool: True if gateway is available
        """
        availability = self._check_gateway_availability(gateway_id, tenant)
        return availability.status in [AvailabilityStatus.AVAILABLE, AvailabilityStatus.LIMITED]
    
    def get_gateway_availability(self, gateway_id: str, tenant: Optional[TenantResponse] = None) -> GatewayAvailability:
        """
        Get detailed availability information for a specific gateway.
        
        Args:
            gateway_id: Gateway identifier
            tenant: Optional tenant information
            
        Returns:
            GatewayAvailability: Detailed availability information
        """
        return self._check_gateway_availability(gateway_id, tenant)
    
    def get_recommended_gateways(self, 
                                currency: str, 
                                business_type: Optional[str] = None,
                                tenant: Optional[TenantResponse] = None) -> List[str]:
        """
        Get recommended gateways for a specific currency and business type.
        
        Args:
            currency: Currency code (e.g., 'USD', 'BTC')
            business_type: Type of business
            tenant: Optional tenant information
            
        Returns:
            List[str]: List of recommended gateway IDs
        """
        available_gateways = self.get_available_gateways(tenant)
        
        # Filter by currency support
        currency_compatible = [
            gw for gw in available_gateways
            if currency in gw.supported_currencies and 
            gw.status in [AvailabilityStatus.AVAILABLE, AvailabilityStatus.LIMITED]
        ]
        
        # Sort by preference for the currency
        currency_preferences = {
            "USD": ["btcpay", "nowpayments"],  # Prefer crypto gateways for USD in restricted regions
            "BTC": ["btcpay", "nowpayments"],
            "ETH": ["nowpayments", "btcpay"],
            "EUR": ["btcpay", "nowpayments"]
        }
        
        preferred_order = currency_preferences.get(currency, [])
        
        # Sort by preference
        def sort_key(gw):
            try:
                return preferred_order.index(gw.gateway_id)
            except ValueError:
                return len(preferred_order)
        
        currency_compatible.sort(key=sort_key)
        
        return [gw.gateway_id for gw in currency_compatible]
    
    def _check_gateway_availability(self, gateway_id: str, tenant: Optional[TenantResponse] = None) -> GatewayAvailability:
        """
        Check availability for a specific gateway.
        
        Args:
            gateway_id: Gateway identifier
            tenant: Optional tenant information
            
        Returns:
            GatewayAvailability: Availability information
        """
        if not self.registry.is_gateway_registered(gateway_id):
            return GatewayAvailability(
                gateway_id=gateway_id,
                name=gateway_id.title(),
                status=AvailabilityStatus.UNAVAILABLE,
                message="Gateway not registered",
                supported_currencies=[],
                supported_methods=[],
                regions=[],
                restrictions=["not_registered"],
                configuration_required=False,
                is_configured=False
            )
        
        gateway_info = self.registry.get_gateway_info(gateway_id)
        is_configured = self.settings.is_gateway_configured(gateway_id)
        regional_info = self.regional_availability.get(gateway_id, {})
        
        # Check regional availability
        available_countries = regional_info.get("available_countries", [])
        restricted_countries = regional_info.get("restricted_countries", [])
        
        region_available = (
            "*" in available_countries or 
            self.current_region in available_countries
        ) and self.current_region not in restricted_countries
        
        # Check business restrictions
        business_restrictions = regional_info.get("business_restrictions", [])
        business_compatible = True
        if tenant and tenant.business_type:
            business_type_lower = tenant.business_type.lower()
            business_compatible = not any(
                restriction in business_type_lower 
                for restriction in business_restrictions
            )
        
        # Determine status and message
        restrictions = []
        
        if not region_available:
            status = AvailabilityStatus.REGION_BLOCKED
            message = f"Not available in {self.current_region}"
            restrictions.append("regional_restriction")
        elif not business_compatible:
            status = AvailabilityStatus.BUSINESS_RESTRICTED
            message = "Business type not supported"
            restrictions.append("business_restriction")
        elif not is_configured:
            status = AvailabilityStatus.NOT_CONFIGURED
            message = "Gateway not configured"
            restrictions.append("not_configured")
        elif gateway_id in ["stripe", "square"] and self.current_region == "DM":
            # Special case for Stripe/Square in Dominica
            status = AvailabilityStatus.LIMITED
            message = "Limited availability - consider crypto alternatives"
            restrictions.append("regional_limitation")
        else:
            status = AvailabilityStatus.AVAILABLE
            message = "Available for use"
        
        return GatewayAvailability(
            gateway_id=gateway_id,
            name=gateway_info["name"],
            status=status,
            message=message,
            supported_currencies=gateway_info["supported_currencies"],
            supported_methods=gateway_info["supported_methods"],
            regions=gateway_info.get("regions", ["global"]),
            restrictions=restrictions,
            configuration_required=not is_configured,
            is_configured=is_configured
        )
    
    def get_region_summary(self) -> Dict:
        """
        Get summary of gateway availability for current region.
        
        Returns:
            Dict: Region availability summary
        """
        available_gateways = self.get_available_gateways()
        
        summary = {
            "region": self.current_region,
            "region_name": "Dominica",
            "total_gateways": len(available_gateways),
            "available_gateways": len([gw for gw in available_gateways if gw.status == AvailabilityStatus.AVAILABLE]),
            "limited_gateways": len([gw for gw in available_gateways if gw.status == AvailabilityStatus.LIMITED]),
            "unavailable_gateways": len([gw for gw in available_gateways if gw.status in [AvailabilityStatus.UNAVAILABLE, AvailabilityStatus.REGION_BLOCKED]]),
            "recommended_gateways": ["btcpay", "nowpayments"],
            "supported_currencies": list(set([
                currency for gw in available_gateways 
                if gw.status in [AvailabilityStatus.AVAILABLE, AvailabilityStatus.LIMITED]
                for currency in gw.supported_currencies
            ])),
            "notes": [
                "Stripe and Square have limited availability in Dominica",
                "BTCPay Server and NowPayments are recommended for global coverage",
                "Cryptocurrency gateways provide the best regional coverage"
            ]
        }
        
        return summary


# Global service instance
_availability_service = GatewayAvailabilityService()


def get_gateway_availability_service() -> GatewayAvailabilityService:
    """Get the global gateway availability service instance"""
    return _availability_service
