# 🔧 ELOH Processing Payment Gateway - Troubleshooting Guide

## 🚨 Common Issues & Solutions

### 1. "Could not open requirements file" Error

**Problem**: `ERROR: Could not open requirements file: [Errno 2] No such file or directory: 'requirements.txt'`

**Solution**:
```bash
# Check current directory
pwd

# If you're not in api-gateway directory:
cd api-gateway

# Verify files exist
ls -la

# Should see: main.py, requirements.txt, setup_database.py, etc.
```

**Windows PowerShell**:
```powershell
# Check current directory
Get-Location

# Navigate to api-gateway
cd api-gateway

# List files
dir
```

### 2. Import Errors

**Problem**: `ModuleNotFoundError: No module named 'app'`

**Solutions**:

**A. Install Dependencies**:
```bash
pip install -r requirements.txt
```

**B. Check Python Path**:
```bash
# Run from api-gateway directory
python -c "import sys; print(sys.path)"
```

**C. Use Debug Script**:
```bash
python debug.py
```

### 3. Database Connection Issues

**Problem**: Database connection fails or tables don't exist

**Solutions**:

**A. Initialize Database**:
```bash
python setup_database.py --sample-data
```

**B. Reset Database**:
```bash
python setup_database.py --reset --sample-data
```

**C. Check Database File**:
```bash
# Should see eloh_gateway.db file
ls -la *.db
```

### 4. Port Already in Use

**Problem**: `OSError: [Errno 48] Address already in use`

**Solutions**:

**A. Use Different Port**:
```bash
uvicorn main:app --port 8001
```

**B. Kill Process on Port 8000**:
```bash
# Linux/Mac
lsof -ti:8000 | xargs kill -9

# Windows
netstat -ano | findstr :8000
taskkill /PID <PID> /F
```

### 5. Environment Variables Not Loading

**Problem**: Configuration not working, gateway credentials missing

**Solutions**:

**A. Create .env File**:
```bash
cp .env.example .env
# Edit .env with your values
```

**B. Check .env Loading**:
```python
python -c "
from app.core.config import get_settings
settings = get_settings()
print(f'App: {settings.app_name}')
print(f'Environment: {settings.environment}')
"
```

### 6. Gateway Not Available

**Problem**: Gateways show as "not configured" or "unavailable"

**Solutions**:

**A. Check Environment Variables**:
```bash
# For BTCPay Server
echo $BTCPAY_SERVER_URL
echo $BTCPAY_API_KEY

# For NowPayments  
echo $NOWPAYMENTS_API_KEY
```

**B. Test Gateway Availability**:
```bash
curl http://localhost:8000/v1/portal/status
```

**C. Regional Check**:
- Stripe: Limited in Dominica
- Square: Not available in Dominica
- BTCPay: Available globally
- NowPayments: Available globally

### 7. Render Deployment Issues

**Problem**: Deployment fails on Render

**Solutions**:

**A. Check Build Logs**:
- Go to Render dashboard
- Check build logs for errors
- Look for Python/dependency issues

**B. Environment Variables**:
```env
# Required on Render
APP_NAME=ELOH Processing Payment Gateway
ENVIRONMENT=production
DEBUG=false

# Database (auto-configured)
DATABASE_URL=postgresql://...

# Gateway credentials
BTCPAY_SERVER_URL=https://your-server.com
BTCPAY_API_KEY=your_key
```

**C. Build Command Issues**:
```bash
# Make sure build.sh is executable
chmod +x build.sh

# Test build locally
./build.sh
```

## 🛠️ Debug Commands

### Quick Health Check
```bash
# Test everything quickly
python test_local.py
```

### Step-by-Step Debug
```bash
# Comprehensive debugging
python debug.py
```

### Manual Testing
```bash
# 1. Check directory
pwd
ls -la

# 2. Install dependencies
pip install -r requirements.txt

# 3. Test imports
python -c "import fastapi; print('FastAPI OK')"
python -c "from app.core.config import get_settings; print('Config OK')"

# 4. Setup database
python setup_database.py --info

# 5. Start server
python start.py
```

### Test Specific Components

**Test Database**:
```python
python -c "
from app.database.connection import get_database_manager
db = get_database_manager()
print('Database healthy:', db.health_check())
"
```

**Test Gateway Registry**:
```python
python -c "
from app.core.adapter_registry import get_adapter_registry
registry = get_adapter_registry()
print('Available gateways:', registry.list_gateways())
"
```

**Test Configuration**:
```python
python -c "
from app.core.config import get_settings
settings = get_settings()
print('Configured gateways:', settings.get_configured_gateways())
"
```

## 🔍 Diagnostic Information

### System Information
```bash
# Python version
python --version

# Pip version
pip --version

# Installed packages
pip list | grep -E "(fastapi|sqlalchemy|uvicorn)"

# Current directory
pwd

# File permissions
ls -la *.py
```

### Application Information
```bash
# Check main app
python -c "from main import app; print('App created successfully')"

# Check health endpoint
curl http://localhost:8000/health

# Check API docs
curl http://localhost:8000/docs
```

## 📞 Getting Help

### Self-Help Resources
1. **Run Debug Script**: `python debug.py`
2. **Check Logs**: Look for error messages in terminal
3. **Test Locally**: `python test_local.py`
4. **Read Documentation**: Check README.md and guides

### Log Analysis
```bash
# Enable debug logging
export LOG_LEVEL=DEBUG

# Run with verbose output
python -m uvicorn main:app --log-level debug

# Check specific component
python -c "
import logging
logging.basicConfig(level=logging.DEBUG)
from app.core.config import get_settings
"
```

### Common Error Patterns

**Import Errors**:
- Usually missing dependencies
- Wrong directory
- Python path issues

**Database Errors**:
- Database not initialized
- Wrong DATABASE_URL
- Permission issues

**Configuration Errors**:
- Missing .env file
- Wrong environment variables
- Invalid credentials

**Network Errors**:
- Port conflicts
- Firewall issues
- Gateway connectivity

## 🎯 Quick Fixes

### Reset Everything
```bash
# Start fresh
rm -f eloh_gateway.db .env
python debug.py
python setup_database.py --sample-data
python start.py
```

### Minimal Working Setup
```bash
# Just get it running
cd api-gateway
pip install fastapi uvicorn sqlalchemy
python -c "
with open('.env', 'w') as f:
    f.write('APP_NAME=ELOH\\nDEBUG=true\\nDATABASE_URL=sqlite:///./test.db')
"
python setup_database.py
uvicorn main:app --reload
```

### Test Without Database
```bash
# Skip database for testing
export DATABASE_URL=sqlite:///:memory:
python -c "from main import app; print('App works')"
```

---

## 🆘 Still Having Issues?

1. **Run the debug script**: `python debug.py`
2. **Check the logs** for specific error messages
3. **Try the minimal setup** above
4. **Check your Python version** (3.11+ recommended)
5. **Verify you're in the right directory** (`api-gateway/`)

**Remember**: Most issues are caused by:
- Wrong directory (not in `api-gateway/`)
- Missing dependencies (`pip install -r requirements.txt`)
- Missing .env file (copy from `.env.example`)
- Database not initialized (`python setup_database.py`)

**The debug script (`python debug.py`) will fix most common issues automatically!**
