<?php
// BTCPay Configuration Debug Tool
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>BTCPay Configuration Debug</h1>";

try {
    $config = require "includes/btcpay-config.php";
    
    echo "<h2>Raw Configuration Values:</h2>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    
    foreach ($config as $key => $value) {
        $displayValue = htmlspecialchars($value);
        $length = strlen($value);
        $hasSpaces = (trim($value) !== $value) ? 'YES' : 'NO';
        
        echo "<tr>";
        echo "<td style='padding: 8px; font-weight: bold;'>$key</td>";
        echo "<td style='padding: 8px; font-family: monospace;'>$displayValue</td>";
        echo "<td style='padding: 8px;'>Length: $length</td>";
        echo "<td style='padding: 8px; color: " . ($hasSpaces === 'YES' ? 'red' : 'green') . ";'>Spaces: $hasSpaces</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h2>URL Construction Test:</h2>";
    
    $host = trim($config['host']);
    $storeId = trim($config['store_id']);
    
    echo "<p><strong>Host (trimmed):</strong> '" . htmlspecialchars($host) . "'</p>";
    echo "<p><strong>Store ID (trimmed):</strong> '" . htmlspecialchars($storeId) . "'</p>";
    
    $encodedStoreId = urlencode($storeId);
    echo "<p><strong>Encoded Store ID:</strong> '" . htmlspecialchars($encodedStoreId) . "'</p>";
    
    $testUrl = $host . '/api/v1/stores/' . $encodedStoreId . '/invoices';
    echo "<p><strong>Constructed URL:</strong> '" . htmlspecialchars($testUrl) . "'</p>";
    
    // Check for URL validity
    if (filter_var($testUrl, FILTER_VALIDATE_URL)) {
        echo "<p style='color: green;'>✅ URL is valid!</p>";
    } else {
        echo "<p style='color: red;'>❌ URL is invalid!</p>";
    }
    
    // Character-by-character analysis of host
    echo "<h2>Host Character Analysis:</h2>";
    echo "<p>Character-by-character breakdown of host URL:</p>";
    echo "<div style='font-family: monospace; background: #f5f5f5; padding: 10px; border: 1px solid #ccc;'>";
    
    $hostChars = str_split($config['host']);
    foreach ($hostChars as $i => $char) {
        $ascii = ord($char);
        $display = ($char === ' ') ? '[SPACE]' : htmlspecialchars($char);
        echo "[$i] $display (ASCII: $ascii)<br>";
    }
    echo "</div>";
    
    echo "<h2>Test BTCPay Gateway Class:</h2>";
    
    require_once "includes/btcpay-gateway.php";
    $gateway = new BTCPay_Gateway();
    $debug = $gateway->getDebugInfo();
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    foreach ($debug as $key => $value) {
        $displayValue = is_bool($value) ? ($value ? 'TRUE' : 'FALSE') : htmlspecialchars($value);
        echo "<tr>";
        echo "<td style='padding: 8px; font-weight: bold;'>$key</td>";
        echo "<td style='padding: 8px; font-family: monospace;'>$displayValue</td>";
        echo "</tr>";
    }
    echo "</table>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<hr>";
echo "<p><a href='btcpay-troubleshoot.php'>← Back to Troubleshooting</a></p>";
?>
