# 🚀 ELOH Processing Streamlined Payment Widget

## 🔴 **LIVE PAYMENT PROCESSING**

This widget integrates directly with your existing BTCPay Server and NowPayments infrastructure to process **REAL cryptocurrency payments**.

## ✨ Key Features

- **🔴 Live Payments** - Processes real cryptocurrency transactions
- **⚡ BTCPay Server** - Direct integration with your existing setup
- **🌐 NowPayments** - 300+ cryptocurrencies supported
- **🔒 Secure** - Uses your existing payment infrastructure
- **📱 Responsive** - Works on all devices and platforms
- **🎨 Professional** - Modern, branded design

## 🚀 Quick Integration

### 1. Direct Embed (Recommended)

```html
<iframe 
  src="https://elohprocessing.infy.uk/widget/streamlined-payment-widget.php?amount=100.00&email=<EMAIL>" 
  width="450" 
  height="700" 
  frameborder="0"
  style="border-radius: 12px; box-shadow: 0 10px 40px rgba(0,0,0,0.1);">
</iframe>
```

### 2. Dynamic PHP Integration

```php
<?php
$amount = 100.00;
$email = '<EMAIL>';
$description = 'Product Purchase';
?>

<iframe 
  src="https://elohprocessing.infy.uk/widget/streamlined-payment-widget.php?amount=<?php echo $amount; ?>&email=<?php echo urlencode($email); ?>&description=<?php echo urlencode($description); ?>" 
  width="450" 
  height="700" 
  frameborder="0">
</iframe>
```

### 3. Mobile App Integration

```javascript
// React Native
import { WebView } from 'react-native-webview';

<WebView
  source={{ 
    uri: 'https://elohprocessing.infy.uk/widget/streamlined-payment-widget.php?amount=100.00' 
  }}
  style={{ width: 450, height: 700 }}
  onMessage={(event) => {
    // Handle payment completion
    console.log('Payment event:', event.nativeEvent.data);
  }}
/>
```

## 🔧 Configuration Parameters

| Parameter | Type | Description | Example |
|-----------|------|-------------|---------|
| `amount` | number | Payment amount in USD | `100.00` |
| `email` | string | Customer email address | `<EMAIL>` |
| `description` | string | Payment description | `Product Purchase` |
| `gateway` | string | Default gateway | `btcpay`, `nowpayments` |

## 🔴 Live Payment Gateways

### BTCPay Server
- **Integration**: Your existing BTCPay setup
- **Currencies**: Bitcoin (BTC), Lightning Network
- **Features**: Self-hosted, no fees, instant Lightning
- **Configuration**: Uses your `includes/btcpay-gateway.php`

### NowPayments
- **Integration**: Your existing NowPayments setup
- **Currencies**: 300+ cryptocurrencies
- **Features**: Multi-currency, global support
- **Configuration**: Uses your `includes/nowpayments-gateway.php`

## 🔒 How It Works

1. **Customer fills form** - Amount, email, description, gateway selection
2. **Widget validates inputs** - Amount limits, email format
3. **Creates payment** - Uses your existing `Payment_Gateway_Manager`
4. **Redirects to gateway** - BTCPay checkout or NowPayments
5. **Payment processed** - Real cryptocurrency transaction
6. **Webhook notification** - Your existing webhook handlers

## 📊 Payment Flow

```
Customer → Widget Form → Your Gateway Manager → Live Payment Gateway → Blockchain → Confirmation
```

## 🛠️ Technical Integration

### Uses Your Existing Infrastructure

The widget leverages your current payment system:

- **`includes/payment-gateway-manager.php`** - Main payment orchestrator
- **`includes/btcpay-gateway.php`** - BTCPay Server integration
- **`includes/nowpayments-gateway.php`** - NowPayments integration
- **Webhook handlers** - Your existing notification system

### Payment Data Storage

Payments are stored in `widget/payments/` with this structure:

```json
{
  "order_id": "WIDGET_1640995200_1234",
  "amount": 100.00,
  "email": "<EMAIL>",
  "gateway": "btcpay",
  "status": "pending",
  "created_at": 1640995200,
  "payment_data": {
    "checkout_link": "https://...",
    "invoice_id": "..."
  }
}
```

## 🌍 Platform Examples

### E-commerce Sites
```html
<!-- Product checkout page -->
<iframe src="https://elohprocessing.infy.uk/widget/streamlined-payment-widget.php?amount=<?php echo $product_price; ?>&email=<?php echo $customer_email; ?>&description=<?php echo urlencode($product_name); ?>" width="450" height="700" frameborder="0"></iframe>
```

### Service Payments
```html
<!-- Service booking -->
<iframe src="https://elohprocessing.infy.uk/widget/streamlined-payment-widget.php?amount=250.00&description=Consulting%20Service" width="450" height="700" frameborder="0"></iframe>
```

### Donation Pages
```html
<!-- Donation widget -->
<iframe src="https://elohprocessing.infy.uk/widget/streamlined-payment-widget.php?description=Donation%20to%20ELOH%20Processing" width="450" height="700" frameborder="0"></iframe>
```

## 🔔 Event Handling

The widget sends resize events for responsive embedding:

```javascript
window.addEventListener('message', function(event) {
  if (event.data.type === 'eloh_widget_resize') {
    // Auto-resize iframe
    iframe.style.height = event.data.height + 'px';
  }
});
```

## ⚠️ Important Notes

### 🔴 Live Payment Warning
- **Real Money**: This processes actual cryptocurrency payments
- **Test First**: Use small amounts for initial testing
- **Monitor**: Check payment confirmations and webhooks
- **Support**: Have customer support ready

### 🔧 Requirements
- **BTCPay Server**: Your existing setup must be configured
- **NowPayments**: API keys must be active
- **Webhooks**: Notification endpoints must be working
- **SSL**: HTTPS required for live payments

## 🚀 Deployment Checklist

### Pre-Production
- [ ] Test BTCPay Server integration
- [ ] Test NowPayments integration
- [ ] Verify webhook delivery
- [ ] Test with small amounts
- [ ] Check error handling

### Production
- [ ] Configure live API keys
- [ ] Set up monitoring
- [ ] Enable logging
- [ ] Test payment flow end-to-end
- [ ] Prepare customer support

### Post-Deployment
- [ ] Monitor payment success rates
- [ ] Check webhook delivery
- [ ] Review error logs
- [ ] Customer feedback collection

## 🆘 Support

- **Technical Issues**: Check your existing gateway configurations
- **Payment Problems**: Review webhook logs and payment status
- **Integration Help**: Ensure all required files are included

---

## 🎯 **Ready for Live Payments!**

This streamlined widget connects directly to your existing payment infrastructure and processes real cryptocurrency transactions through your BTCPay Server and NowPayments gateways.

**Perfect for:**
- E-commerce checkouts
- Service payments
- Donation processing
- Subscription billing
- Any cryptocurrency payment needs

**Your complete payment solution is ready!** 🚀💰
