"""
Configuration management for ELOH Processing Payment Gateway API (Render Optimized)

This module handles all configuration settings for the payment gateway,
including environment variables, database settings, and gateway credentials.
"""

from pydantic_settings import BaseSettings
from pydantic import Field
from typing import Optional, Dict, Any, List
import os
from functools import lru_cache


class Settings(BaseSettings):
    """
    Application settings loaded from environment variables.
    
    This class uses Pydantic BaseSettings to automatically load
    configuration from environment variables with type validation.
    """
    
    # Application settings
    app_name: str = Field(default="ELOH Processing Payment Gateway", env="APP_NAME")
    app_version: str = Field(default="1.0.0", env="APP_VERSION")
    debug: bool = Field(default=False, env="DEBUG")
    environment: str = Field(default="production", env="ENVIRONMENT")
    
    # API settings
    api_host: str = Field(default="0.0.0.0", env="API_HOST")
    api_port: int = Field(default=8000, env="API_PORT")
    api_prefix: str = Field(default="/v1", env="API_PREFIX")
    
    # Security settings
    secret_key: str = Field(default="your-secret-key-change-in-production", env="SECRET_KEY")
    access_token_expire_minutes: int = Field(default=30, env="ACCESS_TOKEN_EXPIRE_MINUTES")
    
    # Database settings (Render PostgreSQL)
    database_url: Optional[str] = Field(default=None, env="DATABASE_URL")
    redis_url: Optional[str] = Field(default=None, env="REDIS_URL")
    
    # Logging settings
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    log_format: str = Field(default="json", env="LOG_FORMAT")
    
    # Gateway credentials
    # BTCPay Server (Recommended for Dominica)
    btcpay_server_url: Optional[str] = Field(default=None, env="BTCPAY_SERVER_URL")
    btcpay_api_key: Optional[str] = Field(default=None, env="BTCPAY_API_KEY")
    btcpay_store_id: Optional[str] = Field(default=None, env="BTCPAY_STORE_ID")
    btcpay_webhook_secret: Optional[str] = Field(default=None, env="BTCPAY_WEBHOOK_SECRET")
    
    # NowPayments (Recommended for Dominica)
    nowpayments_api_key: Optional[str] = Field(default=None, env="NOWPAYMENTS_API_KEY")
    nowpayments_ipn_secret: Optional[str] = Field(default=None, env="NOWPAYMENTS_IPN_SECRET")
    nowpayments_environment: str = Field(default="production", env="NOWPAYMENTS_ENVIRONMENT")
    
    # Stripe (Limited in Dominica)
    stripe_secret_key: Optional[str] = Field(default=None, env="STRIPE_SECRET_KEY")
    stripe_publishable_key: Optional[str] = Field(default=None, env="STRIPE_PUBLISHABLE_KEY")
    stripe_webhook_secret: Optional[str] = Field(default=None, env="STRIPE_WEBHOOK_SECRET")
    
    # Square (Not available in Dominica)
    square_application_id: Optional[str] = Field(default=None, env="SQUARE_APPLICATION_ID")
    square_access_token: Optional[str] = Field(default=None, env="SQUARE_ACCESS_TOKEN")
    square_webhook_signature_key: Optional[str] = Field(default=None, env="SQUARE_WEBHOOK_SIGNATURE_KEY")
    square_environment: str = Field(default="production", env="SQUARE_ENVIRONMENT")
    
    # Rate limiting
    rate_limit_enabled: bool = Field(default=True, env="RATE_LIMIT_ENABLED")
    rate_limit_requests_per_minute: int = Field(default=100, env="RATE_LIMIT_REQUESTS_PER_MINUTE")
    
    # Monitoring
    enable_metrics: bool = Field(default=True, env="ENABLE_METRICS")
    metrics_endpoint: str = Field(default="/metrics", env="METRICS_ENDPOINT")
    
    # Cache settings
    cache_ttl: int = Field(default=300, env="CACHE_TTL")
    cache_enabled: bool = Field(default=True, env="CACHE_ENABLED")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False
    
    def get_gateway_credentials(self, gateway: str) -> Optional[Dict[str, Any]]:
        """Get credentials for a specific gateway."""
        credentials_map = {
            "stripe": {
                "secret_key": self.stripe_secret_key,
                "publishable_key": self.stripe_publishable_key,
                "webhook_secret": self.stripe_webhook_secret
            },
            "square": {
                "application_id": self.square_application_id,
                "access_token": self.square_access_token,
                "webhook_signature_key": self.square_webhook_signature_key,
                "environment": self.square_environment
            },
            "btcpay": {
                "server_url": self.btcpay_server_url,
                "api_key": self.btcpay_api_key,
                "store_id": self.btcpay_store_id,
                "webhook_secret": self.btcpay_webhook_secret
            },
            "nowpayments": {
                "api_key": self.nowpayments_api_key,
                "ipn_secret": self.nowpayments_ipn_secret,
                "environment": self.nowpayments_environment
            }
        }
        
        return credentials_map.get(gateway)
    
    def is_gateway_configured(self, gateway: str) -> bool:
        """Check if a gateway is properly configured."""
        credentials = self.get_gateway_credentials(gateway)
        if not credentials:
            return False
        
        # Check required fields for each gateway
        required_fields = {
            "stripe": ["secret_key"],
            "square": ["application_id", "access_token"],
            "btcpay": ["server_url", "api_key", "store_id"],
            "nowpayments": ["api_key"]
        }
        
        required = required_fields.get(gateway, [])
        return all(credentials.get(field) for field in required)
    
    def get_configured_gateways(self) -> List[str]:
        """Get list of properly configured gateways."""
        gateways = ["btcpay", "nowpayments", "stripe", "square"]
        return [gateway for gateway in gateways if self.is_gateway_configured(gateway)]


@lru_cache()
def get_settings() -> Settings:
    """Get cached settings instance."""
    return Settings()
