/**
 * ELOH Processing Node.js Integration Example
 * Server-side payment processing and widget serving
 */

const express = require('express');
const cors = require('cors');
const fetch = require('node-fetch'); // npm install node-fetch
const path = require('path');

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static('public'));

// ELOH Payment SDK Configuration
const ELOH_CONFIG = {
  apiEndpoint: 'https://elohprocessing.infy.uk/api',
  widgetUrl: 'https://elohprocessing.infy.uk/widget/universal-payment-widget.html',
  environment: process.env.NODE_ENV === 'production' ? 'production' : 'development'
};

/**
 * ELOH Payment Service Class
 */
class ELOHPaymentService {
  constructor(config) {
    this.config = config;
  }

  /**
   * Create payment via ELOH API
   */
  async createPayment(paymentData) {
    try {
      const orderId = this.generateOrderId();
      const payload = {
        ...paymentData,
        order_id: orderId,
        currency: paymentData.currency || 'USD',
        source: 'nodejs_server'
      };

      const endpoint = `${this.config.apiEndpoint}/${paymentData.gateway}-create.php`;
      
      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        body: JSON.stringify(payload)
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      
      // Log payment creation
      console.log(`Payment created: ${orderId} for $${paymentData.amount}`);
      
      return result;

    } catch (error) {
      console.error('Payment creation failed:', error);
      throw error;
    }
  }

  /**
   * Get payment status
   */
  async getPaymentStatus(orderId) {
    try {
      const response = await fetch(`${this.config.apiEndpoint}/payment-status.php`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ order_id: orderId })
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      return await response.json();

    } catch (error) {
      console.error('Status check failed:', error);
      throw error;
    }
  }

  /**
   * Generate unique order ID
   */
  generateOrderId() {
    return 'NODE_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  /**
   * Validate payment data
   */
  validatePaymentData(data) {
    const errors = [];

    if (!data.amount || data.amount < 5) {
      errors.push('Minimum amount is $5.00');
    }
    if (data.amount > 10000) {
      errors.push('Maximum amount is $10,000.00');
    }
    if (!data.email || !data.email.includes('@')) {
      errors.push('Valid email address is required');
    }
    if (!data.gateway || !['btcpay', 'nowpayments'].includes(data.gateway)) {
      errors.push('Valid payment gateway is required');
    }

    return {
      valid: errors.length === 0,
      errors: errors
    };
  }
}

// Initialize payment service
const paymentService = new ELOHPaymentService(ELOH_CONFIG);

/**
 * API Routes
 */

// Create payment endpoint
app.post('/api/payments/create', async (req, res) => {
  try {
    const { amount, email, gateway, description } = req.body;
    
    const paymentData = {
      amount: parseFloat(amount),
      email: email,
      gateway: gateway,
      description: description || 'Node.js Payment'
    };

    // Validate payment data
    const validation = paymentService.validatePaymentData(paymentData);
    if (!validation.valid) {
      return res.status(400).json({
        success: false,
        errors: validation.errors
      });
    }

    // Create payment
    const result = await paymentService.createPayment(paymentData);
    
    res.json({
      success: true,
      ...result
    });

  } catch (error) {
    console.error('Payment creation error:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Get payment status endpoint
app.get('/api/payments/:orderId/status', async (req, res) => {
  try {
    const { orderId } = req.params;
    const result = await paymentService.getPaymentStatus(orderId);
    
    res.json(result);

  } catch (error) {
    console.error('Status check error:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Webhook endpoint for payment notifications
app.post('/webhook/payment', express.raw({ type: 'application/json' }), (req, res) => {
  try {
    const payload = JSON.parse(req.body);
    
    console.log('Payment webhook received:', payload);
    
    // Process webhook based on gateway
    switch (payload.gateway) {
      case 'btcpay':
        handleBTCPayWebhook(payload);
        break;
      case 'nowpayments':
        handleNowPaymentsWebhook(payload);
        break;
      default:
        console.log('Unknown gateway webhook:', payload.gateway);
    }
    
    res.status(200).send('OK');

  } catch (error) {
    console.error('Webhook processing error:', error);
    res.status(500).send('Error');
  }
});

/**
 * Frontend Routes
 */

// Serve payment page with embedded widget
app.get('/payment', (req, res) => {
  const { amount = 100, email = '', description = 'Payment' } = req.query;
  
  res.send(`
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Payment - ELOH Processing</title>
        <style>
            body {
                font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                margin: 0;
                padding: 20px;
                min-height: 100vh;
                display: flex;
                align-items: center;
                justify-content: center;
            }
            .container {
                background: white;
                border-radius: 16px;
                padding: 32px;
                box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
                max-width: 500px;
                width: 100%;
            }
            .header {
                text-align: center;
                margin-bottom: 32px;
            }
            .title {
                font-size: 2rem;
                font-weight: 700;
                background: linear-gradient(135deg, #667eea, #764ba2);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                margin-bottom: 8px;
            }
            .subtitle {
                color: #6b7280;
                font-size: 1rem;
            }
            #payment-widget {
                margin: 20px 0;
            }
        </style>
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1 class="title">ELOH Processing</h1>
                <p class="subtitle">Secure Cryptocurrency Payments</p>
            </div>
            
            <div id="payment-widget"></div>
        </div>
        
        <script src="https://elohprocessing.infy.uk/widget/eloh-payment-sdk.js"></script>
        <script>
            const eloh = new ELOHPaymentSDK({
                debug: ${ELOH_CONFIG.environment === 'development'}
            });
            
            const widget = eloh.createWidget({
                container: '#payment-widget',
                amount: ${amount},
                email: '${email}',
                description: '${description}',
                
                onSuccess: (widget, data) => {
                    alert('Payment successful! Order ID: ' + data.order_id);
                    console.log('Payment completed:', data);
                },
                
                onError: (widget, error) => {
                    alert('Payment failed: ' + error.message);
                    console.error('Payment error:', error);
                },
                
                onPaymentCreated: (widget, data) => {
                    console.log('Payment created, redirecting to gateway:', data);
                }
            });
        </script>
    </body>
    </html>
  `);
});

// Serve checkout page with form
app.get('/checkout', (req, res) => {
  res.send(`
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Checkout - ELOH Processing</title>
        <style>
            body { font-family: Inter, sans-serif; background: #f8fafc; margin: 0; padding: 20px; }
            .container { max-width: 800px; margin: 0 auto; background: white; border-radius: 12px; padding: 32px; box-shadow: 0 4px 20px rgba(0,0,0,0.1); }
            .form-group { margin-bottom: 20px; }
            .form-label { display: block; font-weight: 600; margin-bottom: 8px; }
            .form-input { width: 100%; padding: 12px; border: 2px solid #e5e7eb; border-radius: 8px; font-size: 1rem; }
            .form-input:focus { outline: none; border-color: #667eea; }
            .btn { background: linear-gradient(135deg, #667eea, #764ba2); color: white; border: none; padding: 12px 24px; border-radius: 8px; font-weight: 600; cursor: pointer; }
            .btn:hover { transform: translateY(-1px); }
        </style>
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    </head>
    <body>
        <div class="container">
            <h1>Checkout</h1>
            <form id="checkout-form">
                <div class="form-group">
                    <label class="form-label">Amount ($)</label>
                    <input type="number" id="amount" class="form-input" value="100.00" min="5" step="0.01" required>
                </div>
                <div class="form-group">
                    <label class="form-label">Email</label>
                    <input type="email" id="email" class="form-input" placeholder="<EMAIL>" required>
                </div>
                <div class="form-group">
                    <label class="form-label">Description</label>
                    <input type="text" id="description" class="form-input" placeholder="Payment description">
                </div>
                <button type="submit" class="btn">Proceed to Payment</button>
            </form>
        </div>
        
        <script>
            document.getElementById('checkout-form').addEventListener('submit', (e) => {
                e.preventDefault();
                const amount = document.getElementById('amount').value;
                const email = document.getElementById('email').value;
                const description = document.getElementById('description').value;
                
                window.location.href = '/payment?amount=' + amount + '&email=' + encodeURIComponent(email) + '&description=' + encodeURIComponent(description);
            });
        </script>
    </body>
    </html>
  `);
});

/**
 * Webhook Handlers
 */
function handleBTCPayWebhook(payload) {
  console.log('Processing BTCPay webhook:', payload);
  // Implement BTCPay webhook logic
}

function handleNowPaymentsWebhook(payload) {
  console.log('Processing NowPayments webhook:', payload);
  // Implement NowPayments webhook logic
}

/**
 * Start Server
 */
app.listen(PORT, () => {
  console.log(`🚀 ELOH Processing Node.js Server running on port ${PORT}`);
  console.log(`📱 Checkout: http://localhost:${PORT}/checkout`);
  console.log(`💳 Payment: http://localhost:${PORT}/payment`);
  console.log(`🔗 API: http://localhost:${PORT}/api/payments/create`);
});

module.exports = { app, paymentService };
