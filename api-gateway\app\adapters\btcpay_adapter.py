"""
BTCPay Server Payment Gateway Adapter for ELOH Processing

This adapter implements the GatewayAdapter interface for BTCPay Server payments.
Handles Bitcoin and Lightning Network payments through BTCPay Server API.
"""

import aiohttp
import json
from typing import Dict, Any, Optional, List
from decimal import Decimal
from datetime import datetime
import logging

from .gateway_adapter import GatewayAdapter, GatewayCredentials
from ..models.payment import PaymentRequest, PaymentResponse, PaymentStatus, RefundRequest, RefundResponse
from ..models.customer import CustomerRequest, CustomerResponse
from ..core.exceptions import PaymentGatewayException

logger = logging.getLogger(__name__)


class BTCPayCredentials(GatewayCredentials):
    """BTCPay Server-specific credentials"""
    
    def __init__(self, server_url: str, api_key: str, store_id: str, webhook_secret: Optional[str] = None):
        self.server_url = server_url.rstrip('/')
        self.api_key = api_key
        self.store_id = store_id
        self.webhook_secret = webhook_secret
    
    def is_valid(self) -> bool:
        """Validate BTCPay credentials"""
        return bool(self.server_url and self.api_key and self.store_id)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for secure storage"""
        return {
            "server_url": self.server_url,
            "api_key": self.api_key,
            "store_id": self.store_id,
            "webhook_secret": self.webhook_secret
        }


class BTCPayAdapter(GatewayAdapter):
    """
    BTCPay Server payment gateway adapter.
    
    Handles Bitcoin and Lightning Network payments through BTCPay Server.
    Supports both on-chain Bitcoin transactions and instant Lightning payments.
    """
    
    def __init__(self, credentials: BTCPayCredentials, config: Optional[Dict[str, Any]] = None):
        super().__init__(credentials, config)
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        
        # Default configuration
        self.default_config = {
            "timeout": 30,
            "payment_methods": ["BTC", "BTC-LightningNetwork"],
            "checkout_type": "V1",  # or "V2"
            "default_currency": "USD"
        }
        self.config = {**self.default_config, **(config or {})}
    
    def _get_gateway_name(self) -> str:
        """Return gateway name"""
        return "btcpay"
    
    async def validate_credentials(self) -> bool:
        """Validate BTCPay credentials by checking store access"""
        try:
            async with aiohttp.ClientSession() as session:
                headers = {
                    "Authorization": f"token {self.credentials.api_key}",
                    "Content-Type": "application/json"
                }
                
                url = f"{self.credentials.server_url}/api/v1/stores/{self.credentials.store_id}"
                async with session.get(url, headers=headers) as response:
                    return response.status == 200
                    
        except Exception as e:
            self.logger.error(f"BTCPay credential validation failed: {e}")
            return False
    
    async def process_payment(self, payment_request: PaymentRequest, customer_id: Optional[str] = None) -> PaymentResponse:
        """Process payment through BTCPay Server"""
        try:
            self.logger.info(f"Processing BTCPay payment for amount {payment_request.amount} {payment_request.currency}")
            
            # Prepare BTCPay invoice data
            invoice_data = {
                "amount": str(payment_request.amount),
                "currency": payment_request.currency,
                "orderId": payment_request.reference or f"eloh_{int(datetime.now().timestamp())}",
                "notificationEmail": customer_id,  # Use customer_id as email if available
                "redirectURL": payment_request.success_url,
                "metadata": {
                    "eloh_payment_id": f"btcpay_{int(datetime.now().timestamp())}",
                    "description": payment_request.description or "Payment",
                    **(payment_request.metadata or {})
                }
            }
            
            # Add webhook URL if configured
            if payment_request.webhook_url:
                invoice_data["notificationURL"] = payment_request.webhook_url
            
            # Create BTCPay invoice
            invoice = await self._make_btcpay_request(
                "POST",
                f"/api/v1/stores/{self.credentials.store_id}/invoices",
                data=invoice_data
            )
            
            return self._convert_btcpay_invoice_to_response(invoice, payment_request)
            
        except Exception as e:
            self.logger.error(f"BTCPay payment processing error: {e}")
            raise PaymentGatewayException(f"BTCPay payment failed: {str(e)}")
    
    async def get_payment_status(self, gateway_payment_id: str) -> PaymentResponse:
        """Get payment status from BTCPay Server"""
        try:
            invoice = await self._make_btcpay_request(
                "GET",
                f"/api/v1/stores/{self.credentials.store_id}/invoices/{gateway_payment_id}"
            )
            
            return self._convert_btcpay_invoice_to_response(invoice)
            
        except Exception as e:
            self.logger.error(f"BTCPay status check error: {e}")
            raise PaymentGatewayException(f"Failed to retrieve payment status: {str(e)}")
    
    async def refund_payment(self, refund_request: RefundRequest, gateway_payment_id: str) -> RefundResponse:
        """Process refund through BTCPay Server"""
        # Note: BTCPay Server doesn't support automatic refunds for Bitcoin
        # This would typically require manual intervention or a separate refund process
        raise PaymentGatewayException("Bitcoin payments cannot be automatically refunded. Manual refund process required.")
    
    async def create_customer(self, customer_request: CustomerRequest) -> CustomerResponse:
        """Create customer in BTCPay Server (limited customer support)"""
        # BTCPay Server has limited customer management
        # We'll create a basic customer record with available information
        
        customer_id = f"btcpay_{customer_request.email}_{int(datetime.now().timestamp())}"
        
        return CustomerResponse(
            customer_id=customer_id,
            gateway_customer_ids={"btcpay": customer_id},
            email=customer_request.email,
            first_name=customer_request.first_name,
            last_name=customer_request.last_name,
            phone=customer_request.phone,
            address=customer_request.address,
            company=customer_request.company,
            created_at=datetime.now(),
            metadata=customer_request.metadata
        )
    
    async def get_customer(self, gateway_customer_id: str) -> CustomerResponse:
        """Get customer from BTCPay Server (limited support)"""
        # BTCPay Server doesn't have traditional customer management
        # This would need to be implemented with external storage
        raise PaymentGatewayException("Customer retrieval not supported by BTCPay Server")
    
    async def process_webhook(self, webhook_data: Dict[str, Any], signature: Optional[str] = None) -> Dict[str, Any]:
        """Process BTCPay Server webhook"""
        try:
            # BTCPay webhooks are typically JSON payloads
            # Signature verification would depend on BTCPay configuration
            
            event_type = webhook_data.get("type", "unknown")
            invoice_id = webhook_data.get("invoiceId")
            
            return {
                "gateway": "btcpay",
                "event_type": event_type,
                "event_id": webhook_data.get("deliveryId"),
                "created": datetime.now(),
                "data": {
                    "invoice_id": invoice_id,
                    "status": webhook_data.get("status"),
                    "amount": webhook_data.get("amount"),
                    "currency": webhook_data.get("currency")
                },
                "raw_event": webhook_data
            }
            
        except Exception as e:
            self.logger.error(f"BTCPay webhook processing error: {e}")
            raise PaymentGatewayException(f"Webhook processing failed: {str(e)}")
    
    async def get_supported_currencies(self) -> List[str]:
        """Get BTCPay supported currencies"""
        return ["BTC", "USD", "EUR", "GBP", "CAD", "AUD", "JPY"]
    
    async def get_supported_payment_methods(self) -> List[str]:
        """Get BTCPay supported payment methods"""
        return ["bitcoin", "lightning"]
    
    async def cancel_payment(self, gateway_payment_id: str) -> PaymentResponse:
        """Cancel BTCPay invoice"""
        try:
            # Mark invoice as invalid in BTCPay
            await self._make_btcpay_request(
                "DELETE",
                f"/api/v1/stores/{self.credentials.store_id}/invoices/{gateway_payment_id}"
            )
            
            # Get updated invoice status
            return await self.get_payment_status(gateway_payment_id)
            
        except Exception as e:
            self.logger.error(f"BTCPay payment cancellation error: {e}")
            raise PaymentGatewayException(f"Payment cancellation failed: {str(e)}")
    
    # Helper methods
    
    async def _make_btcpay_request(self, method: str, endpoint: str, data: Optional[Dict] = None) -> Dict[str, Any]:
        """Make async request to BTCPay Server API"""
        try:
            async with aiohttp.ClientSession() as session:
                headers = {
                    "Authorization": f"token {self.credentials.api_key}",
                    "Content-Type": "application/json"
                }
                
                url = f"{self.credentials.server_url}{endpoint}"
                
                kwargs = {"headers": headers}
                if data:
                    kwargs["data"] = json.dumps(data)
                
                async with session.request(method, url, **kwargs) as response:
                    if response.status >= 400:
                        error_text = await response.text()
                        raise PaymentGatewayException(f"BTCPay API error {response.status}: {error_text}")
                    
                    return await response.json()
                    
        except aiohttp.ClientError as e:
            self.logger.error(f"BTCPay API request failed: {e}")
            raise PaymentGatewayException(f"BTCPay API request failed: {str(e)}")
    
    def _convert_btcpay_invoice_to_response(self, invoice: Dict[str, Any], original_request: Optional[PaymentRequest] = None) -> PaymentResponse:
        """Convert BTCPay invoice to our standard PaymentResponse"""
        
        # Map BTCPay status to our standard status
        status_mapping = {
            "New": PaymentStatus.PENDING,
            "Processing": PaymentStatus.PROCESSING,
            "Settled": PaymentStatus.COMPLETED,
            "Complete": PaymentStatus.COMPLETED,
            "Expired": PaymentStatus.FAILED,
            "Invalid": PaymentStatus.CANCELLED
        }
        
        # Extract checkout URL
        checkout_url = None
        if "checkoutLink" in invoice:
            checkout_url = invoice["checkoutLink"]
        elif "url" in invoice:
            checkout_url = invoice["url"]
        
        return PaymentResponse(
            payment_id=f"btcpay_{invoice['id']}",
            gateway_payment_id=invoice["id"],
            reference=invoice.get("orderId"),
            amount=Decimal(str(invoice.get("amount", 0))),
            currency=invoice.get("currency", "BTC"),
            status=status_mapping.get(invoice.get("status"), PaymentStatus.PENDING),
            gateway_used="btcpay",
            checkout_url=checkout_url,
            created_at=datetime.fromisoformat(invoice["createdTime"].replace("Z", "+00:00")) if "createdTime" in invoice else datetime.now(),
            expires_at=datetime.fromisoformat(invoice["expirationTime"].replace("Z", "+00:00")) if "expirationTime" in invoice else None,
            gateway_response=invoice,
            metadata=invoice.get("metadata", {})
        )
