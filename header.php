<!-- header.php -->
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title><?php echo isset($page_title) ? $page_title . ' - ' : ''; ?>ELOH Processing LLC</title>

  <!-- PWA Meta Tags -->
  <meta name="description" content="Sustainable cryptocurrency mining and financial services powered by renewable energy. Professional crypto consulting, mining pool membership, and investment opportunities.">
  <meta name="keywords" content="cryptocurrency mining, bitcoin, renewable energy, crypto consulting, mining pool, investment, sustainable mining">
  <meta name="author" content="ELOH Processing LLC">
  <meta name="robots" content="index, follow">

  <!-- PWA Manifest -->
  <link rel="manifest" href="/manifest.json">

  <!-- PWA Theme Colors -->
  <meta name="theme-color" content="#764ba2">
  <meta name="msapplication-TileColor" content="#667eea">

  <!-- Apple PWA Meta Tags -->
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="default">
  <meta name="apple-mobile-web-app-title" content="ELOH Processing">
  <link rel="apple-touch-icon" href="/assets/icons/icon-192x192.png">

  <!-- Favicon -->
  <link rel="icon" type="image/png" sizes="32x32" href="/assets/icons/icon-32x32.png">
  <link rel="shortcut icon" href="/favicon.ico">

  <!-- Open Graph Meta Tags -->
  <meta property="og:title" content="<?php echo isset($page_title) ? $page_title . ' - ' : ''; ?>ELOH Processing LLC">
  <meta property="og:description" content="Sustainable cryptocurrency mining and financial services powered by renewable energy">
  <meta property="og:image" content="/assets/icons/icon-512x512.png">
  <meta property="og:url" content="https://elohprocessing.infy.uk">
  <meta property="og:type" content="website">

  <!-- Twitter Card Meta Tags -->
  <meta name="twitter:card" content="summary_large_image">
  <meta name="twitter:title" content="<?php echo isset($page_title) ? $page_title . ' - ' : ''; ?>ELOH Processing LLC">
  <meta name="twitter:description" content="Sustainable cryptocurrency mining and financial services powered by renewable energy">
  <meta name="twitter:image" content="/assets/icons/icon-512x512.png">

  <!-- Google Fonts for Modern Typography -->
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=JetBrains+Mono:wght@400;500;600&display=swap" rel="stylesheet">

  <!-- PWA Application Script -->
  <script src="/assets/js/pwa-app.js" defer></script>

  <style>
    /* 🎨 FUTURISTIC DESIGN SYSTEM - ELOH Processing */

    /* CSS Variables for Light Theme */
    :root {
      /* Primary Brand Colors */
      --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      --primary-color: #667eea;
      --primary-dark: #5a67d8;
      --accent-color: #764ba2;
      --accent-light: #9f7aea;

      /* Neutral Colors */
      --background-color: #fafbfc;
      --surface-color: #ffffff;
      --text-primary: #1a202c;
      --text-secondary: #4a5568;
      --text-muted: #718096;
      --border-color: #e2e8f0;
      --border-light: #f7fafc;

      /* Interactive States */
      --hover-bg: #f7fafc;
      --active-bg: #edf2f7;
      --focus-ring: rgba(102, 126, 234, 0.3);

      /* Shadows & Effects */
      --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
      --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
      --shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.1);
      --shadow-xl: 0 20px 40px rgba(0, 0, 0, 0.1);
      --glow: 0 0 20px rgba(102, 126, 234, 0.3);

      /* Typography */
      --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
      --font-mono: 'JetBrains Mono', 'Fira Code', monospace;

      /* Spacing Scale */
      --space-xs: 0.25rem;
      --space-sm: 0.5rem;
      --space-md: 1rem;
      --space-lg: 1.5rem;
      --space-xl: 2rem;
      --space-2xl: 3rem;
      --space-3xl: 4rem;

      /* Border Radius */
      --radius-sm: 0.375rem;
      --radius-md: 0.5rem;
      --radius-lg: 0.75rem;
      --radius-xl: 1rem;
      --radius-2xl: 1.5rem;

      /* Transitions */
      --transition-fast: 0.15s ease-out;
      --transition-base: 0.2s ease-out;
      --transition-slow: 0.3s ease-out;
    }

    /* Dark Theme Variables */
    body.dark-theme {
      --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      --primary-color: #7c3aed;
      --primary-dark: #6d28d9;
      --accent-color: #a855f7;
      --accent-light: #c084fc;

      --background-color: #0f0f23;
      --surface-color: #1a1a2e;
      --text-primary: #f7fafc;
      --text-secondary: #e2e8f0;
      --text-muted: #a0aec0;
      --border-color: #2d3748;
      --border-light: #4a5568;

      --hover-bg: #2d3748;
      --active-bg: #4a5568;
      --focus-ring: rgba(124, 58, 237, 0.4);

      --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.3);
      --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.3);
      --shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.3);
      --shadow-xl: 0 20px 40px rgba(0, 0, 0, 0.4);
      --glow: 0 0 30px rgba(124, 58, 237, 0.4);
    }

    /* 🎯 GLOBAL RESET & BASE STYLES */
    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }

    html {
      scroll-behavior: smooth;
      font-size: 16px;
    }

    body {
      background: var(--background-color);
      color: var(--text-primary);
      font-family: var(--font-primary);
      font-weight: 400;
      line-height: 1.6;
      margin: 0;
      transition: all var(--transition-slow);
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
    }

    /* Typography Scale */
    h1, h2, h3, h4, h5, h6 {
      font-weight: 700;
      line-height: 1.2;
      margin-bottom: var(--space-md);
      color: var(--text-primary);
    }

    h1 { font-size: 3.5rem; font-weight: 800; }
    h2 { font-size: 2.5rem; font-weight: 700; }
    h3 { font-size: 2rem; font-weight: 600; }
    h4 { font-size: 1.5rem; font-weight: 600; }
    h5 { font-size: 1.25rem; font-weight: 500; }
    h6 { font-size: 1rem; font-weight: 500; }

    p {
      margin-bottom: var(--space-md);
      color: var(--text-secondary);
    }

    a {
      color: var(--primary-color);
      text-decoration: none;
      transition: all var(--transition-fast);
    }

    a:hover {
      color: var(--primary-dark);
      text-decoration: underline;
    }
  </style>

  <style>
    /* 🚀 FUTURISTIC HEADER DESIGN */
    header {
      background: var(--surface-color);
      backdrop-filter: blur(20px);
      border-bottom: 1px solid var(--border-color);
      padding: var(--space-lg) var(--space-xl);
      position: sticky;
      top: 0;
      z-index: 1000;
      box-shadow: var(--shadow-sm);
      transition: all var(--transition-base);
    }

    .header-container {
      max-width: 1400px;
      margin: 0 auto;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    /* 🎯 LOGO DESIGN */
    .logo {
      display: flex;
      align-items: center;
      gap: var(--space-md);
    }

    .logo-image {
      height: 48px;
      width: auto;
      transition: transform var(--transition-base);
    }

    .logo-image:hover {
      transform: scale(1.05);
    }

    .logo-text {
      font-size: 1.5rem;
      font-weight: 800;
      background: var(--primary-gradient);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      letter-spacing: -0.02em;
    }

    /* 🧭 NAVIGATION DESIGN */
    .nav-container {
      display: flex;
      align-items: center;
      gap: var(--space-xl);
    }

    nav ul {
      display: flex;
      list-style: none;
      gap: var(--space-lg);
      margin: 0;
      padding: 0;
    }

    nav ul li a {
      color: var(--text-secondary);
      font-weight: 500;
      font-size: 0.95rem;
      padding: var(--space-sm) var(--space-md);
      border-radius: var(--radius-md);
      transition: all var(--transition-fast);
      position: relative;
      overflow: hidden;
    }

    nav ul li a::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: var(--primary-gradient);
      transition: left var(--transition-base);
      z-index: -1;
      opacity: 0.1;
    }

    nav ul li a:hover {
      color: var(--primary-color);
      background: var(--hover-bg);
      transform: translateY(-1px);
      text-decoration: none;
    }

    nav ul li a:hover::before {
      left: 0;
    }

    nav ul li a.active {
      color: var(--primary-color);
      background: var(--active-bg);
      font-weight: 600;
    }

    /* 🌙 THEME TOGGLE BUTTON */
    .theme-toggle {
      background: var(--surface-color);
      border: 1px solid var(--border-color);
      color: var(--text-secondary);
      padding: var(--space-sm) var(--space-md);
      border-radius: var(--radius-lg);
      cursor: pointer;
      font-size: 0.9rem;
      font-weight: 500;
      transition: all var(--transition-fast);
      display: flex;
      align-items: center;
      gap: var(--space-xs);
      box-shadow: var(--shadow-sm);
    }

    .theme-toggle:hover {
      background: var(--hover-bg);
      border-color: var(--primary-color);
      transform: translateY(-1px);
      box-shadow: var(--shadow-md);
    }

    .theme-icon {
      font-size: 1.1em;
      transition: transform var(--transition-base);
    }

    .theme-toggle:hover .theme-icon {
      transform: rotate(180deg);
    }
  </style>

  <style>
    /* 📱 MOBILE RESPONSIVE DESIGN */
    .mobile-menu-toggle {
      display: none;
      background: none;
      border: none;
      cursor: pointer;
      padding: var(--space-sm);
      border-radius: var(--radius-md);
      transition: all var(--transition-fast);
    }

    .mobile-menu-toggle:hover {
      background: var(--hover-bg);
    }

    .hamburger {
      display: flex;
      flex-direction: column;
      gap: 3px;
    }

    .hamburger span {
      width: 24px;
      height: 2px;
      background: var(--text-primary);
      border-radius: 2px;
      transition: all var(--transition-base);
    }

    .mobile-menu-toggle.active .hamburger span:nth-child(1) {
      transform: rotate(45deg) translate(6px, 6px);
    }

    .mobile-menu-toggle.active .hamburger span:nth-child(2) {
      opacity: 0;
    }

    .mobile-menu-toggle.active .hamburger span:nth-child(3) {
      transform: rotate(-45deg) translate(6px, -6px);
    }

    @media (max-width: 768px) {
      .header-container {
        padding: 0 var(--space-md);
      }

      .mobile-menu-toggle {
        display: block;
      }

      .nav-container {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: var(--surface-color);
        border-top: 1px solid var(--border-color);
        box-shadow: var(--shadow-lg);
        transform: translateY(-100%);
        opacity: 0;
        visibility: hidden;
        transition: all var(--transition-base);
        z-index: 999;
      }

      .nav-container.active {
        transform: translateY(0);
        opacity: 1;
        visibility: visible;
      }

      nav ul {
        flex-direction: column;
        padding: var(--space-lg);
        gap: var(--space-sm);
      }

      nav ul li a {
        display: block;
        padding: var(--space-md);
        border-radius: var(--radius-md);
        text-align: center;
      }

      .theme-toggle {
        margin: var(--space-md) var(--space-lg);
      }
    }
  </style>

  <style>
    /* 🎨 FUTURISTIC HERO SECTION */
    .hero {
      background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
      min-height: 60vh;
      display: flex;
      align-items: center;
      justify-content: center;
      text-align: center;
      color: white;
      padding: var(--space-3xl) var(--space-xl);
      position: relative;
      overflow: hidden;
    }

    .hero::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
      opacity: 0.3;
    }

    .hero-content {
      position: relative;
      z-index: 1;
      max-width: 800px;
    }

    .hero h1 {
      font-size: clamp(2.5rem, 5vw, 4rem);
      font-weight: 900;
      margin-bottom: var(--space-lg);
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
      letter-spacing: -0.02em;
    }

    .hero p {
      font-size: clamp(1.1rem, 2vw, 1.4rem);
      margin-bottom: var(--space-xl);
      opacity: 0.95;
      font-weight: 400;
    }
  </style>
  <style>
    /* 🎯 MODERN COMPONENT STYLES */

    /* Section Layout */
    .section {
      max-width: 1200px;
      margin: var(--space-3xl) auto;
      padding: 0 var(--space-xl);
    }

    .section h2 {
      font-size: clamp(2rem, 4vw, 3rem);
      text-align: center;
      margin-bottom: var(--space-xl);
      background: var(--primary-gradient);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      font-weight: 800;
    }

    .section p {
      font-size: 1.125rem;
      line-height: 1.7;
      margin-bottom: var(--space-lg);
      color: var(--text-secondary);
    }

    /* Grid Layouts */
    .two-col {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: var(--space-2xl);
      margin-top: var(--space-2xl);
    }

    .three-col {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: var(--space-xl);
      margin-top: var(--space-2xl);
    }

    /* Card Components */
    .card {
      background: var(--surface-color);
      border: 1px solid var(--border-color);
      border-radius: var(--radius-xl);
      padding: var(--space-xl);
      box-shadow: var(--shadow-sm);
      transition: all var(--transition-base);
    }

    .card:hover {
      transform: translateY(-4px);
      box-shadow: var(--shadow-lg);
      border-color: var(--primary-color);
    }

    /* CTA Buttons */
    .cta-button {
      display: inline-flex;
      align-items: center;
      gap: var(--space-sm);
      padding: var(--space-md) var(--space-xl);
      background: var(--primary-gradient);
      color: white;
      border: none;
      border-radius: var(--radius-lg);
      font-size: 1rem;
      font-weight: 600;
      text-decoration: none;
      cursor: pointer;
      transition: all var(--transition-fast);
      box-shadow: var(--shadow-md);
      position: relative;
      overflow: hidden;
    }

    .cta-button::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
      transition: left 0.5s;
    }

    .cta-button:hover {
      transform: translateY(-2px);
      box-shadow: var(--shadow-lg);
    }

    .cta-button:hover::before {
      left: 100%;
    }

    .cta-button.secondary {
      background: var(--surface-color);
      color: var(--primary-color);
      border: 2px solid var(--primary-color);
    }

    .cta-button.secondary:hover {
      background: var(--primary-color);
      color: white;
    }
  </style>
  <style>
    /* Mobile Media Queries for Header */
    @media (max-width: 768px) {
      header {
        flex-direction: column;
        align-items: flex-start;
      }
      /* Logo remains at top */
      .logo { order: 1; width: 100%; }
      /* Create a container for mobile controls */
      .mobile-controls {
        order: 2;
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
        margin-top: 10px;
      }
      /* Hamburger on the left */
      .menu-toggle {
        display: flex;
      }
      /* Theme toggle remains on the right */
      .theme-toggle {
        display: block;
      }
      /* Navigation dropdown */
      nav {
        order: 3;
        display: none; /* Hide nav by default */
        width: 100%;
      }
      nav.active {
        display: block; /* Show nav when active */
      }
      nav ul {
        flex-direction: column;
        text-align: center;
        width: 100%;
      }
    }
  </style>
</head>
<body>
  <header>
    <div class="header-container">
      <!-- Logo Section -->
      <div class="logo">
        <a href="index.php">
          <img src="https://images2.imgbox.com/12/cb/fbYy71EY_o.png" alt="ELOH Processing LLC Logo" class="logo-image">
          <span class="logo-text">ELOH</span>
        </a>
      </div>

      <!-- Mobile Menu Toggle -->
      <button class="mobile-menu-toggle" id="mobile-menu-toggle">
        <div class="hamburger">
          <span></span>
          <span></span>
          <span></span>
        </div>
      </button>

      <!-- Navigation Container -->
      <div class="nav-container" id="nav-container">
        <nav>
          <ul>
            <li><a href="index.php" class="nav-link">Home</a></li>
            <li><a href="about.php" class="nav-link">About</a></li>
            <li><a href="operations.php" class="nav-link">Operations</a></li>
            <li><a href="services.php" class="nav-link">Services</a></li>
            <li><a href="investors.php" class="nav-link">Investors</a></li>
            <li><a href="contact.php" class="nav-link">Contact</a></li>
          </ul>
        </nav>

        <!-- Theme Toggle -->
        <button class="theme-toggle" id="theme-toggle">
          <span class="theme-icon">🌙</span>
          <span class="theme-text">Dark</span>
        </button>
      </div>
    </div>
  </header>

  <script>
    // 🚀 FUTURISTIC INTERACTION SYSTEM

    // Theme Management
    const themeToggle = document.getElementById('theme-toggle');
    const themeIcon = themeToggle.querySelector('.theme-icon');
    const themeText = themeToggle.querySelector('.theme-text');

    // Check for stored theme preference on page load
    const currentTheme = localStorage.getItem('theme');
    if (currentTheme === 'dark') {
      document.body.classList.add('dark-theme');
      updateThemeToggle(true);
    }

    // Toggle theme on button click
    themeToggle.addEventListener('click', function() {
      const isDark = document.body.classList.toggle('dark-theme');
      localStorage.setItem('theme', isDark ? 'dark' : 'light');
      updateThemeToggle(isDark);
    });

    function updateThemeToggle(isDark) {
      themeIcon.textContent = isDark ? '☀️' : '🌙';
      themeText.textContent = isDark ? 'Light' : 'Dark';
    }

    // Mobile Menu Management
    const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
    const navContainer = document.getElementById('nav-container');

    mobileMenuToggle.addEventListener('click', function() {
      const isActive = mobileMenuToggle.classList.toggle('active');
      navContainer.classList.toggle('active');

      // Prevent body scroll when menu is open
      document.body.style.overflow = isActive ? 'hidden' : '';
    });

    // Close mobile menu when clicking outside
    document.addEventListener('click', function(e) {
      if (!mobileMenuToggle.contains(e.target) && !navContainer.contains(e.target)) {
        mobileMenuToggle.classList.remove('active');
        navContainer.classList.remove('active');
        document.body.style.overflow = '';
      }
    });

    // Active Navigation Link Highlighting
    const currentPage = window.location.pathname.split('/').pop() || 'index.php';
    const navLinks = document.querySelectorAll('.nav-link');

    navLinks.forEach(link => {
      if (link.getAttribute('href') === currentPage) {
        link.classList.add('active');
      }
    });

    // Smooth scroll for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
      anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
          target.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
          });
        }
      });
    });
  </script>
</body>
</html>
