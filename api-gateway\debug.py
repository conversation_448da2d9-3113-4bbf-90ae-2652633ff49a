#!/usr/bin/env python3
"""
Debug Script for ELOH Processing Payment Gateway

This script helps diagnose and fix common issues.
"""

import os
import sys
import subprocess
from pathlib import Path

def print_debug(message, level="INFO"):
    colors = {
        "INFO": "\033[94m",
        "SUCCESS": "\033[92m",
        "WARNING": "\033[93m",
        "ERROR": "\033[91m",
        "RESET": "\033[0m"
    }
    print(f"{colors.get(level, '')}{level}: {message}{colors['RESET']}")

def check_directory():
    """Check if we're in the correct directory"""
    current_dir = Path.cwd()
    print_debug(f"Current directory: {current_dir}")

    # Check for main.py
    if (current_dir / "main.py").exists():
        print_debug("✅ Found main.py - in correct directory", "SUCCESS")
        return True

    # Check if we're one level up
    api_gateway_dir = current_dir / "api-gateway"
    if api_gateway_dir.exists() and (api_gateway_dir / "main.py").exists():
        print_debug("❌ You're in the parent directory!", "ERROR")
        print_debug("💡 Solution: cd api-gateway", "WARNING")
        return False

    print_debug("❌ Cannot find main.py - wrong directory?", "ERROR")
    return False

def check_python_installation():
    """Check Python installation"""
    try:
        version = sys.version_info
        print_debug(f"Python version: {version.major}.{version.minor}.{version.micro}", "INFO")

        if version.major != 3:
            print_debug("❌ Python 3 required!", "ERROR")
            return False

        if version.minor < 11:
            print_debug("⚠️ Python 3.11+ recommended", "WARNING")

        return True
    except Exception as e:
        print_debug(f"❌ Python check failed: {e}", "ERROR")
        return False

def check_requirements_file():
    """Check requirements.txt"""
    req_file = Path("requirements.txt")
    if not req_file.exists():
        print_debug("❌ requirements.txt not found!", "ERROR")
        return False

    print_debug("✅ requirements.txt found", "SUCCESS")

    # Read and display requirements
    try:
        with open(req_file, 'r') as f:
            lines = f.readlines()

        print_debug(f"📦 Found {len(lines)} dependencies:", "INFO")
        for line in lines[:10]:  # Show first 10
            if line.strip() and not line.startswith('#'):
                print_debug(f"  - {line.strip()}", "INFO")

        if len(lines) > 10:
            print_debug(f"  ... and {len(lines) - 10} more", "INFO")

        return True
    except Exception as e:
        print_debug(f"❌ Error reading requirements.txt: {e}", "ERROR")
        return False

def install_dependencies():
    """Install Python dependencies with fallback strategy"""
    print_debug("📦 Installing dependencies...", "INFO")

    try:
        # Upgrade pip first
        subprocess.run([sys.executable, "-m", "pip", "install", "--upgrade", "pip"],
                      check=True, capture_output=True)
        print_debug("✅ pip upgraded", "SUCCESS")

        # Try different requirements files in order of preference
        req_files = ["requirements-debug.txt", "requirements-minimal.txt", "requirements.txt"]

        for req_file in req_files:
            if not Path(req_file).exists():
                continue

            print_debug(f"🔄 Trying {req_file}...", "INFO")
            result = subprocess.run([sys.executable, "-m", "pip", "install", "-r", req_file],
                                   capture_output=True, text=True)

            if result.returncode == 0:
                print_debug(f"✅ Dependencies installed from {req_file}", "SUCCESS")
                return True
            else:
                print_debug(f"⚠️ {req_file} failed, trying next...", "WARNING")
                if "pydantic==3.8.0" in result.stderr:
                    print_debug("🔧 Detected version conflict, creating fixed requirements...", "INFO")
                    create_fixed_requirements()

        print_debug("❌ All requirements files failed", "ERROR")
        return False

    except subprocess.CalledProcessError as e:
        print_debug(f"❌ Installation error: {e}", "ERROR")
        return False
    except FileNotFoundError:
        print_debug("❌ pip not found - Python installation issue?", "ERROR")
        return False

def create_fixed_requirements():
    """Create a fixed requirements file with working versions"""
    print_debug("🔧 Creating fixed requirements...", "INFO")

    fixed_content = """# Fixed requirements for ELOH Processing Payment Gateway
fastapi==0.104.1
uvicorn==0.24.0
pydantic==2.5.0
pydantic-settings==2.1.0
sqlalchemy==2.0.23
psycopg2-binary==2.9.9
cryptography==41.0.7
python-dotenv==1.0.0
httpx==0.25.2
"""

    try:
        with open("requirements-fixed.txt", "w") as f:
            f.write(fixed_content)
        print_debug("✅ Created requirements-fixed.txt", "SUCCESS")

        # Try installing the fixed requirements
        result = subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements-fixed.txt"],
                               capture_output=True, text=True)

        if result.returncode == 0:
            print_debug("✅ Fixed requirements installed successfully", "SUCCESS")
            return True
        else:
            print_debug("❌ Even fixed requirements failed", "ERROR")
            return False

    except Exception as e:
        print_debug(f"❌ Failed to create fixed requirements: {e}", "ERROR")
        return False

def check_environment_variables():
    """Check environment variables"""
    print_debug("🔍 Checking environment variables...", "INFO")

    # Check for .env file
    env_files = [".env", ".env.local", ".env.render"]
    found_env = False

    for env_file in env_files:
        if Path(env_file).exists():
            print_debug(f"✅ Found {env_file}", "SUCCESS")
            found_env = True

    if not found_env:
        print_debug("⚠️ No .env file found", "WARNING")
        print_debug("💡 Copy .env.example to .env and configure", "INFO")

    # Check critical environment variables
    critical_vars = ["SECRET_KEY", "DATABASE_URL"]
    for var in critical_vars:
        if os.getenv(var):
            print_debug(f"✅ {var} is set", "SUCCESS")
        else:
            print_debug(f"⚠️ {var} not set", "WARNING")

def test_basic_imports():
    """Test basic imports"""
    print_debug("🧪 Testing basic imports...", "INFO")

    imports_to_test = [
        ("fastapi", "FastAPI"),
        ("sqlalchemy", "SQLAlchemy"),
        ("pydantic", "Pydantic"),
        ("uvicorn", "Uvicorn")
    ]

    for module, name in imports_to_test:
        try:
            __import__(module)
            print_debug(f"✅ {name} import successful", "SUCCESS")
        except ImportError as e:
            print_debug(f"❌ {name} import failed: {e}", "ERROR")
            return False

    return True

def test_app_imports():
    """Test application imports"""
    print_debug("🧪 Testing app imports...", "INFO")

    # Add current directory to path
    sys.path.insert(0, str(Path.cwd()))

    app_imports = [
        ("app.core.config", "Config"),
        ("app.database.connection", "Database"),
        ("app.core.adapter_registry", "Adapter Registry"),
        ("main", "Main App")
    ]

    for module, name in app_imports:
        try:
            __import__(module)
            print_debug(f"✅ {name} import successful", "SUCCESS")
        except ImportError as e:
            print_debug(f"❌ {name} import failed: {e}", "ERROR")
            print_debug(f"   Module: {module}", "INFO")
            return False

    return True

def create_env_file():
    """Create a basic .env file"""
    if Path(".env").exists():
        print_debug("⚠️ .env file already exists", "WARNING")
        return True

    print_debug("📝 Creating basic .env file...", "INFO")

    env_content = """# ELOH Processing Payment Gateway - Local Development
APP_NAME=ELOH Processing Payment Gateway
ENVIRONMENT=development
DEBUG=true
SECRET_KEY=dev-secret-key-change-in-production
LOG_LEVEL=DEBUG

# Database (SQLite for local development)
DATABASE_URL=sqlite:///./eloh_gateway.db

# Gateway Configuration (set your real values)
# BTCPay Server
# BTCPAY_SERVER_URL=https://your-btcpay-server.com
# BTCPAY_API_KEY=your_api_key
# BTCPAY_STORE_ID=your_store_id

# NowPayments
# NOWPAYMENTS_API_KEY=your_nowpayments_api_key
# NOWPAYMENTS_ENVIRONMENT=sandbox
"""

    try:
        with open(".env", "w") as f:
            f.write(env_content)
        print_debug("✅ .env file created", "SUCCESS")
        print_debug("💡 Edit .env file to add your gateway credentials", "INFO")
        return True
    except Exception as e:
        print_debug(f"❌ Failed to create .env file: {e}", "ERROR")
        return False

def run_database_setup():
    """Run database setup"""
    print_debug("🗄️ Setting up database...", "INFO")

    try:
        result = subprocess.run([sys.executable, "setup_database.py", "--sample-data"],
                               capture_output=True, text=True)

        if result.returncode == 0:
            print_debug("✅ Database setup successful", "SUCCESS")
            print_debug(result.stdout, "INFO")
            return True
        else:
            print_debug("❌ Database setup failed:", "ERROR")
            print_debug(result.stderr, "ERROR")
            return False

    except Exception as e:
        print_debug(f"❌ Database setup error: {e}", "ERROR")
        return False

def main():
    """Main debug function"""
    print("🔧 ELOH Processing Payment Gateway - Debug Script")
    print("=" * 60)

    # Step 1: Check directory
    print_debug("\n📁 Step 1: Checking directory...", "INFO")
    if not check_directory():
        print_debug("💡 Please navigate to the api-gateway directory first", "WARNING")
        print_debug("   Command: cd api-gateway", "INFO")
        return

    # Step 2: Check Python
    print_debug("\n🐍 Step 2: Checking Python...", "INFO")
    if not check_python_installation():
        return

    # Step 3: Check requirements file
    print_debug("\n📋 Step 3: Checking requirements.txt...", "INFO")
    if not check_requirements_file():
        return

    # Step 4: Install dependencies
    print_debug("\n📦 Step 4: Installing dependencies...", "INFO")
    if not install_dependencies():
        print_debug("💡 Try running manually: pip install -r requirements.txt", "WARNING")
        return

    # Step 5: Test basic imports
    print_debug("\n🧪 Step 5: Testing basic imports...", "INFO")
    if not test_basic_imports():
        print_debug("💡 Some dependencies may not be installed correctly", "WARNING")
        return

    # Step 6: Check environment
    print_debug("\n🔍 Step 6: Checking environment...", "INFO")
    check_environment_variables()

    # Step 7: Create .env if needed
    if not Path(".env").exists():
        print_debug("\n📝 Step 7: Creating .env file...", "INFO")
        create_env_file()

    # Step 8: Test app imports
    print_debug("\n🧪 Step 8: Testing app imports...", "INFO")
    if not test_app_imports():
        print_debug("💡 App imports failed - check your code", "WARNING")
        return

    # Step 9: Setup database
    print_debug("\n🗄️ Step 9: Setting up database...", "INFO")
    if not run_database_setup():
        print_debug("💡 Database setup failed - check configuration", "WARNING")

    # Final status
    print_debug("\n🎉 Debug completed!", "SUCCESS")
    print_debug("💡 Next steps:", "INFO")
    print_debug("   1. Run: python test_local.py", "INFO")
    print_debug("   2. Run: uvicorn main:app --reload", "INFO")
    print_debug("   3. Visit: http://localhost:8000/docs", "INFO")

if __name__ == "__main__":
    main()
