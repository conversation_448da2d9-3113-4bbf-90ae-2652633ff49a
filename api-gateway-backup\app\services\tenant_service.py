"""
Tenant Management Service for ELOH Processing Multi-Tenant Payment Gateway

This service handles tenant (customer) management, including tenant creation,
gateway configuration, credential management, and usage tracking.
"""

import uuid
import secrets
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
import logging
from cryptography.fernet import <PERSON><PERSON>t
import json

from ..models.tenant import (
    TenantRequest, TenantResponse, TenantUpdateRequest, TenantStatus, TenantPlan,
    GatewayConfiguration, GatewayConfigurationRequest, TenantUsageStats
)
from ..core.exceptions import PaymentGatewayException
from ..core.config import get_settings

logger = logging.getLogger(__name__)


class TenantService:
    """
    Service for managing tenants in the multi-tenant payment gateway.
    
    This service provides:
    - Tenant creation and management
    - Gateway configuration per tenant
    - Credential encryption and storage
    - Usage tracking and analytics
    - Plan management and limits
    """
    
    def __init__(self):
        self.settings = get_settings()
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        
        # Initialize encryption for credentials
        self.encryption_key = self._get_or_create_encryption_key()
        self.cipher = Fernet(self.encryption_key)
        
        # In-memory storage for demo (replace with database in production)
        self.tenants: Dict[str, Dict[str, Any]] = {}
        self.tenant_api_keys: Dict[str, str] = {}  # api_key -> tenant_id
        
        # Plan configurations
        self.plan_limits = {
            TenantPlan.STARTER: {
                "monthly_volume_limit": 10000.0,
                "transaction_limit": 1000,
                "gateways_limit": 2,
                "webhook_endpoints": 1,
                "api_calls_per_minute": 100
            },
            TenantPlan.PROFESSIONAL: {
                "monthly_volume_limit": 100000.0,
                "transaction_limit": 10000,
                "gateways_limit": 4,
                "webhook_endpoints": 5,
                "api_calls_per_minute": 500
            },
            TenantPlan.ENTERPRISE: {
                "monthly_volume_limit": 1000000.0,
                "transaction_limit": 100000,
                "gateways_limit": 10,
                "webhook_endpoints": 20,
                "api_calls_per_minute": 2000
            },
            TenantPlan.CUSTOM: {
                "monthly_volume_limit": float('inf'),
                "transaction_limit": float('inf'),
                "gateways_limit": float('inf'),
                "webhook_endpoints": float('inf'),
                "api_calls_per_minute": 5000
            }
        }
    
    async def create_tenant(self, tenant_request: TenantRequest) -> TenantResponse:
        """Create a new tenant"""
        try:
            # Generate tenant ID and API key
            tenant_id = f"tenant_{uuid.uuid4().hex[:12]}"
            api_key = f"eloh_{secrets.token_urlsafe(32)}"
            
            # Create tenant record
            tenant_data = {
                "tenant_id": tenant_id,
                "api_key": api_key,
                "company_name": tenant_request.company_name,
                "business_type": tenant_request.business_type,
                "website_url": tenant_request.website_url,
                "contact_email": tenant_request.contact_email,
                "contact_name": tenant_request.contact_name,
                "contact_phone": tenant_request.contact_phone,
                "address": {
                    "line1": tenant_request.address_line1,
                    "line2": tenant_request.address_line2,
                    "city": tenant_request.city,
                    "state": tenant_request.state,
                    "postal_code": tenant_request.postal_code,
                    "country": tenant_request.country
                },
                "plan": tenant_request.plan,
                "status": TenantStatus.ACTIVE,
                "expected_monthly_volume": tenant_request.expected_monthly_volume,
                "preferred_gateways": tenant_request.preferred_gateways,
                "required_currencies": tenant_request.required_currencies,
                "gateways": [],
                "usage_stats": {
                    "total_transactions": 0,
                    "total_volume": 0.0,
                    "monthly_volume": 0.0,
                    "last_reset": datetime.now().isoformat()
                },
                "created_at": datetime.now().isoformat(),
                "updated_at": datetime.now().isoformat(),
                "last_activity": None,
                "metadata": tenant_request.metadata or {}
            }
            
            # Store tenant
            self.tenants[tenant_id] = tenant_data
            self.tenant_api_keys[api_key] = tenant_id
            
            self.logger.info(f"Created tenant: {tenant_id} ({tenant_request.company_name})")
            
            return self._convert_to_tenant_response(tenant_data)
            
        except Exception as e:
            self.logger.error(f"Failed to create tenant: {e}")
            raise PaymentGatewayException(f"Tenant creation failed: {str(e)}")
    
    async def get_tenant(self, tenant_id: str) -> TenantResponse:
        """Get tenant by ID"""
        if tenant_id not in self.tenants:
            raise PaymentGatewayException(f"Tenant not found: {tenant_id}")
        
        tenant_data = self.tenants[tenant_id]
        return self._convert_to_tenant_response(tenant_data)
    
    async def get_tenant_by_api_key(self, api_key: str) -> TenantResponse:
        """Get tenant by API key"""
        if api_key not in self.tenant_api_keys:
            raise PaymentGatewayException("Invalid API key")
        
        tenant_id = self.tenant_api_keys[api_key]
        return await self.get_tenant(tenant_id)
    
    async def update_tenant(self, tenant_id: str, update_request: TenantUpdateRequest) -> TenantResponse:
        """Update tenant information"""
        if tenant_id not in self.tenants:
            raise PaymentGatewayException(f"Tenant not found: {tenant_id}")
        
        tenant_data = self.tenants[tenant_id]
        
        # Update fields
        if update_request.company_name:
            tenant_data["company_name"] = update_request.company_name
        if update_request.business_type:
            tenant_data["business_type"] = update_request.business_type
        if update_request.website_url:
            tenant_data["website_url"] = update_request.website_url
        if update_request.contact_name:
            tenant_data["contact_name"] = update_request.contact_name
        if update_request.contact_phone:
            tenant_data["contact_phone"] = update_request.contact_phone
        if update_request.plan:
            tenant_data["plan"] = update_request.plan
        if update_request.status:
            tenant_data["status"] = update_request.status
        if update_request.metadata:
            tenant_data["metadata"].update(update_request.metadata)
        
        tenant_data["updated_at"] = datetime.now().isoformat()
        
        self.logger.info(f"Updated tenant: {tenant_id}")
        
        return self._convert_to_tenant_response(tenant_data)
    
    async def configure_gateway(self, tenant_id: str, gateway_config: GatewayConfigurationRequest) -> TenantResponse:
        """Configure a payment gateway for a tenant"""
        if tenant_id not in self.tenants:
            raise PaymentGatewayException(f"Tenant not found: {tenant_id}")
        
        tenant_data = self.tenants[tenant_id]
        
        # Check plan limits
        plan_limits = self.plan_limits[TenantPlan(tenant_data["plan"])]
        current_gateways = len(tenant_data["gateways"])
        
        if current_gateways >= plan_limits["gateways_limit"]:
            raise PaymentGatewayException(f"Gateway limit exceeded for plan {tenant_data['plan']}")
        
        # Encrypt credentials
        encrypted_credentials = self._encrypt_credentials(gateway_config.credentials)
        
        # Create gateway configuration
        gateway_data = {
            "gateway_id": gateway_config.gateway_id,
            "enabled": gateway_config.enabled,
            "credentials": encrypted_credentials,
            "configuration": gateway_config.configuration,
            "priority": gateway_config.priority,
            "min_amount": gateway_config.min_amount,
            "max_amount": gateway_config.max_amount,
            "daily_limit": gateway_config.daily_limit,
            "monthly_limit": gateway_config.monthly_limit,
            "supports_refunds": True,  # Default, can be configured
            "supports_recurring": False,  # Default, can be configured
            "supports_webhooks": True,  # Default, can be configured
            "webhook_url": gateway_config.webhook_url,
            "webhook_events": gateway_config.webhook_events,
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat()
        }
        
        # Remove existing configuration for same gateway
        tenant_data["gateways"] = [
            g for g in tenant_data["gateways"] 
            if g["gateway_id"] != gateway_config.gateway_id
        ]
        
        # Add new configuration
        tenant_data["gateways"].append(gateway_data)
        tenant_data["updated_at"] = datetime.now().isoformat()
        
        self.logger.info(f"Configured gateway {gateway_config.gateway_id} for tenant {tenant_id}")
        
        return self._convert_to_tenant_response(tenant_data)
    
    async def get_tenant_gateway_config(self, tenant_id: str, gateway_id: str) -> Optional[Dict[str, Any]]:
        """Get decrypted gateway configuration for a tenant"""
        if tenant_id not in self.tenants:
            return None
        
        tenant_data = self.tenants[tenant_id]
        
        for gateway in tenant_data["gateways"]:
            if gateway["gateway_id"] == gateway_id and gateway["enabled"]:
                # Decrypt credentials
                decrypted_config = gateway.copy()
                decrypted_config["credentials"] = self._decrypt_credentials(gateway["credentials"])
                return decrypted_config
        
        return None
    
    async def record_transaction(self, tenant_id: str, amount: float, currency: str, gateway: str, status: str):
        """Record a transaction for usage tracking"""
        if tenant_id not in self.tenants:
            return
        
        tenant_data = self.tenants[tenant_id]
        usage_stats = tenant_data["usage_stats"]
        
        # Update transaction count
        usage_stats["total_transactions"] += 1
        
        # Update volume (convert to USD for tracking)
        usd_amount = amount  # In production, you'd convert currencies to USD
        usage_stats["total_volume"] += usd_amount
        usage_stats["monthly_volume"] += usd_amount
        
        # Update last activity
        tenant_data["last_activity"] = datetime.now().isoformat()
        
        # Check monthly reset
        last_reset = datetime.fromisoformat(usage_stats["last_reset"])
        if datetime.now().month != last_reset.month:
            usage_stats["monthly_volume"] = usd_amount
            usage_stats["last_reset"] = datetime.now().isoformat()
        
        self.logger.debug(f"Recorded transaction for tenant {tenant_id}: {amount} {currency}")
    
    async def check_limits(self, tenant_id: str, amount: float) -> bool:
        """Check if transaction is within tenant limits"""
        if tenant_id not in self.tenants:
            return False
        
        tenant_data = self.tenants[tenant_id]
        plan_limits = self.plan_limits[TenantPlan(tenant_data["plan"])]
        usage_stats = tenant_data["usage_stats"]
        
        # Check monthly volume limit
        if usage_stats["monthly_volume"] + amount > plan_limits["monthly_volume_limit"]:
            return False
        
        # Check transaction limit
        if usage_stats["total_transactions"] >= plan_limits["transaction_limit"]:
            return False
        
        return True
    
    async def get_tenant_usage_stats(self, tenant_id: str, period_days: int = 30) -> TenantUsageStats:
        """Get usage statistics for a tenant"""
        if tenant_id not in self.tenants:
            raise PaymentGatewayException(f"Tenant not found: {tenant_id}")
        
        tenant_data = self.tenants[tenant_id]
        usage_stats = tenant_data["usage_stats"]
        
        period_start = datetime.now() - timedelta(days=period_days)
        period_end = datetime.now()
        
        return TenantUsageStats(
            tenant_id=tenant_id,
            period_start=period_start,
            period_end=period_end,
            total_transactions=usage_stats["total_transactions"],
            successful_transactions=usage_stats["total_transactions"],  # Simplified
            failed_transactions=0,  # Simplified
            refunded_transactions=0,  # Simplified
            total_volume=usage_stats["total_volume"],
            average_transaction=usage_stats["total_volume"] / max(usage_stats["total_transactions"], 1),
            gateway_stats={},  # Would be populated from detailed tracking
            currency_stats={}  # Would be populated from detailed tracking
        )
    
    # Helper methods
    
    def _get_or_create_encryption_key(self) -> bytes:
        """Get or create encryption key for credentials"""
        # In production, this should be stored securely (e.g., AWS KMS, HashiCorp Vault)
        key = self.settings.secret_key.encode('utf-8')
        # Pad or truncate to 32 bytes for Fernet
        key = key[:32].ljust(32, b'0')
        return Fernet.generate_key()  # For demo, generate new key each time
    
    def _encrypt_credentials(self, credentials: Dict[str, str]) -> str:
        """Encrypt gateway credentials"""
        credentials_json = json.dumps(credentials)
        encrypted = self.cipher.encrypt(credentials_json.encode('utf-8'))
        return encrypted.decode('utf-8')
    
    def _decrypt_credentials(self, encrypted_credentials: str) -> Dict[str, str]:
        """Decrypt gateway credentials"""
        try:
            decrypted = self.cipher.decrypt(encrypted_credentials.encode('utf-8'))
            return json.loads(decrypted.decode('utf-8'))
        except Exception as e:
            self.logger.error(f"Failed to decrypt credentials: {e}")
            return {}
    
    def _convert_to_tenant_response(self, tenant_data: Dict[str, Any]) -> TenantResponse:
        """Convert internal tenant data to TenantResponse"""
        
        # Convert gateway configurations
        gateways = []
        for gateway_data in tenant_data.get("gateways", []):
            gateway_config = GatewayConfiguration(
                gateway_id=gateway_data["gateway_id"],
                enabled=gateway_data["enabled"],
                credentials={"encrypted": "true"},  # Don't expose credentials
                configuration=gateway_data["configuration"],
                priority=gateway_data["priority"],
                min_amount=gateway_data.get("min_amount"),
                max_amount=gateway_data.get("max_amount"),
                daily_limit=gateway_data.get("daily_limit"),
                monthly_limit=gateway_data.get("monthly_limit"),
                supports_refunds=gateway_data.get("supports_refunds", True),
                supports_recurring=gateway_data.get("supports_recurring", False),
                supports_webhooks=gateway_data.get("supports_webhooks", True),
                webhook_url=gateway_data.get("webhook_url"),
                webhook_events=gateway_data.get("webhook_events", [])
            )
            gateways.append(gateway_config)
        
        usage_stats = tenant_data.get("usage_stats", {})
        
        return TenantResponse(
            tenant_id=tenant_data["tenant_id"],
            api_key=tenant_data["api_key"],
            company_name=tenant_data["company_name"],
            business_type=tenant_data["business_type"],
            website_url=tenant_data.get("website_url"),
            contact_email=tenant_data["contact_email"],
            contact_name=tenant_data["contact_name"],
            contact_phone=tenant_data.get("contact_phone"),
            plan=TenantPlan(tenant_data["plan"]),
            status=TenantStatus(tenant_data["status"]),
            gateways=gateways,
            total_transactions=usage_stats.get("total_transactions", 0),
            total_volume=usage_stats.get("total_volume", 0.0),
            monthly_volume=usage_stats.get("monthly_volume", 0.0),
            created_at=datetime.fromisoformat(tenant_data["created_at"]),
            updated_at=datetime.fromisoformat(tenant_data["updated_at"]) if tenant_data.get("updated_at") else None,
            last_activity=datetime.fromisoformat(tenant_data["last_activity"]) if tenant_data.get("last_activity") else None,
            metadata=tenant_data.get("metadata", {})
        )


# Global tenant service instance
_tenant_service = TenantService()


def get_tenant_service() -> TenantService:
    """Get the global tenant service instance"""
    return _tenant_service
