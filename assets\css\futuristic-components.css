/* 🚀 FUTURISTIC COMPONENT LIBRARY - ELOH Processing */

/* ⚡ ENHANCED FORM COMPONENTS */
.form-group {
  margin-bottom: var(--space-lg);
  position: relative;
}

.form-label {
  display: block;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-sm);
  font-size: 0.95rem;
  position: relative;
}

.form-label.required::after {
  content: '*';
  color: #ef4444;
  margin-left: var(--space-xs);
}

.form-input {
  width: 100%;
  padding: var(--space-md) var(--space-lg);
  border: 2px solid var(--border-color);
  border-radius: var(--radius-lg);
  background: var(--surface-color);
  color: var(--text-primary);
  font-size: 1rem;
  transition: all var(--transition-base);
  font-family: var(--font-primary);
  position: relative;
  box-shadow: var(--shadow-sm);
}

.form-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px var(--focus-ring), var(--shadow-md);
  transform: translateY(-2px);
  background: var(--surface-color);
}

.form-input:hover {
  border-color: var(--primary-color);
  box-shadow: var(--shadow-md);
}

.form-input::placeholder {
  color: var(--text-muted);
  font-style: italic;
}

.form-input:invalid {
  border-color: #ef4444;
}

.form-input:valid {
  border-color: #22c55e;
}

/* Floating Label Effect */
.form-group.floating {
  position: relative;
}

.form-group.floating .form-input {
  padding-top: 1.5rem;
  padding-bottom: 0.5rem;
}

.form-group.floating .form-label {
  position: absolute;
  top: 1rem;
  left: 1rem;
  transition: all var(--transition-base);
  pointer-events: none;
  background: var(--surface-color);
  padding: 0 var(--space-xs);
  z-index: 1;
}

.form-group.floating .form-input:focus + .form-label,
.form-group.floating .form-input:not(:placeholder-shown) + .form-label {
  top: -0.5rem;
  left: 0.75rem;
  font-size: 0.75rem;
  color: var(--primary-color);
}

/* Enhanced Select Styling */
.form-select {
  appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%23667eea' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 1rem center;
  background-repeat: no-repeat;
  background-size: 1.25em 1.25em;
  padding-right: 3rem;
  cursor: pointer;
}

.form-select:focus {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%235a67d8' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
}

/* Textarea Styling */
.form-textarea {
  min-height: 120px;
  resize: vertical;
  font-family: var(--font-primary);
  line-height: 1.5;
}

/* Checkbox and Radio Styling */
.form-checkbox,
.form-radio {
  appearance: none;
  width: 1.25rem;
  height: 1.25rem;
  border: 2px solid var(--border-color);
  border-radius: var(--radius-sm);
  background: var(--surface-color);
  cursor: pointer;
  transition: all var(--transition-base);
  position: relative;
  margin-right: var(--space-sm);
}

.form-radio {
  border-radius: 50%;
}

.form-checkbox:checked,
.form-radio:checked {
  background: var(--primary-gradient);
  border-color: var(--primary-color);
}

.form-checkbox:checked::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 0.875rem;
  font-weight: bold;
}

.form-radio:checked::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 0.5rem;
  height: 0.5rem;
  background: white;
  border-radius: 50%;
}

/* Form Validation Styling */
.form-error {
  color: #ef4444;
  font-size: 0.875rem;
  margin-top: var(--space-xs);
  display: flex;
  align-items: center;
  gap: var(--space-xs);
}

.form-error::before {
  content: '⚠️';
}

.form-success {
  color: #22c55e;
  font-size: 0.875rem;
  margin-top: var(--space-xs);
  display: flex;
  align-items: center;
  gap: var(--space-xs);
}

.form-success::before {
  content: '✅';
}

/* Input Group Styling */
.input-group {
  display: flex;
  align-items: stretch;
}

.input-group .form-input {
  border-radius: 0;
  border-right: none;
}

.input-group .form-input:first-child {
  border-radius: var(--radius-lg) 0 0 var(--radius-lg);
}

.input-group .form-input:last-child {
  border-radius: 0 var(--radius-lg) var(--radius-lg) 0;
  border-right: 2px solid var(--border-color);
}

.input-group-addon {
  background: var(--hover-bg);
  border: 2px solid var(--border-color);
  border-left: none;
  padding: var(--space-md);
  display: flex;
  align-items: center;
  color: var(--text-muted);
  font-weight: 500;
  border-radius: 0 var(--radius-lg) var(--radius-lg) 0;
}

/* 🎨 ALERT COMPONENTS */
.alert {
  padding: var(--space-lg);
  border-radius: var(--radius-lg);
  margin-bottom: var(--space-lg);
  border-left: 4px solid;
  position: relative;
  overflow: hidden;
}

.alert::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0.05;
  background: currentColor;
}

.alert-success {
  background: #f0fdf4;
  color: #166534;
  border-color: #22c55e;
}

.alert-warning {
  background: #fffbeb;
  color: #92400e;
  border-color: #f59e0b;
}

.alert-error {
  background: #fef2f2;
  color: #dc2626;
  border-color: #ef4444;
}

.alert-info {
  background: #eff6ff;
  color: #1d4ed8;
  border-color: #3b82f6;
}

/* 🔘 BADGE COMPONENTS */
.badge {
  display: inline-flex;
  align-items: center;
  gap: var(--space-xs);
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-md);
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.badge-primary {
  background: var(--primary-color);
  color: white;
}

.badge-secondary {
  background: var(--text-muted);
  color: white;
}

.badge-success {
  background: #22c55e;
  color: white;
}

.badge-warning {
  background: #f59e0b;
  color: white;
}

.badge-error {
  background: #ef4444;
  color: white;
}

/* 📊 PROGRESS COMPONENTS */
.progress {
  width: 100%;
  height: 8px;
  background: var(--border-light);
  border-radius: var(--radius-md);
  overflow: hidden;
  position: relative;
}

.progress-bar {
  height: 100%;
  background: var(--primary-gradient);
  border-radius: var(--radius-md);
  transition: width var(--transition-slow);
  position: relative;
  overflow: hidden;
}

.progress-bar::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* 🎯 LOADING COMPONENTS */
.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--border-color);
  border-top: 3px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-dots {
  display: inline-flex;
  gap: var(--space-xs);
}

.loading-dot {
  width: 8px;
  height: 8px;
  background: var(--primary-color);
  border-radius: 50%;
  animation: bounce 1.4s ease-in-out infinite both;
}

.loading-dot:nth-child(1) { animation-delay: -0.32s; }
.loading-dot:nth-child(2) { animation-delay: -0.16s; }

@keyframes bounce {
  0%, 80%, 100% { transform: scale(0); }
  40% { transform: scale(1); }
}

/* 🎪 MODAL COMPONENTS */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-base);
}

.modal-overlay.active {
  opacity: 1;
  visibility: visible;
}

.modal {
  background: var(--surface-color);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-xl);
  max-width: 500px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  transform: scale(0.9) translateY(20px);
  transition: transform var(--transition-base);
}

.modal-overlay.active .modal {
  transform: scale(1) translateY(0);
}

.modal-header {
  padding: var(--space-xl);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.modal-title {
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0;
}

.modal-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: var(--text-muted);
  transition: color var(--transition-fast);
}

.modal-close:hover {
  color: var(--text-primary);
}

.modal-body {
  padding: var(--space-xl);
}

.modal-footer {
  padding: var(--space-xl);
  border-top: 1px solid var(--border-color);
  display: flex;
  gap: var(--space-md);
  justify-content: flex-end;
}

/* 🎨 UTILITY CLASSES */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.font-bold { font-weight: 700; }
.font-semibold { font-weight: 600; }
.font-medium { font-weight: 500; }

.text-primary { color: var(--primary-color); }
.text-secondary { color: var(--text-secondary); }
.text-muted { color: var(--text-muted); }

.bg-primary { background: var(--primary-color); }
.bg-surface { background: var(--surface-color); }
.bg-gradient { background: var(--primary-gradient); }

.rounded { border-radius: var(--radius-md); }
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-xl { border-radius: var(--radius-xl); }

.shadow { box-shadow: var(--shadow-sm); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }

.mb-0 { margin-bottom: 0; }
.mb-sm { margin-bottom: var(--space-sm); }
.mb-md { margin-bottom: var(--space-md); }
.mb-lg { margin-bottom: var(--space-lg); }
.mb-xl { margin-bottom: var(--space-xl); }

.mt-0 { margin-top: 0; }
.mt-sm { margin-top: var(--space-sm); }
.mt-md { margin-top: var(--space-md); }
.mt-lg { margin-top: var(--space-lg); }
.mt-xl { margin-top: var(--space-xl); }

.p-sm { padding: var(--space-sm); }
.p-md { padding: var(--space-md); }
.p-lg { padding: var(--space-lg); }
.p-xl { padding: var(--space-xl); }

.flex { display: flex; }
.flex-col { flex-direction: column; }
.items-center { align-items: center; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.gap-sm { gap: var(--space-sm); }
.gap-md { gap: var(--space-md); }
.gap-lg { gap: var(--space-lg); }
