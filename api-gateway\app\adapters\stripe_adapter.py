"""
Stripe Payment Gateway Adapter for ELOH Processing

This adapter implements the GatewayAdapter interface for Stripe payments.
It handles translation between our standard API and Stripe's specific requirements.
"""

import stripe
from typing import Dict, Any, Optional, List
from decimal import Decimal
from datetime import datetime
import logging

from .gateway_adapter import GatewayAdapter, GatewayCredentials
from ..models.payment import PaymentRequest, PaymentResponse, PaymentStatus, RefundRequest, RefundResponse
from ..models.customer import CustomerRequest, CustomerResponse
from ..core.exceptions import PaymentGatewayException

logger = logging.getLogger(__name__)


class StripeCredentials(GatewayCredentials):
    """Stripe-specific credentials"""
    
    def __init__(self, secret_key: str, publishable_key: str, webhook_secret: Optional[str] = None):
        self.secret_key = secret_key
        self.publishable_key = publishable_key
        self.webhook_secret = webhook_secret
    
    def is_valid(self) -> bool:
        """Validate Stripe credentials"""
        return bool(self.secret_key and self.secret_key.startswith('sk_'))
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for secure storage"""
        return {
            "secret_key": self.secret_key,
            "publishable_key": self.publishable_key,
            "webhook_secret": self.webhook_secret
        }


class StripeAdapter(GatewayAdapter):
    """
    Stripe payment gateway adapter.
    
    Handles credit card payments, ACH transfers, and international payment methods
    through Stripe's API. Supports both one-time payments and subscription billing.
    """
    
    def __init__(self, credentials: StripeCredentials, config: Optional[Dict[str, Any]] = None):
        super().__init__(credentials, config)
        
        # Configure Stripe SDK
        stripe.api_key = credentials.secret_key
        stripe.api_version = "2023-10-16"  # Use latest stable API version
        
        # Set up logging
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
    
    def _get_gateway_name(self) -> str:
        """Return gateway name"""
        return "stripe"
    
    async def validate_credentials(self) -> bool:
        """Validate Stripe credentials by making a test API call"""
        try:
            # Test credentials with a simple API call
            await self._make_stripe_call(stripe.Account.retrieve)
            return True
        except Exception as e:
            self.logger.error(f"Stripe credential validation failed: {e}")
            return False
    
    async def process_payment(self, payment_request: PaymentRequest, customer_id: Optional[str] = None) -> PaymentResponse:
        """Process payment through Stripe"""
        try:
            self.logger.info(f"Processing Stripe payment for amount {payment_request.amount} {payment_request.currency}")
            
            # Convert amount to cents (Stripe requirement)
            amount_cents = int(payment_request.amount * 100)
            
            # Prepare Stripe payment intent data
            intent_data = {
                "amount": amount_cents,
                "currency": payment_request.currency.lower(),
                "automatic_payment_methods": {"enabled": True},
                "metadata": {
                    "eloh_reference": payment_request.reference or "",
                    "eloh_description": payment_request.description or "",
                    **(payment_request.metadata or {})
                }
            }
            
            # Add customer if provided
            if customer_id:
                intent_data["customer"] = customer_id
            
            # Add description
            if payment_request.description:
                intent_data["description"] = payment_request.description
            
            # Create Stripe Payment Intent
            payment_intent = await self._make_stripe_call(
                stripe.PaymentIntent.create,
                **intent_data
            )
            
            # Convert Stripe response to our standard format
            return self._convert_stripe_payment_to_response(payment_intent, payment_request)
            
        except stripe.error.StripeError as e:
            self.logger.error(f"Stripe API error: {e}")
            raise PaymentGatewayException(f"Stripe payment failed: {e.user_message or str(e)}")
        except Exception as e:
            self.logger.error(f"Unexpected error processing Stripe payment: {e}")
            raise PaymentGatewayException(f"Payment processing failed: {str(e)}")
    
    async def get_payment_status(self, gateway_payment_id: str) -> PaymentResponse:
        """Get payment status from Stripe"""
        try:
            payment_intent = await self._make_stripe_call(
                stripe.PaymentIntent.retrieve,
                gateway_payment_id
            )
            
            return self._convert_stripe_payment_to_response(payment_intent)
            
        except stripe.error.StripeError as e:
            self.logger.error(f"Stripe API error retrieving payment: {e}")
            raise PaymentGatewayException(f"Failed to retrieve payment: {e.user_message or str(e)}")
    
    async def refund_payment(self, refund_request: RefundRequest, gateway_payment_id: str) -> RefundResponse:
        """Process refund through Stripe"""
        try:
            # Get the original payment intent
            payment_intent = await self._make_stripe_call(
                stripe.PaymentIntent.retrieve,
                gateway_payment_id
            )
            
            # Prepare refund data
            refund_data = {
                "payment_intent": gateway_payment_id,
                "metadata": {
                    "eloh_payment_id": refund_request.payment_id,
                    "reason": refund_request.reason or "",
                    **(refund_request.metadata or {})
                }
            }
            
            # Add amount if partial refund
            if refund_request.amount:
                refund_data["amount"] = int(refund_request.amount * 100)
            
            # Create refund
            refund = await self._make_stripe_call(
                stripe.Refund.create,
                **refund_data
            )
            
            return self._convert_stripe_refund_to_response(refund, refund_request)
            
        except stripe.error.StripeError as e:
            self.logger.error(f"Stripe refund error: {e}")
            raise PaymentGatewayException(f"Refund failed: {e.user_message or str(e)}")
    
    async def create_customer(self, customer_request: CustomerRequest) -> CustomerResponse:
        """Create customer in Stripe"""
        try:
            customer_data = {
                "email": customer_request.email,
                "metadata": customer_request.metadata or {}
            }
            
            # Add optional fields
            if customer_request.first_name or customer_request.last_name:
                name_parts = [customer_request.first_name or "", customer_request.last_name or ""]
                customer_data["name"] = " ".join(name_parts).strip()
            
            if customer_request.phone:
                customer_data["phone"] = customer_request.phone
            
            if customer_request.address:
                customer_data["address"] = {
                    "line1": customer_request.address.line1,
                    "line2": customer_request.address.line2,
                    "city": customer_request.address.city,
                    "state": customer_request.address.state,
                    "postal_code": customer_request.address.postal_code,
                    "country": customer_request.address.country
                }
            
            # Create Stripe customer
            customer = await self._make_stripe_call(
                stripe.Customer.create,
                **customer_data
            )
            
            return self._convert_stripe_customer_to_response(customer, customer_request)
            
        except stripe.error.StripeError as e:
            self.logger.error(f"Stripe customer creation error: {e}")
            raise PaymentGatewayException(f"Customer creation failed: {e.user_message or str(e)}")
    
    async def get_customer(self, gateway_customer_id: str) -> CustomerResponse:
        """Get customer from Stripe"""
        try:
            customer = await self._make_stripe_call(
                stripe.Customer.retrieve,
                gateway_customer_id
            )
            
            return self._convert_stripe_customer_to_response(customer)
            
        except stripe.error.StripeError as e:
            self.logger.error(f"Stripe customer retrieval error: {e}")
            raise PaymentGatewayException(f"Customer retrieval failed: {e.user_message or str(e)}")
    
    async def process_webhook(self, webhook_data: Dict[str, Any], signature: Optional[str] = None) -> Dict[str, Any]:
        """Process Stripe webhook"""
        try:
            # Verify webhook signature if secret is available
            if self.credentials.webhook_secret and signature:
                event = stripe.Webhook.construct_event(
                    webhook_data,
                    signature,
                    self.credentials.webhook_secret
                )
            else:
                event = webhook_data
            
            # Convert to standard format
            return {
                "gateway": "stripe",
                "event_type": event.get("type"),
                "event_id": event.get("id"),
                "created": datetime.fromtimestamp(event.get("created", 0)),
                "data": event.get("data", {}),
                "raw_event": event
            }
            
        except stripe.error.SignatureVerificationError as e:
            self.logger.error(f"Stripe webhook signature verification failed: {e}")
            raise PaymentGatewayException("Invalid webhook signature")
        except Exception as e:
            self.logger.error(f"Stripe webhook processing error: {e}")
            raise PaymentGatewayException(f"Webhook processing failed: {str(e)}")
    
    async def get_supported_currencies(self) -> List[str]:
        """Get Stripe supported currencies"""
        # Stripe supports 135+ currencies - returning most common ones
        return [
            "USD", "EUR", "GBP", "CAD", "AUD", "JPY", "CHF", "SEK", "NOK", "DKK",
            "PLN", "CZK", "HUF", "BGN", "RON", "HRK", "ISK", "MXN", "BRL", "SGD",
            "HKD", "NZD", "KRW", "INR", "MYR", "THB", "PHP", "IDR", "VND"
        ]
    
    async def get_supported_payment_methods(self) -> List[str]:
        """Get Stripe supported payment methods"""
        return [
            "card", "bank_transfer", "digital_wallet", "buy_now_pay_later",
            "apple_pay", "google_pay", "link", "klarna", "afterpay"
        ]
    
    # Helper methods
    
    async def _make_stripe_call(self, func, *args, **kwargs):
        """Make async Stripe API call with error handling"""
        try:
            # Note: Stripe Python SDK doesn't have native async support
            # In production, you might want to use aiohttp or run_in_executor
            return func(*args, **kwargs)
        except Exception as e:
            self.logger.error(f"Stripe API call failed: {e}")
            raise
    
    def _convert_stripe_payment_to_response(self, payment_intent, original_request: Optional[PaymentRequest] = None) -> PaymentResponse:
        """Convert Stripe PaymentIntent to our standard PaymentResponse"""
        
        # Map Stripe status to our standard status
        status_mapping = {
            "requires_payment_method": PaymentStatus.PENDING,
            "requires_confirmation": PaymentStatus.PENDING,
            "requires_action": PaymentStatus.PENDING,
            "processing": PaymentStatus.PROCESSING,
            "succeeded": PaymentStatus.COMPLETED,
            "canceled": PaymentStatus.CANCELLED
        }
        
        return PaymentResponse(
            payment_id=f"stripe_{payment_intent.id}",
            gateway_payment_id=payment_intent.id,
            reference=payment_intent.metadata.get("eloh_reference"),
            amount=Decimal(payment_intent.amount) / 100,
            currency=payment_intent.currency.upper(),
            status=status_mapping.get(payment_intent.status, PaymentStatus.PENDING),
            gateway_used="stripe",
            checkout_url=payment_intent.get("next_action", {}).get("redirect_to_url", {}).get("url"),
            created_at=datetime.fromtimestamp(payment_intent.created),
            gateway_response=payment_intent.to_dict(),
            metadata=payment_intent.metadata
        )
    
    def _convert_stripe_refund_to_response(self, refund, original_request: RefundRequest) -> RefundResponse:
        """Convert Stripe Refund to our standard RefundResponse"""
        
        status_mapping = {
            "pending": PaymentStatus.PROCESSING,
            "succeeded": PaymentStatus.REFUNDED,
            "failed": PaymentStatus.FAILED,
            "canceled": PaymentStatus.CANCELLED
        }
        
        return RefundResponse(
            refund_id=f"stripe_{refund.id}",
            payment_id=original_request.payment_id,
            amount=Decimal(refund.amount) / 100,
            status=status_mapping.get(refund.status, PaymentStatus.PROCESSING),
            gateway_used="stripe",
            created_at=datetime.fromtimestamp(refund.created),
            gateway_response=refund.to_dict()
        )
    
    def _convert_stripe_customer_to_response(self, customer, original_request: Optional[CustomerRequest] = None) -> CustomerResponse:
        """Convert Stripe Customer to our standard CustomerResponse"""
        
        # Parse name if available
        first_name = None
        last_name = None
        if customer.name:
            name_parts = customer.name.split(" ", 1)
            first_name = name_parts[0] if name_parts else None
            last_name = name_parts[1] if len(name_parts) > 1 else None
        
        return CustomerResponse(
            customer_id=f"stripe_{customer.id}",
            gateway_customer_ids={"stripe": customer.id},
            email=customer.email,
            first_name=first_name,
            last_name=last_name,
            phone=customer.phone,
            created_at=datetime.fromtimestamp(customer.created),
            metadata=customer.metadata
        )
