<?php
include "header.php";
?>

<main>
  <section class="hero">
    <h1>Simple Payment Gateway Test</h1>
  </section>

  <section class="section">
    <div style="max-width: 600px; margin: 0 auto;">
      <div style="background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin-bottom: 20px;">
        <h3>🚀 Quick Payment Gateway Test</h3>
        <p>Direct links to test the dynamic payment gateway selection functionality.</p>
      </div>

      <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin-bottom: 20px;">
        <h3>🧪 Test Scenarios:</h3>
        
        <div style="margin-bottom: 20px;">
          <h4>1. Service Payments (Auto-populated amounts):</h4>
          <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px;">
            <a href="multi-gateway-payment-form.php?type=service&service=consulting&amount=150" 
               class="cta-button" style="text-decoration: none; text-align: center; padding: 10px;">
              Consulting $150
            </a>
            <a href="multi-gateway-payment-form.php?type=service&service=mining-pool&amount=200" 
               class="cta-button" style="text-decoration: none; text-align: center; padding: 10px;">
              Mining Pool $200
            </a>
            <a href="multi-gateway-payment-form.php?type=service&service=mining-services&amount=500" 
               class="cta-button" style="text-decoration: none; text-align: center; padding: 10px;">
              Mining Services $500
            </a>
            <a href="multi-gateway-payment-form.php?type=service&service=analysis&amount=99" 
               class="cta-button" style="text-decoration: none; text-align: center; padding: 10px;">
              Analysis $99
            </a>
          </div>
        </div>

        <div style="margin-bottom: 20px;">
          <h4>2. Donation Payments (Preset amounts):</h4>
          <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 10px;">
            <a href="multi-gateway-payment-form.php?type=donation&amount=100" 
               class="cta-button" style="text-decoration: none; text-align: center; padding: 10px; background: #28a745;">
              $100 Donation
            </a>
            <a href="multi-gateway-payment-form.php?type=donation&amount=1000" 
               class="cta-button" style="text-decoration: none; text-align: center; padding: 10px; background: #28a745;">
              $1,000 Donation
            </a>
            <a href="multi-gateway-payment-form.php?type=donation&amount=5000" 
               class="cta-button" style="text-decoration: none; text-align: center; padding: 10px; background: #28a745;">
              $5,000 Donation
            </a>
          </div>
        </div>

        <div style="margin-bottom: 20px;">
          <h4>3. Custom Amounts:</h4>
          <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px;">
            <a href="multi-gateway-payment-form.php?type=service" 
               class="cta-button" style="text-decoration: none; text-align: center; padding: 10px; background: #6c757d;">
              Custom Service Payment
            </a>
            <a href="multi-gateway-payment-form.php?type=donation" 
               class="cta-button" style="text-decoration: none; text-align: center; padding: 10px; background: #6c757d;">
              Custom Donation
            </a>
          </div>
        </div>
      </div>

      <div style="background: #fff3cd; color: #856404; padding: 15px; border-radius: 5px; margin-bottom: 20px;">
        <h3>✅ What to Test:</h3>
        <ol>
          <li><strong>Gateway Selection:</strong> Click between BTCPay Server and NowPayments</li>
          <li><strong>Visual Feedback:</strong> Look for blue highlighting and checkmarks</li>
          <li><strong>Currency Updates:</strong> Watch the dropdown change options</li>
          <li><strong>Auto-population:</strong> Verify amounts and services are pre-filled</li>
          <li><strong>Mobile Testing:</strong> Test on mobile devices</li>
        </ol>
      </div>

      <div style="background: #e2e3e5; color: #383d41; padding: 15px; border-radius: 5px; margin-bottom: 20px;">
        <h3>🎯 Expected Behavior:</h3>
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
          <div>
            <h4>⚡ BTCPay Server:</h4>
            <ul style="font-size: 0.9em;">
              <li>Pre-selected by default</li>
              <li>Blue border and background</li>
              <li>Checkmark in corner</li>
              <li>Auto-selects Bitcoin</li>
              <li>Shows "Lightning & On-chain"</li>
            </ul>
          </div>
          <div>
            <h4>🌐 NowPayments:</h4>
            <ul style="font-size: 0.9em;">
              <li>Click to select</li>
              <li>Blue border and background</li>
              <li>Checkmark appears</li>
              <li>Shows 20+ cryptocurrencies</li>
              <li>Dropdown updates instantly</li>
            </ul>
          </div>
        </div>
      </div>

      <div style="text-align: center; margin: 30px 0;">
        <a href="multi-gateway-payment-form.php" class="cta-button" style="text-decoration: none; padding: 15px 30px; font-size: 1.1em;">
          🚀 Open Full Payment Form
        </a>
      </div>

      <div style="text-align: center; margin: 20px 0;">
        <a href="test-payment-selection.php" style="color: #0077cc; text-decoration: none;">← Back to Advanced Test Page</a> |
        <a href="index.php" style="color: #0077cc; text-decoration: none;">Homepage</a>
      </div>
    </div>
  </section>
</main>

<script>
console.log('Simple payment test page loaded');

// Add click tracking for testing
document.addEventListener('click', function(e) {
  if (e.target.tagName === 'A' && e.target.href.includes('multi-gateway-payment-form.php')) {
    console.log('Payment form link clicked:', e.target.href);
    
    // Optional: Add visual feedback
    e.target.style.transform = 'scale(0.95)';
    setTimeout(() => {
      e.target.style.transform = 'scale(1)';
    }, 150);
  }
});
</script>

<?php include "footer.php"; ?>
