#!/usr/bin/env python3
"""
Simple test script for ELOH Processing Payment Gateway API

This script demonstrates the basic functionality of the payment gateway API
including payment creation, status checking, and gateway listing.
"""

import asyncio
import json
from decimal import Decimal
from datetime import datetime

# Import the core components
from app.models.payment import PaymentRequest, Currency, PaymentMethod
from app.core.routing import UserGatewayConfig, RoutingStrategy, get_payment_router
from app.core.adapter_registry import get_adapter_registry
from app.core.config import get_settings
from app.core.logging import setup_logging, get_logger

# Setup logging
setup_logging()
logger = get_logger(__name__)


async def test_adapter_registry():
    """Test the adapter registry functionality"""
    print("🔧 Testing Adapter Registry...")
    
    registry = get_adapter_registry()
    
    # List available gateways
    gateways = registry.list_gateways()
    print(f"Available gateways: {gateways}")
    
    # Get gateway information
    for gateway in gateways:
        info = registry.get_gateway_info(gateway)
        print(f"\n{gateway.upper()} Gateway:")
        print(f"  Name: {info['name']}")
        print(f"  Description: {info['description']}")
        print(f"  Supported currencies: {info['supported_currencies']}")
        print(f"  Supported methods: {info['supported_methods']}")
        print(f"  AI routing weight: {info['ai_routing_weight']}")
    
    # Test gateway filtering
    usd_gateways = registry.get_gateways_by_currency("USD")
    print(f"\nGateways supporting USD: {usd_gateways}")
    
    btc_gateways = registry.get_gateways_by_currency("BTC")
    print(f"Gateways supporting BTC: {btc_gateways}")
    
    card_gateways = registry.get_gateways_by_method("card")
    print(f"Gateways supporting cards: {card_gateways}")


async def test_routing_system():
    """Test the rule-based routing system"""
    print("\n🧠 Testing Routing System...")
    
    # Create test user configuration
    user_config = UserGatewayConfig(
        user_id="test_user",
        enabled_gateways=["stripe", "btcpay"],
        preferred_gateway=None,  # Let routing decide
        currency_preferences={
            "USD": "stripe",
            "BTC": "btcpay"
        },
        routing_strategy=RoutingStrategy.RULE_BASED
    )
    
    router = get_payment_router()
    
    # Test different payment scenarios
    test_payments = [
        {
            "amount": Decimal("100.00"),
            "currency": Currency.USD,
            "description": "USD payment test"
        },
        {
            "amount": Decimal("0.001"),
            "currency": Currency.BTC,
            "description": "Bitcoin payment test"
        },
        {
            "amount": Decimal("50.00"),
            "currency": Currency.EUR,
            "description": "EUR payment test"
        },
        {
            "amount": Decimal("10000.00"),
            "currency": Currency.USD,
            "description": "Large USD payment test"
        }
    ]
    
    for payment_data in test_payments:
        payment_request = PaymentRequest(
            amount=payment_data["amount"],
            currency=payment_data["currency"],
            description=payment_data["description"],
            email="<EMAIL>"
        )
        
        try:
            selected_gateway = await router.route_payment(payment_request, user_config)
            print(f"\n💳 Payment: {payment_data['description']}")
            print(f"   Amount: {payment_data['amount']} {payment_data['currency']}")
            print(f"   Selected Gateway: {selected_gateway}")
        except Exception as e:
            print(f"\n❌ Routing failed for {payment_data['description']}: {e}")


async def test_gateway_adapters():
    """Test gateway adapter functionality (mock mode)"""
    print("\n🔌 Testing Gateway Adapters...")
    
    settings = get_settings()
    registry = get_adapter_registry()
    
    # Test each configured gateway
    for gateway in ["stripe", "btcpay"]:
        if not settings.is_gateway_configured(gateway):
            print(f"\n⚠️  {gateway.upper()} not configured, skipping...")
            continue
        
        print(f"\n🔧 Testing {gateway.upper()} adapter...")
        
        try:
            # Get adapter class
            adapter_class = registry.get_adapter_class(gateway)
            print(f"   Adapter class: {adapter_class.__name__}")
            
            # Get gateway info
            info = registry.get_gateway_info(gateway)
            print(f"   Supported currencies: {info['supported_currencies']}")
            print(f"   Supported methods: {info['supported_methods']}")
            
            # Note: We're not creating actual adapter instances here
            # because that would require valid credentials
            print(f"   ✅ {gateway.upper()} adapter ready")
            
        except Exception as e:
            print(f"   ❌ {gateway.upper()} adapter error: {e}")


async def test_payment_models():
    """Test payment model validation"""
    print("\n📋 Testing Payment Models...")
    
    # Test valid payment request
    try:
        payment_request = PaymentRequest(
            amount=Decimal("100.00"),
            currency=Currency.USD,
            email="<EMAIL>",
            description="Test payment",
            payment_method=PaymentMethod.CARD
        )
        print("✅ Valid payment request created")
        print(f"   Amount: {payment_request.amount}")
        print(f"   Currency: {payment_request.currency}")
        print(f"   Email: {payment_request.email}")
    except Exception as e:
        print(f"❌ Payment request validation failed: {e}")
    
    # Test invalid payment request
    try:
        invalid_payment = PaymentRequest(
            amount=Decimal("-10.00"),  # Invalid negative amount
            currency=Currency.USD,
            email="invalid-email"  # Invalid email format
        )
        print("❌ Invalid payment request should have failed")
    except Exception as e:
        print(f"✅ Invalid payment request correctly rejected: {e}")


async def test_configuration():
    """Test configuration management"""
    print("\n⚙️  Testing Configuration...")
    
    settings = get_settings()
    
    print(f"App name: {settings.app_name}")
    print(f"Environment: {settings.environment}")
    print(f"Debug mode: {settings.debug}")
    print(f"Log level: {settings.log_level}")
    
    # Test gateway configuration
    configured_gateways = settings.get_configured_gateways()
    print(f"Configured gateways: {configured_gateways}")
    
    for gateway in ["stripe", "btcpay", "square", "nowpayments"]:
        is_configured = settings.is_gateway_configured(gateway)
        status = "✅ Configured" if is_configured else "❌ Not configured"
        print(f"   {gateway.upper()}: {status}")


async def main():
    """Run all tests"""
    print("🚀 ELOH Processing Payment Gateway API Test Suite")
    print("=" * 60)
    
    try:
        await test_configuration()
        await test_adapter_registry()
        await test_routing_system()
        await test_gateway_adapters()
        await test_payment_models()
        
        print("\n" + "=" * 60)
        print("✅ All tests completed successfully!")
        print("\n🎯 Next Steps:")
        print("1. Configure your payment gateway credentials in .env")
        print("2. Start the API server: uvicorn main:app --reload")
        print("3. Visit http://localhost:8000/docs for interactive API documentation")
        print("4. Test real payments with your configured gateways")
        
    except Exception as e:
        print(f"\n❌ Test suite failed: {e}")
        logger.exception("Test suite error")


if __name__ == "__main__":
    asyncio.run(main())
