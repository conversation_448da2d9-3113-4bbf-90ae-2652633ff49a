# 🔴 ELOH Processing LIVE Production Widget

## 🚨 **LIVE PAYMENT PROCESSING**

This widget processes **REAL PAYMENTS** through live cryptocurrency gateways. Use with caution and ensure proper testing.

## ✨ Key Features

- **🔴 LIVE PAYMENTS** - Processes real cryptocurrency transactions
- **🌐 Static Site Compatible** - Works on TinyHost, GitHub Pages, any static hosting
- **📱 Cross-Platform** - Web, mobile apps, desktop applications
- **⚡ BTCPay Server** - Direct Bitcoin and Lightning Network payments
- **🌍 NowPayments** - 300+ cryptocurrencies supported
- **🔒 Secure** - No server-side sessions required

## 🚀 Quick Integration

### 1. Static Sites (TinyHost, GitHub Pages, etc.)

```html
<iframe 
  src="https://elohprocessing.infy.uk/widget/live-production-widget.html?amount=100.00&email=<EMAIL>" 
  width="400" 
  height="600" 
  frameborder="0"
  style="border-radius: 12px; box-shadow: 0 4px 20px rgba(0,0,0,0.1);">
</iframe>
```

### 2. Mobile Apps (React Native)

```javascript
import { WebView } from 'react-native-webview';

<WebView
  source={{ 
    uri: 'https://elohprocessing.infy.uk/widget/live-production-widget.html?amount=100.00' 
  }}
  style={{ flex: 1 }}
  onMessage={(event) => {
    const data = JSON.parse(event.nativeEvent.data);
    if (data.type === 'eloh_widget_success') {
      // Handle LIVE payment success
      console.log('Payment completed:', data.payload);
    }
  }}
/>
```

### 3. Desktop Apps (Electron)

```javascript
const { BrowserView } = require('electron');

const view = new BrowserView();
mainWindow.setBrowserView(view);
view.setBounds({ x: 0, y: 0, width: 400, height: 600 });
view.webContents.loadURL('https://elohprocessing.infy.uk/widget/live-production-widget.html?amount=100.00');
```

### 4. WordPress/PHP Sites

```php
<iframe 
  src="https://elohprocessing.infy.uk/widget/live-production-widget.html?amount=<?php echo $amount; ?>&email=<?php echo urlencode($email); ?>" 
  width="400" 
  height="600" 
  frameborder="0">
</iframe>
```

## 🔧 Configuration Parameters

| Parameter | Type | Description | Example |
|-----------|------|-------------|---------|
| `amount` | number | Payment amount in USD | `100.00` |
| `email` | string | Customer email address | `<EMAIL>` |
| `description` | string | Payment description | `Product Purchase` |
| `gateway` | string | Preferred gateway | `btcpay`, `nowpayments` |

## 🔴 Live Payment Gateways

### BTCPay Server
- **Currency**: Bitcoin (BTC)
- **Features**: Lightning Network, Self-hosted
- **Processing**: Instant confirmation
- **Fees**: Network fees only

### NowPayments
- **Currencies**: 300+ cryptocurrencies
- **Features**: Multi-currency, Global support
- **Processing**: Automatic conversion
- **Fees**: 0.5% + network fees

## 🔒 Security Features

- **No Sessions** - Stateless operation
- **CORS Protected** - Cross-origin security
- **Input Validation** - All inputs sanitized
- **API Authentication** - Secure gateway connections
- **Real-time Verification** - Payment status tracking

## 📱 Platform Examples

### iOS (Swift)
```swift
import WebKit

let webView = WKWebView()
let url = URL(string: "https://elohprocessing.infy.uk/widget/live-production-widget.html?amount=100.00")!
webView.load(URLRequest(url: url))
```

### Android (Java/Kotlin)
```java
WebView webView = findViewById(R.id.webview);
webView.getSettings().setJavaScriptEnabled(true);
webView.loadUrl("https://elohprocessing.infy.uk/widget/live-production-widget.html?amount=100.00");
```

### Flutter
```dart
import 'package:webview_flutter/webview_flutter.dart';

WebView(
  initialUrl: 'https://elohprocessing.infy.uk/widget/live-production-widget.html?amount=100.00',
  javascriptMode: JavascriptMode.unrestricted,
)
```

## 🔔 Event Handling

The widget sends messages to the parent window:

```javascript
window.addEventListener('message', function(event) {
  if (event.data.type === 'eloh_widget_success') {
    // LIVE payment completed successfully
    const payment = event.data.payload;
    console.log('Payment successful:', payment);
    
    // Handle success (redirect, show confirmation, etc.)
    window.location.href = '/payment-success';
  }
  
  if (event.data.type === 'eloh_widget_resize') {
    // Auto-resize iframe
    iframe.style.height = event.data.height + 'px';
  }
});
```

## 🚨 Important Notes

### ⚠️ LIVE PAYMENT WARNING
- This widget processes **REAL MONEY**
- Test thoroughly before deployment
- Ensure proper error handling
- Monitor payment confirmations

### 💰 Payment Flow
1. Customer fills payment form
2. Widget validates inputs
3. API creates live invoice/payment
4. Customer redirected to gateway
5. Payment processed on blockchain
6. Confirmation sent via webhook

### 🔧 Required Setup
1. **BTCPay Server** - Configure API keys in `/config/btcpay-config.json`
2. **NowPayments** - Configure API keys in `/config/nowpayments-config.json`
3. **Webhooks** - Set up payment confirmation endpoints
4. **SSL Certificate** - HTTPS required for live payments

## 📊 Payment Tracking

Payments are stored in `/widget/payments/` with this structure:

```json
{
  "order_id": "WIDGET_1234567890_abc123",
  "amount": 100.00,
  "email": "<EMAIL>",
  "gateway": "btcpay",
  "status": "pending",
  "created_at": 1640995200,
  "payment_data": {
    "invoice_id": "...",
    "checkout_link": "..."
  }
}
```

## 🔗 API Endpoints

- **BTCPay**: `https://elohprocessing.infy.uk/api/btcpay-create.php`
- **NowPayments**: `https://elohprocessing.infy.uk/api/nowpayments-create.php`

## 🆘 Support & Monitoring

### Error Handling
- All API errors are logged
- User-friendly error messages
- Automatic retry mechanisms

### Monitoring
- Payment creation logs
- Gateway response tracking
- Error rate monitoring

### Support
- **Email**: <EMAIL>
- **Documentation**: https://elohprocessing.infy.uk/widget/
- **Status**: https://status.elohprocessing.com

## 🚀 Deployment Checklist

### Pre-Production
- [ ] Test with small amounts
- [ ] Verify webhook delivery
- [ ] Check error handling
- [ ] Test on target platforms

### Production
- [ ] Configure live API keys
- [ ] Set up monitoring
- [ ] Enable logging
- [ ] Test payment flow

### Post-Deployment
- [ ] Monitor payment success rates
- [ ] Check webhook delivery
- [ ] Review error logs
- [ ] Customer feedback

---

## ⚠️ **FINAL WARNING**

**THIS WIDGET PROCESSES LIVE CRYPTOCURRENCY PAYMENTS**

- Ensure proper testing before deployment
- Monitor all transactions
- Have customer support ready
- Keep API keys secure

**Ready for live cryptocurrency payment processing!** 🚀💰
