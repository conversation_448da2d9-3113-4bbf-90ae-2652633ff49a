<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ELOH Processing - Live Payment Widget</title>
    <style>
        :root {
            --widget-primary: #667eea;
            --widget-accent: #764ba2;
            --widget-radius: 12px;
            --widget-font: Inter, sans-serif;
        }
        
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        
        body {
            font-family: var(--widget-font);
            background: transparent;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .widget-container {
            background: white;
            border-radius: var(--widget-radius);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            padding: 24px;
            width: 100%;
            max-width: 400px;
            border: 1px solid #e2e8f0;
        }
        
        .widget-header {
            text-align: center;
            margin-bottom: 24px;
        }
        
        .widget-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #1a202c;
            margin-bottom: 8px;
        }
        
        .widget-description {
            color: #718096;
            font-size: 0.9rem;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-label {
            display: block;
            font-weight: 500;
            color: #4a5568;
            margin-bottom: 8px;
            font-size: 0.9rem;
        }
        
        .form-input, .form-select {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e2e8f0;
            border-radius: var(--widget-radius);
            font-size: 1rem;
            transition: all 0.2s ease;
            background: white;
        }
        
        .form-input:focus, .form-select:focus {
            outline: none;
            border-color: var(--widget-primary);
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .gateway-selector {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 12px;
            margin-bottom: 20px;
        }
        
        .gateway-option {
            position: relative;
        }
        
        .gateway-option input[type="radio"] {
            position: absolute;
            opacity: 0;
        }
        
        .gateway-option label {
            display: block;
            padding: 16px 12px;
            border: 2px solid #e2e8f0;
            border-radius: var(--widget-radius);
            text-align: center;
            cursor: pointer;
            transition: all 0.2s ease;
            background: white;
        }
        
        .gateway-option input[type="radio"]:checked + label {
            border-color: var(--widget-primary);
            background: rgba(102, 126, 234, 0.05);
        }
        
        .gateway-icon {
            font-size: 1.5rem;
            margin-bottom: 8px;
        }
        
        .gateway-name {
            font-size: 0.85rem;
            font-weight: 500;
            color: #4a5568;
        }
        
        .amount-input-group {
            position: relative;
        }
        
        .currency-symbol {
            position: absolute;
            left: 16px;
            top: 50%;
            transform: translateY(-50%);
            color: #718096;
            font-weight: 500;
        }
        
        .amount-input-group .form-input {
            padding-left: 40px;
        }
        
        .pay-button {
            width: 100%;
            background: linear-gradient(135deg, var(--widget-primary) 0%, var(--widget-accent) 100%);
            color: white;
            border: none;
            padding: 16px 24px;
            border-radius: var(--widget-radius);
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            margin-top: 8px;
        }
        
        .pay-button:hover {
            transform: translateY(-1px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }
        
        .pay-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .powered-by {
            text-align: center;
            margin-top: 20px;
            font-size: 0.8rem;
            color: #a0aec0;
        }
        
        .powered-by a {
            color: var(--widget-primary);
            text-decoration: none;
        }
        
        .error-message {
            background: #fed7d7;
            color: #c53030;
            padding: 12px 16px;
            border-radius: var(--widget-radius);
            margin-bottom: 20px;
            font-size: 0.9rem;
        }
        
        .success-message {
            background: #f0fdf4;
            color: #166534;
            padding: 12px 16px;
            border-radius: var(--widget-radius);
            margin-bottom: 20px;
            font-size: 0.9rem;
        }
        
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }
        
        .loading-spinner {
            width: 32px;
            height: 32px;
            border: 3px solid #e2e8f0;
            border-top: 3px solid var(--widget-primary);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 12px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .live-badge {
            background: linear-gradient(135deg, #ef4444, #dc2626);
            color: white;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            margin-left: 8px;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }
        
        /* Dark theme support */
        @media (prefers-color-scheme: dark) {
            body {
                background: #1a1a2e;
            }
            
            .widget-container {
                background: #2d3748;
                border-color: #4a5568;
                color: #f7fafc;
            }
            
            .widget-title {
                color: #f7fafc;
            }
            
            .form-input,
            .form-select,
            .gateway-option label {
                background: #4a5568;
                border-color: #718096;
                color: #f7fafc;
            }
        }
        
        /* Responsive */
        @media (max-width: 480px) {
            body {
                padding: 12px;
            }
            
            .widget-container {
                padding: 20px;
            }
            
            .gateway-selector {
                grid-template-columns: 1fr;
            }
        }
    </style>
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="widget-container">
        <div class="widget-header">
            <h1 class="widget-title">
                Complete Your Payment
                <span class="live-badge">LIVE</span>
            </h1>
            <p class="widget-description">Secure cryptocurrency payment processing</p>
        </div>
        
        <div id="message-container"></div>
        
        <form id="widget-payment-form">
            <div class="form-group">
                <label class="form-label">Payment Method</label>
                <div class="gateway-selector">
                    <div class="gateway-option">
                        <input type="radio" name="gateway" value="btcpay" id="gateway_btcpay" checked>
                        <label for="gateway_btcpay">
                            <div class="gateway-icon">⚡</div>
                            <div class="gateway-name">BTCPay Server</div>
                        </label>
                    </div>
                    <div class="gateway-option">
                        <input type="radio" name="gateway" value="nowpayments" id="gateway_nowpayments">
                        <label for="gateway_nowpayments">
                            <div class="gateway-icon">🌐</div>
                            <div class="gateway-name">NowPayments</div>
                        </label>
                    </div>
                </div>
            </div>
            
            <div class="form-group">
                <label for="amount" class="form-label">Amount</label>
                <div class="amount-input-group">
                    <span class="currency-symbol">$</span>
                    <input type="number" id="amount" name="amount" 
                           min="5" max="10000" step="0.01" required class="form-input"
                           placeholder="0.00">
                </div>
            </div>
            
            <div class="form-group">
                <label for="email" class="form-label">Email Address</label>
                <input type="email" id="email" name="email" required class="form-input"
                       placeholder="<EMAIL>">
            </div>
            
            <div class="form-group">
                <label for="description" class="form-label">Description</label>
                <input type="text" id="description" name="description" class="form-input"
                       placeholder="Payment description">
            </div>
            
            <button type="submit" class="pay-button" id="pay-button">
                Pay Now - Live Processing
            </button>
        </form>
        
        <div class="loading" id="loading">
            <div class="loading-spinner"></div>
            <div>Creating live payment...</div>
        </div>
        
        <div class="powered-by">
            Powered by <a href="https://elohprocessing.infy.uk" target="_blank">ELOH Processing</a>
        </div>
    </div>
    
    <script>
        // Configuration
        const WIDGET_CONFIG = {
            apiEndpoint: 'https://elohprocessing.infy.uk/api/create-payment.php',
            btcpayEndpoint: 'https://elohprocessing.infy.uk/api/btcpay-create.php',
            nowpaymentsEndpoint: 'https://elohprocessing.infy.uk/api/nowpayments-create.php'
        };
        
        // Get URL parameters for configuration
        const urlParams = new URLSearchParams(window.location.search);
        const amount = urlParams.get('amount') || '';
        const email = urlParams.get('email') || '';
        const description = urlParams.get('description') || '';
        const gateway = urlParams.get('gateway') || 'btcpay';
        
        // Apply URL parameters to form
        document.addEventListener('DOMContentLoaded', function() {
            if (amount) document.getElementById('amount').value = amount;
            if (email) document.getElementById('email').value = email;
            if (description) document.getElementById('description').value = description;
            
            // Set gateway
            const gatewayRadio = document.getElementById('gateway_' + gateway);
            if (gatewayRadio) gatewayRadio.checked = true;
        });
        
        // Form handling - LIVE PAYMENT PROCESSING
        document.getElementById('widget-payment-form').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const payButton = document.getElementById('pay-button');
            const loading = document.getElementById('loading');
            const messageContainer = document.getElementById('message-container');
            
            // Show loading state
            payButton.style.display = 'none';
            loading.style.display = 'block';
            messageContainer.innerHTML = '';
            
            // Get form values
            const amount = formData.get('amount');
            const email = formData.get('email');
            const gateway = formData.get('gateway');
            const description = formData.get('description');
            
            // Validate inputs
            if (!amount || parseFloat(amount) < 5) {
                showMessage('Minimum amount is $5.00', 'error');
                resetForm();
                return;
            }
            
            if (parseFloat(amount) > 10000) {
                showMessage('Maximum amount is $10,000.00', 'error');
                resetForm();
                return;
            }
            
            if (!email || !email.includes('@')) {
                showMessage('Valid email address is required', 'error');
                resetForm();
                return;
            }
            
            // Create live payment
            createLivePayment(gateway, amount, email, description);
        });
        
        function createLivePayment(gateway, amount, email, description) {
            const orderId = 'WIDGET_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
            
            // Prepare payment data
            const paymentData = {
                gateway: gateway,
                amount: parseFloat(amount),
                email: email,
                description: description || 'Widget Payment',
                order_id: orderId,
                currency: 'USD'
            };
            
            // Choose endpoint based on gateway
            let endpoint;
            switch (gateway) {
                case 'btcpay':
                    endpoint = WIDGET_CONFIG.btcpayEndpoint;
                    break;
                case 'nowpayments':
                    endpoint = WIDGET_CONFIG.nowpaymentsEndpoint;
                    break;
                default:
                    endpoint = WIDGET_CONFIG.apiEndpoint;
            }
            
            // Make API call to create live payment
            fetch(endpoint, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                },
                body: JSON.stringify(paymentData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Redirect to payment gateway
                    if (data.payment_url || data.checkout_link) {
                        window.top.location.href = data.payment_url || data.checkout_link;
                    } else {
                        showMessage('✅ Payment created successfully! Redirecting to payment gateway...', 'success');
                        
                        // Send success message to parent
                        if (window.parent && window.parent !== window) {
                            window.parent.postMessage({
                                type: 'eloh_widget_success',
                                payload: {
                                    order_id: orderId,
                                    amount: parseFloat(amount),
                                    gateway: gateway,
                                    email: email,
                                    description: description,
                                    payment_data: data
                                }
                            }, '*');
                        }
                        
                        // Auto redirect after 3 seconds if no URL provided
                        setTimeout(() => {
                            if (data.invoice_id) {
                                window.top.location.href = `https://elohprocessing.infy.uk/payment-redirect.php?invoice=${data.invoice_id}&gateway=${gateway}`;
                            }
                        }, 3000);
                    }
                } else {
                    showMessage('❌ Payment creation failed: ' + (data.error || 'Unknown error'), 'error');
                    resetForm();
                }
            })
            .catch(error => {
                console.error('Payment API error:', error);
                showMessage('❌ Network error. Please check your connection and try again.', 'error');
                resetForm();
            });
        }
        
        function showMessage(message, type) {
            const messageContainer = document.getElementById('message-container');
            const className = type === 'error' ? 'error-message' : 'success-message';
            messageContainer.innerHTML = `<div class="${className}">${message}</div>`;
            sendResize();
        }
        
        function resetForm() {
            document.getElementById('pay-button').style.display = 'block';
            document.getElementById('loading').style.display = 'none';
        }
        
        // Auto-resize functionality for iframe
        function sendResize() {
            if (window.parent && window.parent !== window) {
                const height = document.body.scrollHeight;
                window.parent.postMessage({
                    type: 'eloh_widget_resize',
                    height: height
                }, '*');
            }
        }
        
        // Send initial size
        setTimeout(sendResize, 100);
        
        // Send size on changes
        window.addEventListener('resize', sendResize);
        document.addEventListener('change', () => setTimeout(sendResize, 100));
        document.addEventListener('input', () => setTimeout(sendResize, 100));
        
        // Log widget initialization
        console.log('ELOH Processing Live Widget initialized');
        console.log('Gateway endpoints configured:', WIDGET_CONFIG);
    </script>
</body>
</html>
