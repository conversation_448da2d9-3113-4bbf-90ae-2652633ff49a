"""
Tenant management endpoints for ELOH Processing Multi-Tenant Payment Gateway API

This module implements tenant management endpoints for ELOH Processing's
payment gateway service, allowing management of customer accounts and their
gateway configurations.
"""

from fastapi import APIRouter, Depends, HTTPException, Header, BackgroundTasks
from typing import List, Optional
import logging

from ...models.tenant import (
    TenantRequest, TenantResponse, TenantUpdateRequest, 
    GatewayConfigurationRequest, TenantUsageStats
)
from ...services.tenant_service import get_tenant_service, TenantService
from ...core.exceptions import PaymentGatewayException, create_http_exception
from ...core.logging import get_logger

router = APIRouter()
logger = get_logger(__name__)


# Authentication dependency for tenant API
async def get_current_tenant(
    authorization: Optional[str] = Header(None),
    x_api_key: Optional[str] = Header(None)
) -> TenantResponse:
    """
    Get current tenant from API key authentication.
    
    Supports both Authorization header and X-API-Key header formats.
    """
    tenant_service = get_tenant_service()
    
    # Extract API key from headers
    api_key = None
    if authorization and authorization.startswith("Bearer "):
        api_key = authorization[7:]  # Remove "Bearer " prefix
    elif x_api_key:
        api_key = x_api_key
    
    if not api_key:
        raise HTTPException(
            status_code=401,
            detail={"error": {"code": "MISSING_API_KEY", "message": "API key required"}}
        )
    
    try:
        tenant = await tenant_service.get_tenant_by_api_key(api_key)
        return tenant
    except PaymentGatewayException:
        raise HTTPException(
            status_code=401,
            detail={"error": {"code": "INVALID_API_KEY", "message": "Invalid API key"}}
        )


# Admin authentication (for ELOH Processing internal use)
async def get_admin_user(
    authorization: Optional[str] = Header(None)
) -> dict:
    """
    Admin authentication for tenant management.
    
    In production, this would integrate with your admin authentication system.
    """
    if not authorization or not authorization.startswith("Admin "):
        raise HTTPException(
            status_code=401,
            detail={"error": {"code": "ADMIN_AUTH_REQUIRED", "message": "Admin authentication required"}}
        )
    
    # Simplified admin auth - in production, validate admin token
    admin_token = authorization[6:]  # Remove "Admin " prefix
    if admin_token != "eloh_admin_token_123":  # Replace with real admin auth
        raise HTTPException(
            status_code=401,
            detail={"error": {"code": "INVALID_ADMIN_TOKEN", "message": "Invalid admin token"}}
        )
    
    return {"admin_id": "admin", "permissions": ["tenant_management"]}


@router.post("/tenants", response_model=TenantResponse)
async def create_tenant(
    tenant_request: TenantRequest,
    background_tasks: BackgroundTasks,
    admin_user: dict = Depends(get_admin_user)
) -> TenantResponse:
    """
    Create a new tenant account.
    
    This endpoint is for ELOH Processing administrators to onboard
    new customers who want to use our payment gateway services.
    
    **Admin Authentication Required**
    """
    try:
        logger.info(f"Creating new tenant: {tenant_request.company_name}")
        
        tenant_service = get_tenant_service()
        tenant = await tenant_service.create_tenant(tenant_request)
        
        # Schedule background tasks
        background_tasks.add_task(
            send_welcome_email,
            tenant.contact_email,
            tenant.company_name,
            tenant.api_key
        )
        
        logger.info(f"Tenant created successfully: {tenant.tenant_id}")
        return tenant
        
    except PaymentGatewayException as e:
        logger.error(f"Tenant creation failed: {e.message}")
        raise create_http_exception(e)
    except Exception as e:
        logger.error(f"Unexpected error creating tenant: {e}")
        raise HTTPException(
            status_code=500,
            detail={"error": {"code": "INTERNAL_ERROR", "message": "Internal server error"}}
        )


@router.get("/tenants/me", response_model=TenantResponse)
async def get_current_tenant_info(
    current_tenant: TenantResponse = Depends(get_current_tenant)
) -> TenantResponse:
    """
    Get current tenant information.
    
    Returns information about the authenticated tenant including
    configured gateways, usage statistics, and account details.
    """
    logger.info(f"Retrieving tenant info: {current_tenant.tenant_id}")
    return current_tenant


@router.put("/tenants/me", response_model=TenantResponse)
async def update_current_tenant(
    update_request: TenantUpdateRequest,
    current_tenant: TenantResponse = Depends(get_current_tenant)
) -> TenantResponse:
    """
    Update current tenant information.
    
    Allows tenants to update their company information, contact details,
    and other account settings.
    """
    try:
        logger.info(f"Updating tenant: {current_tenant.tenant_id}")
        
        tenant_service = get_tenant_service()
        updated_tenant = await tenant_service.update_tenant(
            current_tenant.tenant_id, 
            update_request
        )
        
        logger.info(f"Tenant updated successfully: {current_tenant.tenant_id}")
        return updated_tenant
        
    except PaymentGatewayException as e:
        logger.error(f"Tenant update failed: {e.message}")
        raise create_http_exception(e)
    except Exception as e:
        logger.error(f"Unexpected error updating tenant: {e}")
        raise HTTPException(
            status_code=500,
            detail={"error": {"code": "INTERNAL_ERROR", "message": "Internal server error"}}
        )


@router.post("/tenants/me/gateways", response_model=TenantResponse)
async def configure_gateway(
    gateway_config: GatewayConfigurationRequest,
    current_tenant: TenantResponse = Depends(get_current_tenant)
) -> TenantResponse:
    """
    Configure a payment gateway for the current tenant.
    
    This endpoint allows tenants to add or update payment gateway
    configurations including credentials, limits, and webhook settings.
    
    **Supported Gateways:**
    - `stripe`: Stripe payment processing
    - `square`: Square payment processing  
    - `btcpay`: BTCPay Server for Bitcoin/Lightning
    - `nowpayments`: NowPayments for 300+ cryptocurrencies
    """
    try:
        logger.info(f"Configuring gateway {gateway_config.gateway_id} for tenant {current_tenant.tenant_id}")
        
        tenant_service = get_tenant_service()
        updated_tenant = await tenant_service.configure_gateway(
            current_tenant.tenant_id,
            gateway_config
        )
        
        logger.info(f"Gateway {gateway_config.gateway_id} configured successfully")
        return updated_tenant
        
    except PaymentGatewayException as e:
        logger.error(f"Gateway configuration failed: {e.message}")
        raise create_http_exception(e)
    except Exception as e:
        logger.error(f"Unexpected error configuring gateway: {e}")
        raise HTTPException(
            status_code=500,
            detail={"error": {"code": "INTERNAL_ERROR", "message": "Internal server error"}}
        )


@router.get("/tenants/me/usage", response_model=TenantUsageStats)
async def get_tenant_usage_stats(
    period_days: int = 30,
    current_tenant: TenantResponse = Depends(get_current_tenant)
) -> TenantUsageStats:
    """
    Get usage statistics for the current tenant.
    
    Returns transaction volume, counts, and other usage metrics
    for the specified period.
    
    **Parameters:**
    - `period_days`: Number of days to include in statistics (default: 30)
    """
    try:
        logger.info(f"Retrieving usage stats for tenant {current_tenant.tenant_id}")
        
        tenant_service = get_tenant_service()
        usage_stats = await tenant_service.get_tenant_usage_stats(
            current_tenant.tenant_id,
            period_days
        )
        
        return usage_stats
        
    except PaymentGatewayException as e:
        logger.error(f"Failed to retrieve usage stats: {e.message}")
        raise create_http_exception(e)
    except Exception as e:
        logger.error(f"Unexpected error retrieving usage stats: {e}")
        raise HTTPException(
            status_code=500,
            detail={"error": {"code": "INTERNAL_ERROR", "message": "Internal server error"}}
        )


@router.get("/tenants/{tenant_id}", response_model=TenantResponse)
async def get_tenant_by_id(
    tenant_id: str,
    admin_user: dict = Depends(get_admin_user)
) -> TenantResponse:
    """
    Get tenant by ID (Admin only).
    
    Allows ELOH Processing administrators to view any tenant's information.
    
    **Admin Authentication Required**
    """
    try:
        logger.info(f"Admin retrieving tenant: {tenant_id}")
        
        tenant_service = get_tenant_service()
        tenant = await tenant_service.get_tenant(tenant_id)
        
        return tenant
        
    except PaymentGatewayException as e:
        logger.error(f"Failed to retrieve tenant: {e.message}")
        raise create_http_exception(e)
    except Exception as e:
        logger.error(f"Unexpected error retrieving tenant: {e}")
        raise HTTPException(
            status_code=500,
            detail={"error": {"code": "INTERNAL_ERROR", "message": "Internal server error"}}
        )


@router.put("/tenants/{tenant_id}", response_model=TenantResponse)
async def update_tenant_by_id(
    tenant_id: str,
    update_request: TenantUpdateRequest,
    admin_user: dict = Depends(get_admin_user)
) -> TenantResponse:
    """
    Update tenant by ID (Admin only).
    
    Allows ELOH Processing administrators to update any tenant's information,
    including status changes and plan modifications.
    
    **Admin Authentication Required**
    """
    try:
        logger.info(f"Admin updating tenant: {tenant_id}")
        
        tenant_service = get_tenant_service()
        updated_tenant = await tenant_service.update_tenant(tenant_id, update_request)
        
        logger.info(f"Tenant updated by admin: {tenant_id}")
        return updated_tenant
        
    except PaymentGatewayException as e:
        logger.error(f"Admin tenant update failed: {e.message}")
        raise create_http_exception(e)
    except Exception as e:
        logger.error(f"Unexpected error in admin tenant update: {e}")
        raise HTTPException(
            status_code=500,
            detail={"error": {"code": "INTERNAL_ERROR", "message": "Internal server error"}}
        )


@router.get("/tenants/{tenant_id}/usage", response_model=TenantUsageStats)
async def get_tenant_usage_stats_by_id(
    tenant_id: str,
    period_days: int = 30,
    admin_user: dict = Depends(get_admin_user)
) -> TenantUsageStats:
    """
    Get usage statistics for any tenant (Admin only).
    
    **Admin Authentication Required**
    """
    try:
        logger.info(f"Admin retrieving usage stats for tenant: {tenant_id}")
        
        tenant_service = get_tenant_service()
        usage_stats = await tenant_service.get_tenant_usage_stats(tenant_id, period_days)
        
        return usage_stats
        
    except PaymentGatewayException as e:
        logger.error(f"Failed to retrieve tenant usage stats: {e.message}")
        raise create_http_exception(e)
    except Exception as e:
        logger.error(f"Unexpected error retrieving tenant usage stats: {e}")
        raise HTTPException(
            status_code=500,
            detail={"error": {"code": "INTERNAL_ERROR", "message": "Internal server error"}}
        )


# Background task functions

async def send_welcome_email(email: str, company_name: str, api_key: str):
    """
    Send welcome email to new tenant.
    
    In production, this would integrate with your email service
    to send onboarding information and API documentation.
    """
    logger.info(f"Sending welcome email to {email} for {company_name}")
    
    # Placeholder for email sending logic
    # In production, you would:
    # 1. Use an email service (SendGrid, AWS SES, etc.)
    # 2. Send API documentation
    # 3. Provide onboarding resources
    # 4. Include API key securely
    
    logger.info(f"Welcome email sent to {email}")


async def notify_gateway_configuration(tenant_id: str, gateway_id: str):
    """
    Notify relevant systems about gateway configuration changes.
    
    This could include updating monitoring, analytics, or other systems.
    """
    logger.info(f"Gateway {gateway_id} configured for tenant {tenant_id}")
    
    # Placeholder for notification logic
    # In production, you might:
    # 1. Update monitoring dashboards
    # 2. Configure alerting rules
    # 3. Update analytics tracking
    # 4. Notify support team
