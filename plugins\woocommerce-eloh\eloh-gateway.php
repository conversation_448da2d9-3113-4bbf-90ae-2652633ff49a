<?php
/**
 * Plugin Name: ELOH Crypto Payment Gateway
 * Description: Accept BTC, ETH, and TRX payments via ELOH Processing
 * Version: 1.0.0
 */

if (!defined('ABSPATH')) {
    exit;
}

add_action('plugins_loaded', 'init_eloh_gateway');

function init_eloh_gateway() {
    class WC_Gateway_ELOH extends WC_Payment_Gateway {
        public function __construct() {
            $this->id = 'eloh';
            $this->method_title = 'ELOH Crypto Payments';
            $this->method_description = 'Accept cryptocurrency payments via ELOH Processing';
            
            // Setup form fields for the admin
            $this->init_form_fields();
            $this->init_settings();
            
            $this->title = $this->get_option('title');
            $this->description = $this->get_option('description');
            $this->api_key = $this->get_option('api_key');
            $this->enabled_currencies = array(
                'btc' => $this->get_option('enable_btc'),
                'eth' => $this->get_option('enable_eth'),
                'trx' => $this->get_option('enable_trx')
            );
            
            // Actions
            add_action('woocommerce_update_options_payment_gateways_' . $this->id, array($this, 'process_admin_options'));
            add_action('woocommerce_api_eloh_callback', array($this, 'handle_webhook'));
        }
        
        // Process payment method
        public function process_payment($order_id) {
            // Implementation to create invoice via ELOH API
            // and redirect customer to payment page
        }
        
        // Handle webhook from ELOH
        public function handle_webhook() {
            // Implementation to verify and process webhook
        }
    }
    
    // Add to WooCommerce payment gateways