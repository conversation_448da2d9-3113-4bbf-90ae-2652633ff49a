<?php
/**
 * Square Webhook Handler
 * Handles Square payment notifications and updates
 */

// Set content type for JSON response
header('Content-Type: application/json');

try {
    // Validate request method
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        http_response_code(405);
        echo json_encode(['error' => 'Method not allowed']);
        exit;
    }

    // Get webhook data
    $body = file_get_contents('php://input');
    $signature = $_SERVER['HTTP_X_SQUARE_SIGNATURE'] ?? '';
    $url = 'https://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'];

    // Log webhook received
    error_log("Square webhook received: " . substr($body, 0, 200) . "...");

    // Initialize Square Gateway
    require_once 'includes/square-gateway.php';
    $squareGateway = new Square_Gateway();

    // Validate webhook signature
    if (!$squareGateway->validateWebhook($body, $signature, $url)) {
        error_log("Square webhook signature validation failed");
        http_response_code(401);
        echo json_encode(['error' => 'Invalid signature']);
        exit;
    }

    // Parse webhook data
    $webhookData = json_decode($body, true);
    if (!$webhookData) {
        error_log("Square webhook invalid JSON: " . $body);
        http_response_code(400);
        echo json_encode(['error' => 'Invalid JSON']);
        exit;
    }

    // Get event type and data
    $eventType = $webhookData['type'] ?? '';
    $eventData = $webhookData['data'] ?? [];

    error_log("Square webhook event type: $eventType");

    // Handle different event types
    switch ($eventType) {
        case 'payment.created':
            handlePaymentCreated($eventData);
            break;

        case 'payment.updated':
            handlePaymentUpdated($eventData);
            break;

        case 'refund.created':
            handleRefundCreated($eventData);
            break;

        case 'refund.updated':
            handleRefundUpdated($eventData);
            break;

        default:
            error_log("Square webhook unhandled event type: $eventType");
            // Still return success to avoid retries
            break;
    }

    // Return success response
    http_response_code(200);
    echo json_encode(['status' => 'success']);

} catch (Exception $e) {
    error_log("Square webhook error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'Internal server error']);
}

/**
 * Handle payment created event
 */
function handlePaymentCreated($eventData) {
    try {
        $payment = $eventData['object']['payment'] ?? null;
        if (!$payment) {
            error_log("Square webhook payment.created: No payment data");
            return;
        }

        $paymentId = $payment['id'];
        $status = $payment['status'];
        $amount = $payment['amount_money']['amount'] / 100; // Convert from cents
        $currency = $payment['amount_money']['currency'];

        error_log("Square payment created: $paymentId, Status: $status, Amount: $amount $currency");

        // Update payment status in database (if you have one)
        // updatePaymentStatus($paymentId, $status, $amount, $currency);

        // Send confirmation email if payment is completed
        if ($status === 'COMPLETED') {
            // sendPaymentConfirmationEmail($paymentId, $amount, $currency);
            error_log("Square payment completed: $paymentId");
        }

    } catch (Exception $e) {
        error_log("Error handling payment.created: " . $e->getMessage());
    }
}

/**
 * Handle payment updated event
 */
function handlePaymentUpdated($eventData) {
    try {
        $payment = $eventData['object']['payment'] ?? null;
        if (!$payment) {
            error_log("Square webhook payment.updated: No payment data");
            return;
        }

        $paymentId = $payment['id'];
        $status = $payment['status'];
        $amount = $payment['amount_money']['amount'] / 100;
        $currency = $payment['amount_money']['currency'];

        error_log("Square payment updated: $paymentId, Status: $status, Amount: $amount $currency");

        // Update payment status in database
        // updatePaymentStatus($paymentId, $status, $amount, $currency);

        // Handle different status updates
        switch ($status) {
            case 'COMPLETED':
                error_log("Square payment completed: $paymentId");
                // Send confirmation email
                // sendPaymentConfirmationEmail($paymentId, $amount, $currency);
                break;

            case 'FAILED':
                error_log("Square payment failed: $paymentId");
                // Send failure notification
                // sendPaymentFailureEmail($paymentId, $amount, $currency);
                break;

            case 'CANCELED':
                error_log("Square payment canceled: $paymentId");
                // Handle cancellation
                // handlePaymentCancellation($paymentId);
                break;
        }

    } catch (Exception $e) {
        error_log("Error handling payment.updated: " . $e->getMessage());
    }
}

/**
 * Handle refund created event
 */
function handleRefundCreated($eventData) {
    try {
        $refund = $eventData['object']['refund'] ?? null;
        if (!$refund) {
            error_log("Square webhook refund.created: No refund data");
            return;
        }

        $refundId = $refund['id'];
        $paymentId = $refund['payment_id'];
        $status = $refund['status'];
        $amount = $refund['amount_money']['amount'] / 100;
        $currency = $refund['amount_money']['currency'];

        error_log("Square refund created: $refundId for payment $paymentId, Status: $status, Amount: $amount $currency");

        // Update refund status in database
        // updateRefundStatus($refundId, $paymentId, $status, $amount, $currency);

    } catch (Exception $e) {
        error_log("Error handling refund.created: " . $e->getMessage());
    }
}

/**
 * Handle refund updated event
 */
function handleRefundUpdated($eventData) {
    try {
        $refund = $eventData['object']['refund'] ?? null;
        if (!$refund) {
            error_log("Square webhook refund.updated: No refund data");
            return;
        }

        $refundId = $refund['id'];
        $paymentId = $refund['payment_id'];
        $status = $refund['status'];
        $amount = $refund['amount_money']['amount'] / 100;
        $currency = $refund['amount_money']['currency'];

        error_log("Square refund updated: $refundId for payment $paymentId, Status: $status, Amount: $amount $currency");

        // Update refund status in database
        // updateRefundStatus($refundId, $paymentId, $status, $amount, $currency);

        // Handle different refund statuses
        switch ($status) {
            case 'COMPLETED':
                error_log("Square refund completed: $refundId");
                // Send refund confirmation email
                // sendRefundConfirmationEmail($refundId, $paymentId, $amount, $currency);
                break;

            case 'FAILED':
                error_log("Square refund failed: $refundId");
                // Handle refund failure
                // handleRefundFailure($refundId, $paymentId);
                break;
        }

    } catch (Exception $e) {
        error_log("Error handling refund.updated: " . $e->getMessage());
    }
}

/**
 * Update payment status in database
 * (This would be implemented with your database)
 */
function updatePaymentStatus($paymentId, $status, $amount, $currency) {
    // Database update implementation would go here
    error_log("Would update payment status: $paymentId -> $status");
}

/**
 * Update refund status in database
 * (This would be implemented with your database)
 */
function updateRefundStatus($refundId, $paymentId, $status, $amount, $currency) {
    // Database update implementation would go here
    error_log("Would update refund status: $refundId -> $status");
}
?>
