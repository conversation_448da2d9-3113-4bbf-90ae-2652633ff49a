"""
Webhook handling API endpoints
"""

from fastapi import APIRouter, Request, HTTPException
from typing import Dict, Any
import logging

router = APIRouter()
logger = logging.getLogger(__name__)


@router.post("/webhooks/stripe")
async def stripe_webhook(request: Request):
    """Handle Stripe webhooks"""
    try:
        payload = await request.body()
        # Basic webhook handling
        logger.info("Stripe webhook received")
        return {"status": "received"}
    except Exception as e:
        logger.error(f"Stripe webhook error: {e}")
        raise HTTPException(status_code=400, detail="Webhook processing failed")


@router.post("/webhooks/btcpay")
async def btcpay_webhook(request: Request):
    """Handle BTCPay Server webhooks"""
    try:
        payload = await request.body()
        logger.info("BTCPay webhook received")
        return {"status": "received"}
    except Exception as e:
        logger.error(f"BTCPay webhook error: {e}")
        raise HTTPException(status_code=400, detail="Webhook processing failed")


@router.post("/webhooks/nowpayments")
async def nowpayments_webhook(request: Request):
    """Handle NowPayments webhooks"""
    try:
        payload = await request.body()
        logger.info("NowPayments webhook received")
        return {"status": "received"}
    except Exception as e:
        logger.error(f"NowPayments webhook error: {e}")
        raise HTTPException(status_code=400, detail="Webhook processing failed")
