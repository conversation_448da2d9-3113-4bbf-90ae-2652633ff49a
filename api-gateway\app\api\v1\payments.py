"""
Payment endpoints for ELOH Processing Payment Gateway API

This module implements the payment-related API endpoints that provide
a unified interface for processing payments across multiple gateways.
"""

from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks, Header
from typing import Optional
import logging
from datetime import datetime

from ...models.payment import (
    PaymentRequest, PaymentResponse, RefundRequest, RefundResponse
)
from ...models.tenant import TenantResponse
from ...core.routing import get_payment_router, UserGatewayConfig, RoutingStrategy
from ...core.adapter_registry import get_adapter_registry
from ...core.exceptions import PaymentGatewayException, create_http_exception
from ...core.config import get_settings
from ...core.logging import log_payment_event, log_routing_decision

router = APIRouter()
logger = logging.getLogger(__name__)


# Multi-tenant authentication dependency
async def get_current_tenant(
    authorization: Optional[str] = Header(None),
    x_api_key: Optional[str] = Header(None)
) -> TenantResponse:
    """Get current tenant from API key authentication"""
    from ...services.tenant_service import get_tenant_service
    from ...models.tenant import TenantResponse

    tenant_service = get_tenant_service()

    # Extract API key from headers
    api_key = None
    if authorization and authorization.startswith("Bearer "):
        api_key = authorization[7:]  # Remove "Bearer " prefix
    elif x_api_key:
        api_key = x_api_key

    if not api_key:
        raise HTTPException(
            status_code=401,
            detail={"error": {"code": "MISSING_API_KEY", "message": "API key required"}}
        )

    try:
        tenant = await tenant_service.get_tenant_by_api_key(api_key)
        return tenant
    except PaymentGatewayException:
        raise HTTPException(
            status_code=401,
            detail={"error": {"code": "INVALID_API_KEY", "message": "Invalid API key"}}
        )


# Dependency to get tenant gateway configuration
async def get_tenant_gateway_config(
    current_tenant: TenantResponse = Depends(get_current_tenant)
) -> UserGatewayConfig:
    """
    Get tenant-specific gateway configuration.

    Converts tenant gateway configurations to UserGatewayConfig format
    for compatibility with the existing routing system.
    """
    from ...services.tenant_service import get_tenant_service

    tenant_service = get_tenant_service()

    # Get enabled gateways from tenant configuration
    enabled_gateways = []
    gateway_priority = []
    currency_preferences = {}

    for gateway_config in current_tenant.gateways:
        if gateway_config.enabled:
            enabled_gateways.append(gateway_config.gateway_id)

            # Build priority list based on gateway priority
            gateway_priority.append((gateway_config.gateway_id, gateway_config.priority))

    # Sort by priority (lower number = higher priority)
    gateway_priority.sort(key=lambda x: x[1])
    gateway_priority = [gw[0] for gw in gateway_priority]

    # Set default currency preferences based on gateway capabilities
    if "btcpay" in enabled_gateways:
        currency_preferences["BTC"] = "btcpay"
    if "nowpayments" in enabled_gateways:
        currency_preferences["ETH"] = "nowpayments"
        currency_preferences["LTC"] = "nowpayments"
    if "stripe" in enabled_gateways:
        currency_preferences["USD"] = "stripe"
        currency_preferences["EUR"] = "stripe"
    if "square" in enabled_gateways and "USD" not in currency_preferences:
        currency_preferences["USD"] = "square"

    return UserGatewayConfig(
        user_id=current_tenant.tenant_id,
        enabled_gateways=enabled_gateways,
        preferred_gateway=gateway_priority[0] if gateway_priority else None,
        gateway_priority=gateway_priority if len(gateway_priority) > 1 else None,
        currency_preferences=currency_preferences,
        routing_strategy=RoutingStrategy.RULE_BASED
    )


@router.post("/payments", response_model=PaymentResponse)
async def create_payment(
    payment_request: PaymentRequest,
    background_tasks: BackgroundTasks,
    user_config: UserGatewayConfig = Depends(get_tenant_gateway_config)
) -> PaymentResponse:
    """
    Create a new payment using intelligent gateway routing.

    This endpoint demonstrates the core functionality of the consolidated
    payment gateway API:

    1. Receives a standard payment request
    2. Uses rule-based routing to select the best gateway
    3. Creates the payment using the selected gateway adapter
    4. Returns a standardized response

    The routing system considers:
    - User preferences and enabled gateways
    - Payment amount and currency
    - Gateway availability and capabilities
    - Business rules and constraints
    """
    try:
        logger.info(f"Processing payment request: {payment_request.amount} {payment_request.currency}")

        # Step 1: Route the payment to the best gateway
        router_instance = get_payment_router()
        selected_gateway = await router_instance.route_payment(payment_request, user_config)

        # Log routing decision
        registry = get_adapter_registry()
        available_gateways = user_config.enabled_gateways

        log_routing_decision(
            logger,
            payment_id=f"pending_{int(datetime.now().timestamp())}",
            selected_gateway=selected_gateway,
            available_gateways=available_gateways,
            routing_strategy=user_config.routing_strategy.value,
            decision_factors={
                "currency": payment_request.currency,
                "amount": float(payment_request.amount),
                "preferred_gateway": payment_request.preferred_gateway,
                "user_preferred": user_config.preferred_gateway
            }
        )

        # Step 2: Get the adapter class and credentials
        adapter_class = registry.get_adapter_class(selected_gateway)
        settings = get_settings()
        gateway_credentials = settings.get_gateway_credentials(selected_gateway)

        if not gateway_credentials:
            raise PaymentGatewayException(
                f"No credentials configured for gateway: {selected_gateway}",
                error_code="GATEWAY_NOT_CONFIGURED",
                gateway=selected_gateway
            )

        # Step 3: Create and configure the adapter
        if selected_gateway == "stripe":
            from ...adapters.stripe_adapter import StripeCredentials
            credentials = StripeCredentials(
                secret_key=gateway_credentials["secret_key"],
                publishable_key=gateway_credentials["publishable_key"],
                webhook_secret=gateway_credentials.get("webhook_secret")
            )
        elif selected_gateway == "btcpay":
            from ...adapters.btcpay_adapter import BTCPayCredentials
            credentials = BTCPayCredentials(
                server_url=gateway_credentials["server_url"],
                api_key=gateway_credentials["api_key"],
                store_id=gateway_credentials["store_id"],
                webhook_secret=gateway_credentials.get("webhook_secret")
            )
        else:
            raise PaymentGatewayException(
                f"Adapter not implemented for gateway: {selected_gateway}",
                error_code="ADAPTER_NOT_IMPLEMENTED",
                gateway=selected_gateway
            )

        # Step 4: Create adapter instance and process payment
        adapter = adapter_class(credentials)

        # Validate credentials before processing
        if not await adapter.validate_credentials():
            raise PaymentGatewayException(
                f"Invalid credentials for gateway: {selected_gateway}",
                error_code="INVALID_CREDENTIALS",
                gateway=selected_gateway
            )

        # Process the payment
        start_time = datetime.now()
        payment_response = await adapter.process_payment(payment_request)
        processing_time = (datetime.now() - start_time).total_seconds() * 1000

        # Log payment event
        log_payment_event(
            logger,
            event_type="payment_created",
            payment_id=payment_response.payment_id,
            gateway=selected_gateway,
            amount=float(payment_response.amount),
            currency=payment_response.currency,
            status=payment_response.status.value,
            processing_time_ms=processing_time
        )

        # Schedule background tasks (webhook notifications, etc.)
        background_tasks.add_task(
            process_payment_created,
            payment_response,
            selected_gateway,
            user_config.user_id
        )

        return payment_response

    except PaymentGatewayException as e:
        logger.error(f"Payment processing failed: {e.message}")
        raise create_http_exception(e)
    except Exception as e:
        logger.error(f"Unexpected error processing payment: {e}")
        raise HTTPException(
            status_code=500,
            detail={"error": {"code": "INTERNAL_ERROR", "message": "Internal server error"}}
        )


@router.get("/payments/{payment_id}", response_model=PaymentResponse)
async def get_payment_status(
    payment_id: str,
    user_config: UserGatewayConfig = Depends(get_tenant_gateway_config)
) -> PaymentResponse:
    """
    Get the status of a payment.

    This endpoint retrieves the current status of a payment from the
    appropriate gateway based on the payment ID format.
    """
    try:
        logger.info(f"Retrieving payment status: {payment_id}")

        # Extract gateway from payment ID (format: gateway_actual_id)
        if "_" not in payment_id:
            raise PaymentGatewayException(
                "Invalid payment ID format",
                error_code="INVALID_PAYMENT_ID"
            )

        gateway, gateway_payment_id = payment_id.split("_", 1)

        # Verify user has access to this gateway
        if gateway not in user_config.enabled_gateways:
            raise PaymentGatewayException(
                f"Access denied to gateway: {gateway}",
                error_code="ACCESS_DENIED",
                gateway=gateway
            )

        # Get adapter and credentials
        registry = get_adapter_registry()
        adapter_class = registry.get_adapter_class(gateway)
        settings = get_settings()
        gateway_credentials = settings.get_gateway_credentials(gateway)

        if not gateway_credentials:
            raise PaymentGatewayException(
                f"No credentials configured for gateway: {gateway}",
                error_code="GATEWAY_NOT_CONFIGURED",
                gateway=gateway
            )

        # Create adapter and get payment status
        if gateway == "stripe":
            from ...adapters.stripe_adapter import StripeCredentials
            credentials = StripeCredentials(
                secret_key=gateway_credentials["secret_key"],
                publishable_key=gateway_credentials["publishable_key"],
                webhook_secret=gateway_credentials.get("webhook_secret")
            )
        elif gateway == "btcpay":
            from ...adapters.btcpay_adapter import BTCPayCredentials
            credentials = BTCPayCredentials(
                server_url=gateway_credentials["server_url"],
                api_key=gateway_credentials["api_key"],
                store_id=gateway_credentials["store_id"],
                webhook_secret=gateway_credentials.get("webhook_secret")
            )
        else:
            raise PaymentGatewayException(
                f"Adapter not implemented for gateway: {gateway}",
                error_code="ADAPTER_NOT_IMPLEMENTED",
                gateway=gateway
            )

        adapter = adapter_class(credentials)
        payment_response = await adapter.get_payment_status(gateway_payment_id)

        logger.info(f"Payment status retrieved: {payment_id} - {payment_response.status}")
        return payment_response

    except PaymentGatewayException as e:
        logger.error(f"Failed to retrieve payment status: {e.message}")
        raise create_http_exception(e)
    except Exception as e:
        logger.error(f"Unexpected error retrieving payment status: {e}")
        raise HTTPException(
            status_code=500,
            detail={"error": {"code": "INTERNAL_ERROR", "message": "Internal server error"}}
        )


@router.post("/payments/{payment_id}/refund", response_model=RefundResponse)
async def refund_payment(
    payment_id: str,
    refund_request: RefundRequest,
    user_config: UserGatewayConfig = Depends(get_tenant_gateway_config)
) -> RefundResponse:
    """
    Refund a payment.

    This endpoint processes refunds through the appropriate gateway
    based on the original payment.
    """
    try:
        logger.info(f"Processing refund for payment: {payment_id}")

        # Extract gateway from payment ID
        if "_" not in payment_id:
            raise PaymentGatewayException(
                "Invalid payment ID format",
                error_code="INVALID_PAYMENT_ID"
            )

        gateway, gateway_payment_id = payment_id.split("_", 1)

        # Verify user has access to this gateway
        if gateway not in user_config.enabled_gateways:
            raise PaymentGatewayException(
                f"Access denied to gateway: {gateway}",
                error_code="ACCESS_DENIED",
                gateway=gateway
            )

        # Get adapter and process refund
        registry = get_adapter_registry()
        adapter_class = registry.get_adapter_class(gateway)
        settings = get_settings()
        gateway_credentials = settings.get_gateway_credentials(gateway)

        if not gateway_credentials:
            raise PaymentGatewayException(
                f"No credentials configured for gateway: {gateway}",
                error_code="GATEWAY_NOT_CONFIGURED",
                gateway=gateway
            )

        # Create adapter
        if gateway == "stripe":
            from ...adapters.stripe_adapter import StripeCredentials
            credentials = StripeCredentials(
                secret_key=gateway_credentials["secret_key"],
                publishable_key=gateway_credentials["publishable_key"],
                webhook_secret=gateway_credentials.get("webhook_secret")
            )
        elif gateway == "btcpay":
            from ...adapters.btcpay_adapter import BTCPayCredentials
            credentials = BTCPayCredentials(
                server_url=gateway_credentials["server_url"],
                api_key=gateway_credentials["api_key"],
                store_id=gateway_credentials["store_id"],
                webhook_secret=gateway_credentials.get("webhook_secret")
            )
        else:
            raise PaymentGatewayException(
                f"Adapter not implemented for gateway: {gateway}",
                error_code="ADAPTER_NOT_IMPLEMENTED",
                gateway=gateway
            )

        adapter = adapter_class(credentials)
        refund_response = await adapter.refund_payment(refund_request, gateway_payment_id)

        # Log refund event
        log_payment_event(
            logger,
            event_type="refund_processed",
            payment_id=payment_id,
            gateway=gateway,
            amount=float(refund_response.amount),
            currency="USD",  # Would need to get from original payment
            status=refund_response.status.value,
            refund_id=refund_response.refund_id
        )

        return refund_response

    except PaymentGatewayException as e:
        logger.error(f"Refund processing failed: {e.message}")
        raise create_http_exception(e)
    except Exception as e:
        logger.error(f"Unexpected error processing refund: {e}")
        raise HTTPException(
            status_code=500,
            detail={"error": {"code": "INTERNAL_ERROR", "message": "Internal server error"}}
        )


@router.get("/gateways")
async def list_gateways(
    current_tenant: TenantResponse = Depends(get_current_tenant)
) -> dict:
    """
    List available payment gateways and their capabilities.

    This endpoint provides information about available gateways,
    their supported features, current status, and regional availability.
    """
    try:
        from ...services.gateway_availability_service import get_gateway_availability_service

        availability_service = get_gateway_availability_service()
        available_gateways = availability_service.get_available_gateways(current_tenant)
        region_summary = availability_service.get_region_summary()

        # Format gateway information
        gateway_info = {}
        for gw in available_gateways:
            gateway_info[gw.gateway_id] = {
                "name": gw.name,
                "status": gw.status.value,
                "message": gw.message,
                "supported_currencies": gw.supported_currencies,
                "supported_methods": gw.supported_methods,
                "regions": gw.regions,
                "restrictions": gw.restrictions,
                "configured": gw.is_configured,
                "configuration_required": gw.configuration_required
            }

        return {
            "gateways": gateway_info,
            "region_summary": region_summary,
            "available_count": len([gw for gw in available_gateways if gw.status.value == "available"]),
            "total_count": len(available_gateways),
            "recommendations": {
                "USD": availability_service.get_recommended_gateways("USD", current_tenant.business_type, current_tenant),
                "BTC": availability_service.get_recommended_gateways("BTC", current_tenant.business_type, current_tenant),
                "EUR": availability_service.get_recommended_gateways("EUR", current_tenant.business_type, current_tenant)
            }
        }

    except Exception as e:
        logger.error(f"Error listing gateways: {e}")
        raise HTTPException(
            status_code=500,
            detail={"error": {"code": "INTERNAL_ERROR", "message": "Internal server error"}}
        )


# Background task functions

async def process_payment_created(
    payment_response: PaymentResponse,
    gateway: str,
    user_id: str
) -> None:
    """
    Background task to process payment creation events.

    This could include:
    - Sending webhook notifications
    - Updating analytics
    - Triggering business logic
    """
    logger.info(f"Processing payment created event: {payment_response.payment_id}")

    # Placeholder for background processing
    # In production, this would handle:
    # - Database updates
    # - Webhook notifications to merchants
    # - Analytics and reporting
    # - Fraud detection
    # - etc.
