"""
Tenant-related Pydantic models for ELOH Processing Multi-Tenant Payment Gateway API

This module defines models for managing tenants (customers of ELOH Processing)
who use our payment gateway services for their own customers.
"""

from pydantic import BaseModel, Field, EmailStr, validator
from typing import Optional, Dict, Any, List
from datetime import datetime
from enum import Enum

class TenantStatus(str, Enum):
    """Tenant account status"""
    ACTIVE = "active"
    SUSPENDED = "suspended"
    PENDING = "pending"
    CANCELLED = "cancelled"

class TenantPlan(str, Enum):
    """Tenant service plans"""
    STARTER = "starter"
    PROFESSIONAL = "professional"
    ENTERPRISE = "enterprise"
    CUSTOM = "custom"

class GatewayConfiguration(BaseModel):
    """Gateway configuration for a tenant"""
    gateway_id: str = Field(..., description="Gateway identifier (stripe, square, btcpay, nowpayments)")
    enabled: bool = Field(True, description="Whether gateway is enabled for tenant")
    credentials: Dict[str, str] = Field(..., description="Encrypted gateway credentials")
    configuration: Dict[str, Any] = Field(default_factory=dict, description="Gateway-specific configuration")
    priority: int = Field(1, description="Gateway priority (1 = highest)")
    
    # Gateway limits and settings
    min_amount: Optional[float] = Field(None, description="Minimum transaction amount")
    max_amount: Optional[float] = Field(None, description="Maximum transaction amount")
    daily_limit: Optional[float] = Field(None, description="Daily transaction limit")
    monthly_limit: Optional[float] = Field(None, description="Monthly transaction limit")
    
    # Supported features
    supports_refunds: bool = Field(True, description="Whether gateway supports refunds")
    supports_recurring: bool = Field(False, description="Whether gateway supports recurring payments")
    supports_webhooks: bool = Field(True, description="Whether gateway supports webhooks")
    
    # Webhook configuration
    webhook_url: Optional[str] = Field(None, description="Tenant webhook URL")
    webhook_events: List[str] = Field(default_factory=list, description="Subscribed webhook events")

class TenantRequest(BaseModel):
    """Request model for creating a new tenant"""
    
    # Company information
    company_name: str = Field(..., max_length=100, description="Company name")
    business_type: str = Field(..., max_length=50, description="Type of business")
    website_url: Optional[str] = Field(None, description="Company website")
    
    # Primary contact
    contact_email: EmailStr = Field(..., description="Primary contact email")
    contact_name: str = Field(..., max_length=100, description="Primary contact name")
    contact_phone: Optional[str] = Field(None, max_length=20, description="Primary contact phone")
    
    # Business address
    address_line1: str = Field(..., max_length=100, description="Address line 1")
    address_line2: Optional[str] = Field(None, max_length=100, description="Address line 2")
    city: str = Field(..., max_length=50, description="City")
    state: Optional[str] = Field(None, max_length=50, description="State/Province")
    postal_code: str = Field(..., max_length=20, description="Postal/ZIP code")
    country: str = Field(..., min_length=2, max_length=2, description="ISO 3166-1 alpha-2 country code")
    
    # Service configuration
    plan: TenantPlan = Field(TenantPlan.STARTER, description="Service plan")
    expected_monthly_volume: Optional[float] = Field(None, description="Expected monthly transaction volume")
    
    # Gateway preferences
    preferred_gateways: List[str] = Field(default_factory=list, description="Preferred payment gateways")
    required_currencies: List[str] = Field(default_factory=list, description="Required currency support")
    
    # Metadata
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")

class TenantResponse(BaseModel):
    """Response model for tenant information"""
    
    # Tenant identifiers
    tenant_id: str = Field(..., description="Unique tenant identifier")
    api_key: str = Field(..., description="Tenant API key for authentication")
    
    # Company information
    company_name: str = Field(..., description="Company name")
    business_type: str = Field(..., description="Type of business")
    website_url: Optional[str] = Field(None, description="Company website")
    
    # Contact information
    contact_email: EmailStr = Field(..., description="Primary contact email")
    contact_name: str = Field(..., description="Primary contact name")
    contact_phone: Optional[str] = Field(None, description="Primary contact phone")
    
    # Service information
    plan: TenantPlan = Field(..., description="Service plan")
    status: TenantStatus = Field(..., description="Tenant status")
    
    # Gateway configurations
    gateways: List[GatewayConfiguration] = Field(default_factory=list, description="Configured gateways")
    
    # Usage statistics
    total_transactions: int = Field(0, description="Total number of transactions")
    total_volume: float = Field(0.0, description="Total transaction volume")
    monthly_volume: float = Field(0.0, description="Current month transaction volume")
    
    # Timestamps
    created_at: datetime = Field(..., description="Tenant creation timestamp")
    updated_at: Optional[datetime] = Field(None, description="Last update timestamp")
    last_activity: Optional[datetime] = Field(None, description="Last activity timestamp")
    
    # Metadata
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")

class TenantUpdateRequest(BaseModel):
    """Request model for updating tenant information"""
    
    # Company information updates
    company_name: Optional[str] = Field(None, max_length=100, description="Company name")
    business_type: Optional[str] = Field(None, max_length=50, description="Type of business")
    website_url: Optional[str] = Field(None, description="Company website")
    
    # Contact updates
    contact_name: Optional[str] = Field(None, max_length=100, description="Primary contact name")
    contact_phone: Optional[str] = Field(None, max_length=20, description="Primary contact phone")
    
    # Service updates
    plan: Optional[TenantPlan] = Field(None, description="Service plan")
    status: Optional[TenantStatus] = Field(None, description="Tenant status")
    
    # Metadata
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")

class GatewayConfigurationRequest(BaseModel):
    """Request model for configuring tenant gateways"""
    
    gateway_id: str = Field(..., description="Gateway identifier")
    enabled: bool = Field(True, description="Whether gateway is enabled")
    credentials: Dict[str, str] = Field(..., description="Gateway credentials")
    configuration: Dict[str, Any] = Field(default_factory=dict, description="Gateway configuration")
    priority: int = Field(1, ge=1, le=10, description="Gateway priority (1-10)")
    
    # Limits
    min_amount: Optional[float] = Field(None, ge=0, description="Minimum transaction amount")
    max_amount: Optional[float] = Field(None, ge=0, description="Maximum transaction amount")
    daily_limit: Optional[float] = Field(None, ge=0, description="Daily transaction limit")
    monthly_limit: Optional[float] = Field(None, ge=0, description="Monthly transaction limit")
    
    # Webhook configuration
    webhook_url: Optional[str] = Field(None, description="Tenant webhook URL")
    webhook_events: List[str] = Field(default_factory=list, description="Subscribed webhook events")

class TenantListRequest(BaseModel):
    """Request model for listing tenants"""
    status: Optional[TenantStatus] = Field(None, description="Filter by status")
    plan: Optional[TenantPlan] = Field(None, description="Filter by plan")
    business_type: Optional[str] = Field(None, description="Filter by business type")
    limit: int = Field(50, ge=1, le=100, description="Number of results to return")
    offset: int = Field(0, ge=0, description="Number of results to skip")
    search: Optional[str] = Field(None, description="Search in company name or email")

class TenantListResponse(BaseModel):
    """Response model for listing tenants"""
    tenants: List[TenantResponse] = Field(..., description="List of tenants")
    total: int = Field(..., description="Total number of tenants")
    limit: int = Field(..., description="Limit used")
    offset: int = Field(..., description="Offset used")
    has_more: bool = Field(..., description="Whether there are more results")

class TenantUsageStats(BaseModel):
    """Tenant usage statistics"""
    tenant_id: str = Field(..., description="Tenant identifier")
    period_start: datetime = Field(..., description="Statistics period start")
    period_end: datetime = Field(..., description="Statistics period end")
    
    # Transaction statistics
    total_transactions: int = Field(0, description="Total transactions in period")
    successful_transactions: int = Field(0, description="Successful transactions")
    failed_transactions: int = Field(0, description="Failed transactions")
    refunded_transactions: int = Field(0, description="Refunded transactions")
    
    # Volume statistics
    total_volume: float = Field(0.0, description="Total transaction volume")
    average_transaction: float = Field(0.0, description="Average transaction amount")
    
    # Gateway breakdown
    gateway_stats: Dict[str, Dict[str, Any]] = Field(default_factory=dict, description="Per-gateway statistics")
    
    # Currency breakdown
    currency_stats: Dict[str, Dict[str, Any]] = Field(default_factory=dict, description="Per-currency statistics")
