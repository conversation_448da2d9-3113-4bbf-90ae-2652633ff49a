<?php
/**
 * Square Payment Processor
 * Handles Square payment token processing
 */

header('Content-Type: application/json');

try {
    // Validate request method
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Invalid request method');
    }

    // Get form data
    $paymentToken = $_POST['payment_token'] ?? '';
    $paymentMethod = $_POST['payment_method'] ?? 'card';
    $amount = floatval($_POST['amount'] ?? 0);
    $email = $_POST['email'] ?? '';
    $service = $_POST['service'] ?? '';
    $description = $_POST['description'] ?? '';
    $paymentType = $_POST['payment_type'] ?? 'service';

    // Validate required fields
    if (empty($paymentToken)) {
        throw new Exception('Payment token is required');
    }
    if ($amount < 1) {
        throw new Exception('Amount must be at least $1.00');
    }
    if (empty($email) || !filter_var($email, FILTER_VALIDATE_EMAIL)) {
        throw new Exception('Valid email address is required');
    }

    // Initialize Square Gateway
    require_once 'includes/square-gateway.php';
    $squareGateway = new Square_Gateway();

    // Check if Square is available
    if (!$squareGateway->isAvailable()) {
        throw new Exception('Square payment processing is not available. Please try a different payment method.');
    }

    // Generate unique order ID
    $orderId = 'ELOH_' . strtoupper($paymentType) . '_' . date('Ymd') . '_' . uniqid();

    // Prepare payment options
    $paymentOptions = [
        'customer_email' => $email,
        'order_id' => $orderId,
        'note' => $description,
        'payment_method' => $paymentMethod
    ];

    // Add service information if not a donation
    if ($paymentType !== 'donation' && !empty($service)) {
        $paymentOptions['service_type'] = $service;
    }

    // Create payment with Square
    $paymentResult = $squareGateway->createPayment($amount, 'USD', $paymentToken, $paymentOptions);

    if ($paymentResult['success']) {
        // Log successful payment
        error_log("Square payment successful: " . json_encode($paymentResult));

        // Send confirmation email (in a real implementation)
        // sendPaymentConfirmationEmail($email, $paymentResult, $amount, $service);

        // Return success response
        echo json_encode([
            'success' => true,
            'payment_id' => $paymentResult['payment_id'],
            'amount' => $paymentResult['amount'],
            'currency' => $paymentResult['currency'],
            'status' => $paymentResult['status'],
            'receipt_url' => $paymentResult['receipt_url'] ?? null,
            'message' => 'Payment processed successfully!'
        ]);

    } else {
        // Log payment failure
        error_log("Square payment failed: " . json_encode($paymentResult));

        // Return error response
        echo json_encode([
            'success' => false,
            'error' => $paymentResult['error'] ?? 'Payment processing failed',
            'error_code' => $paymentResult['error_code'] ?? 'UNKNOWN'
        ]);
    }

} catch (Exception $e) {
    // Log exception
    error_log("Square payment exception: " . $e->getMessage());

    // Return error response
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'error_code' => 'EXCEPTION'
    ]);
}

/**
 * Send payment confirmation email
 * (This would be implemented in a real system)
 */
function sendPaymentConfirmationEmail($email, $paymentResult, $amount, $service) {
    // Email implementation would go here
    // For now, just log that we would send an email
    error_log("Would send confirmation email to: $email for payment: {$paymentResult['payment_id']}");
}
?>
