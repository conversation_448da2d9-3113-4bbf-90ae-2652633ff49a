<?php
/**
 * NowPayments Payment Creation API
 * Creates live NowPayments invoices for the widget
 */

// CORS headers for cross-origin requests
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Accept');
header('Content-Type: application/json');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'error' => 'Method not allowed']);
    exit;
}

try {
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        throw new Exception('Invalid JSON input');
    }
    
    // Validate required fields
    $required_fields = ['amount', 'email', 'order_id'];
    foreach ($required_fields as $field) {
        if (empty($input[$field])) {
            throw new Exception("Missing required field: {$field}");
        }
    }
    
    $amount = floatval($input['amount']);
    $email = filter_var($input['email'], FILTER_VALIDATE_EMAIL);
    $order_id = sanitize_text_field($input['order_id']);
    $description = sanitize_text_field($input['description'] ?? 'Widget Payment');
    
    // Validate inputs
    if ($amount < 5 || $amount > 10000) {
        throw new Exception('Amount must be between $5.00 and $10,000.00');
    }
    
    if (!$email) {
        throw new Exception('Invalid email address');
    }
    
    // Load NowPayments configuration
    $config_file = __DIR__ . '/../config/nowpayments-config.json';
    if (!file_exists($config_file)) {
        throw new Exception('NowPayments not configured');
    }
    
    $nowpayments_config = json_decode(file_get_contents($config_file), true);
    if (!$nowpayments_config || empty($nowpayments_config['api_key'])) {
        throw new Exception('NowPayments configuration incomplete');
    }
    
    // Create NowPayments payment
    $payment_data = [
        'price_amount' => $amount,
        'price_currency' => 'usd',
        'pay_currency' => 'btc',
        'order_id' => $order_id,
        'order_description' => $description,
        'ipn_callback_url' => 'https://elohprocessing.infy.uk/webhook/nowpayments.php',
        'success_url' => 'https://elohprocessing.infy.uk/payment-success.php?order=' . urlencode($order_id),
        'cancel_url' => 'https://elohprocessing.infy.uk/payment-cancel.php?order=' . urlencode($order_id)
    ];
    
    // Make API call to NowPayments
    $context = stream_context_create([
        'http' => [
            'method' => 'POST',
            'header' => [
                'Content-Type: application/json',
                'x-api-key: ' . $nowpayments_config['api_key']
            ],
            'content' => json_encode($payment_data),
            'timeout' => 30
        ]
    ]);
    
    $response = file_get_contents('https://api.nowpayments.io/v1/payment', false, $context);
    
    if ($response === false) {
        throw new Exception('Failed to connect to NowPayments API');
    }
    
    $payment = json_decode($response, true);
    
    if (!$payment || !isset($payment['payment_id'])) {
        $error_message = 'Failed to create NowPayments payment';
        if (isset($payment['message'])) {
            $error_message .= ': ' . $payment['message'];
        }
        throw new Exception($error_message);
    }
    
    // Log successful payment creation
    error_log("NowPayments payment created: {$payment['payment_id']} for order: {$order_id}");
    
    // Store payment data
    $payment_record = [
        'order_id' => $order_id,
        'payment_id' => $payment['payment_id'],
        'amount' => $amount,
        'email' => $email,
        'description' => $description,
        'gateway' => 'nowpayments',
        'status' => 'pending',
        'created_at' => time(),
        'payment_data' => $payment
    ];
    
    $payments_dir = __DIR__ . '/../widget/payments';
    if (!is_dir($payments_dir)) {
        mkdir($payments_dir, 0755, true);
    }
    
    file_put_contents($payments_dir . "/{$order_id}.json", json_encode($payment_record, JSON_PRETTY_PRINT));
    
    // Determine payment URL
    $payment_url = '';
    if (isset($payment['invoice_url'])) {
        $payment_url = $payment['invoice_url'];
    } else {
        // Create custom checkout URL
        $payment_url = 'https://elohprocessing.infy.uk/nowpayments-checkout.php?payment_id=' . $payment['payment_id'] . '&order=' . urlencode($order_id);
    }
    
    // Return success response
    echo json_encode([
        'success' => true,
        'payment_id' => $payment['payment_id'],
        'payment_url' => $payment_url,
        'checkout_link' => $payment_url,
        'order_id' => $order_id,
        'amount' => $amount,
        'currency' => 'USD',
        'pay_currency' => 'BTC',
        'gateway' => 'nowpayments',
        'payment_data' => $payment
    ]);
    
} catch (Exception $e) {
    error_log("NowPayments API Error: " . $e->getMessage());
    
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}

function sanitize_text_field($value) {
    return htmlspecialchars(strip_tags(trim($value ?? '')));
}
?>
