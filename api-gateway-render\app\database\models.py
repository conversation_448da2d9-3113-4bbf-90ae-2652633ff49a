"""
Database models for ELOH Processing Payment Gateway
"""

from sqlalchemy import Column, Integer, String, Float, Boolean, DateTime, Text, ForeignKey, JSON
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from datetime import datetime
import uuid

Base = declarative_base()


class Tenant(Base):
    """Tenant model for multi-tenant architecture"""
    __tablename__ = "tenants"
    
    id = Column(Integer, primary_key=True, index=True)
    tenant_id = Column(String(50), unique=True, index=True, default=lambda: f"tenant_{uuid.uuid4().hex[:8]}")
    api_key = Column(String(100), unique=True, index=True, default=lambda: f"eloh_{uuid.uuid4().hex}")
    
    # Company information
    company_name = Column(String(200), nullable=False)
    business_type = Column(String(100))
    website_url = Column(String(500))
    
    # Contact information
    contact_email = Column(String(200), nullable=False)
    contact_name = Column(String(200))
    contact_phone = Column(String(50))
    
    # Address
    address_line1 = Column(String(200))
    city = Column(String(100))
    state = Column(String(100))
    postal_code = Column(String(20))
    country = Column(String(10), default="DM")  # Dominica
    
    # Plan and limits
    plan = Column(String(50), default="starter")
    status = Column(String(20), default="active")
    
    # Usage tracking
    total_transactions = Column(Integer, default=0)
    total_volume = Column(Float, default=0.0)
    monthly_volume = Column(Float, default=0.0)
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    last_activity = Column(DateTime)
    
    # Relationships
    gateways = relationship("GatewayConfig", back_populates="tenant")
    payments = relationship("Payment", back_populates="tenant")


class GatewayConfig(Base):
    """Gateway configuration for tenants"""
    __tablename__ = "gateway_configs"
    
    id = Column(Integer, primary_key=True, index=True)
    tenant_id = Column(Integer, ForeignKey("tenants.id"), nullable=False)
    
    # Gateway information
    gateway_id = Column(String(50), nullable=False)  # btcpay, nowpayments, stripe, square
    enabled = Column(Boolean, default=True)
    priority = Column(Integer, default=1)
    
    # Configuration
    credentials = Column(JSON)  # Encrypted gateway credentials
    configuration = Column(JSON)  # Gateway-specific settings
    
    # Limits
    min_amount = Column(Float, default=0.01)
    max_amount = Column(Float, default=10000.0)
    daily_limit = Column(Float, default=50000.0)
    
    # Webhooks
    webhook_url = Column(String(500))
    webhook_events = Column(JSON)
    
    # Statistics
    total_transactions = Column(Integer, default=0)
    total_volume = Column(Float, default=0.0)
    last_used = Column(DateTime)
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    tenant = relationship("Tenant", back_populates="gateways")


class Payment(Base):
    """Payment transaction model"""
    __tablename__ = "payments"
    
    id = Column(Integer, primary_key=True, index=True)
    payment_id = Column(String(100), unique=True, index=True, default=lambda: f"pay_{uuid.uuid4().hex}")
    tenant_id = Column(Integer, ForeignKey("tenants.id"), nullable=False)
    gateway_config_id = Column(Integer, ForeignKey("gateway_configs.id"))
    
    # Payment details
    amount = Column(Float, nullable=False)
    currency = Column(String(10), nullable=False)
    description = Column(Text)
    
    # Customer information
    customer_email = Column(String(200))
    customer_name = Column(String(200))
    
    # Payment status
    status = Column(String(20), default="pending")  # pending, completed, failed, cancelled
    gateway_payment_id = Column(String(200))
    gateway_status = Column(String(50))
    
    # Gateway information
    gateway_used = Column(String(50))
    gateway_response = Column(JSON)
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    completed_at = Column(DateTime)
    
    # Relationships
    tenant = relationship("Tenant", back_populates="payments")


class Customer(Base):
    """Customer model"""
    __tablename__ = "customers"
    
    id = Column(Integer, primary_key=True, index=True)
    customer_id = Column(String(100), unique=True, index=True, default=lambda: f"cust_{uuid.uuid4().hex[:12]}")
    tenant_id = Column(Integer, ForeignKey("tenants.id"), nullable=False)
    
    # Customer information
    email = Column(String(200), nullable=False)
    name = Column(String(200))
    phone = Column(String(50))
    
    # Address
    address_line1 = Column(String(200))
    city = Column(String(100))
    state = Column(String(100))
    postal_code = Column(String(20))
    country = Column(String(10))
    
    # Metadata
    metadata = Column(JSON)
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)


class UsageStats(Base):
    """Usage statistics for tenants"""
    __tablename__ = "usage_stats"
    
    id = Column(Integer, primary_key=True, index=True)
    tenant_id = Column(Integer, ForeignKey("tenants.id"), nullable=False)
    
    # Period information
    period_type = Column(String(20), nullable=False)  # daily, weekly, monthly
    period_start = Column(DateTime, nullable=False)
    period_end = Column(DateTime, nullable=False)
    
    # Statistics
    transaction_count = Column(Integer, default=0)
    total_volume = Column(Float, default=0.0)
    successful_payments = Column(Integer, default=0)
    failed_payments = Column(Integer, default=0)
    
    # Gateway breakdown
    gateway_stats = Column(JSON)
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
