<?php
/**
 * NowPayments Gateway for ELOH Processing
 * Supports 300+ cryptocurrencies with low fees
 */

class NowPayments_Gateway {
    
    private $apiKey;
    private $apiUrl;
    private $sandboxMode;
    private $ipnSecret;
    
    public function __construct($sandboxMode = true) {
        $config = require __DIR__ . '/nowpayments-config.php';
        $this->sandboxMode = $sandboxMode;
        $this->apiKey = $sandboxMode ? $config['sandbox_api_key'] : $config['live_api_key'];
        $this->apiUrl = $sandboxMode ? $config['sandbox_url'] : $config['live_url'];
        $this->ipnSecret = $config['ipn_secret'];
    }
    
    /**
     * Get available currencies
     */
    public function getAvailableCurrencies() {
        $response = $this->makeApiCall('GET', '/currencies');
        
        if ($response && isset($response['currencies'])) {
            return [
                'success' => true,
                'currencies' => $response['currencies']
            ];
        }
        
        return [
            'success' => false,
            'error' => 'Failed to fetch currencies',
            'details' => $response
        ];
    }
    
    /**
     * Get minimum payment amount for a currency
     */
    public function getMinimumAmount($currency) {
        $response = $this->makeApiCall('GET', "/min-amount?currency_from=usd&currency_to=" . strtolower($currency));
        
        if ($response && isset($response['min_amount'])) {
            return [
                'success' => true,
                'min_amount' => $response['min_amount']
            ];
        }
        
        return [
            'success' => false,
            'error' => 'Failed to get minimum amount'
        ];
    }
    
    /**
     * Create payment
     */
    public function createPayment($amount, $currency, $orderId, $description = null, $customerEmail = null) {
        $paymentData = [
            'price_amount' => (float)$amount,
            'price_currency' => 'usd',
            'pay_currency' => strtolower($currency),
            'order_id' => $orderId,
            'order_description' => $description ?: 'ELOH Processing Payment',
            'ipn_callback_url' => $this->getIpnUrl(),
            'success_url' => $this->getSuccessUrl($orderId),
            'cancel_url' => $this->getCancelUrl($orderId)
        ];
        
        if ($customerEmail) {
            $paymentData['customer_email'] = $customerEmail;
        }
        
        $response = $this->makeApiCall('POST', '/payment', $paymentData);
        
        if ($response && isset($response['payment_id'])) {
            return [
                'success' => true,
                'payment_id' => $response['payment_id'],
                'payment_status' => $response['payment_status'],
                'pay_address' => $response['pay_address'],
                'pay_amount' => $response['pay_amount'],
                'pay_currency' => $response['pay_currency'],
                'price_amount' => $response['price_amount'],
                'price_currency' => $response['price_currency'],
                'payment_url' => $this->generatePaymentUrl($response['payment_id']),
                'qr_code_url' => $this->generateQrCode($response['pay_address'], $response['pay_amount'])
            ];
        }
        
        return [
            'success' => false,
            'error' => 'Failed to create payment',
            'details' => $response
        ];
    }
    
    /**
     * Get payment status
     */
    public function getPaymentStatus($paymentId) {
        $response = $this->makeApiCall('GET', "/payment/{$paymentId}");
        
        if ($response && isset($response['payment_status'])) {
            return [
                'success' => true,
                'payment_status' => $response['payment_status'],
                'pay_amount' => $response['pay_amount'] ?? null,
                'actually_paid' => $response['actually_paid'] ?? null,
                'outcome_amount' => $response['outcome_amount'] ?? null,
                'outcome_currency' => $response['outcome_currency'] ?? null
            ];
        }
        
        return [
            'success' => false,
            'error' => 'Payment not found'
        ];
    }
    
    /**
     * Validate IPN callback
     */
    public function validateIpn($payload, $signature) {
        $expectedSignature = hash_hmac('sha512', $payload, $this->ipnSecret);
        return hash_equals($expectedSignature, $signature);
    }
    
    /**
     * Make API call to NowPayments
     */
    private function makeApiCall($method, $endpoint, $data = null) {
        $url = $this->apiUrl . $endpoint;
        
        $headers = [
            'x-api-key: ' . $this->apiKey,
            'Content-Type: application/json'
        ];
        
        // Log the request for debugging
        error_log("NowPayments API Request: $method $url");
        if ($data) {
            error_log("NowPayments API Data: " . json_encode($data));
        }
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_USERAGENT, 'ELOH-Processing-NowPayments/1.0');
        
        if ($method === 'POST') {
            curl_setopt($ch, CURLOPT_POST, true);
            if ($data) {
                curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
            }
        }
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        // Log the response for debugging
        error_log("NowPayments API Response: HTTP $httpCode - " . substr($response, 0, 500));
        
        if ($error) {
            error_log("NowPayments API cURL Error: " . $error);
            return [
                'error' => 'Connection error: ' . $error,
                'http_code' => 0
            ];
        }
        
        if ($httpCode >= 400) {
            error_log("NowPayments API HTTP Error: " . $httpCode . " - " . $response);
            return [
                'error' => 'HTTP Error ' . $httpCode,
                'http_code' => $httpCode,
                'response' => $response
            ];
        }
        
        $decoded = json_decode($response, true);
        if ($decoded === null && json_last_error() !== JSON_ERROR_NONE) {
            error_log("NowPayments API JSON Error: " . json_last_error_msg());
            return [
                'error' => 'Invalid JSON response: ' . json_last_error_msg(),
                'response' => $response
            ];
        }
        
        return $decoded;
    }
    
    /**
     * Generate payment URL for hosted checkout
     */
    private function generatePaymentUrl($paymentId) {
        return "nowpayments-checkout.php?payment_id=" . $paymentId;
    }
    
    /**
     * Generate QR code for payment address
     */
    private function generateQrCode($address, $amount) {
        return "https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=" . urlencode($address);
    }
    
    /**
     * Get IPN callback URL
     */
    private function getIpnUrl() {
        return 'https://elohprocessing.infy.uk/nowpayments-ipn.php';
    }
    
    /**
     * Get success URL
     */
    private function getSuccessUrl($orderId) {
        return 'https://elohprocessing.infy.uk/payment-success.php?order=' . $orderId . '&gateway=nowpayments';
    }
    
    /**
     * Get cancel URL
     */
    private function getCancelUrl($orderId) {
        return 'https://elohprocessing.infy.uk/payment-cancelled.php?order=' . $orderId;
    }
    
    /**
     * Get supported currencies (popular ones)
     */
    public function getPopularCurrencies() {
        return [
            'btc' => 'Bitcoin',
            'eth' => 'Ethereum',
            'usdt' => 'Tether',
            'usdc' => 'USD Coin',
            'ltc' => 'Litecoin',
            'bch' => 'Bitcoin Cash',
            'xrp' => 'Ripple',
            'ada' => 'Cardano',
            'dot' => 'Polkadot',
            'matic' => 'Polygon',
            'trx' => 'Tron',
            'bnb' => 'Binance Coin'
        ];
    }
    
    /**
     * Get debug information
     */
    public function getDebugInfo() {
        return [
            'api_url' => $this->apiUrl,
            'sandbox_mode' => $this->sandboxMode,
            'api_key_set' => !empty($this->apiKey),
            'ipn_secret_set' => !empty($this->ipnSecret)
        ];
    }
}
