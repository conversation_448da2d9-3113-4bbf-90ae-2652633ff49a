"""
Tenant service for ELOH Processing Payment Gateway
"""

from typing import Optional, List
from sqlalchemy.orm import Session
import logging

from ..database.connection import get_database_manager
from ..database.models import Tenant, GatewayConfig
from ..models.tenant import TenantRequest, TenantResponse, GatewayConfigurationRequest

logger = logging.getLogger(__name__)


class TenantService:
    """Service for managing tenants"""
    
    def __init__(self):
        self.db_manager = get_database_manager()
    
    async def create_tenant(self, tenant_request: TenantRequest) -> TenantResponse:
        """Create a new tenant"""
        try:
            with self.db_manager.session_scope() as session:
                tenant = Tenant(
                    company_name=tenant_request.company_name,
                    business_type=tenant_request.business_type,
                    website_url=tenant_request.website_url,
                    contact_email=tenant_request.contact_email,
                    contact_name=tenant_request.contact_name,
                    contact_phone=tenant_request.contact_phone,
                    address_line1=tenant_request.address_line1,
                    city=tenant_request.city,
                    state=tenant_request.state,
                    postal_code=tenant_request.postal_code,
                    country=tenant_request.country,
                    plan=tenant_request.plan.value
                )
                
                session.add(tenant)
                session.flush()  # Get the ID
                
                # Create default gateway configurations if preferred gateways specified
                for gateway_id in tenant_request.preferred_gateways:
                    gateway_config = GatewayConfig(
                        tenant_id=tenant.id,
                        gateway_id=gateway_id,
                        enabled=False,  # Disabled until credentials are provided
                        credentials={}
                    )
                    session.add(gateway_config)
                
                return TenantResponse(
                    tenant_id=tenant.tenant_id,
                    api_key=tenant.api_key,
                    company_name=tenant.company_name,
                    business_type=tenant.business_type,
                    contact_email=tenant.contact_email,
                    contact_name=tenant.contact_name,
                    country=tenant.country,
                    plan=tenant.plan,
                    status=tenant.status,
                    total_transactions=tenant.total_transactions,
                    total_volume=tenant.total_volume,
                    monthly_volume=tenant.monthly_volume,
                    created_at=tenant.created_at,
                    last_activity=tenant.last_activity
                )
                
        except Exception as e:
            logger.error(f"Failed to create tenant: {e}")
            raise
    
    async def get_tenant(self, tenant_id: str) -> Optional[TenantResponse]:
        """Get tenant by ID"""
        try:
            with self.db_manager.session_scope() as session:
                tenant = session.query(Tenant).filter(
                    Tenant.tenant_id == tenant_id
                ).first()
                
                if not tenant:
                    return None
                
                return TenantResponse(
                    tenant_id=tenant.tenant_id,
                    api_key=tenant.api_key,
                    company_name=tenant.company_name,
                    business_type=tenant.business_type,
                    contact_email=tenant.contact_email,
                    contact_name=tenant.contact_name,
                    country=tenant.country,
                    plan=tenant.plan,
                    status=tenant.status,
                    total_transactions=tenant.total_transactions,
                    total_volume=tenant.total_volume,
                    monthly_volume=tenant.monthly_volume,
                    created_at=tenant.created_at,
                    last_activity=tenant.last_activity
                )
                
        except Exception as e:
            logger.error(f"Failed to get tenant {tenant_id}: {e}")
            raise
    
    async def configure_gateway(
        self, 
        tenant_id: str, 
        gateway_config: GatewayConfigurationRequest
    ) -> bool:
        """Configure a gateway for a tenant"""
        try:
            with self.db_manager.session_scope() as session:
                tenant = session.query(Tenant).filter(
                    Tenant.tenant_id == tenant_id
                ).first()
                
                if not tenant:
                    raise ValueError(f"Tenant {tenant_id} not found")
                
                # Check if gateway config already exists
                existing_config = session.query(GatewayConfig).filter(
                    GatewayConfig.tenant_id == tenant.id,
                    GatewayConfig.gateway_id == gateway_config.gateway_id
                ).first()
                
                if existing_config:
                    # Update existing configuration
                    existing_config.enabled = gateway_config.enabled
                    existing_config.credentials = gateway_config.credentials
                    existing_config.configuration = gateway_config.configuration
                    existing_config.priority = gateway_config.priority
                    existing_config.min_amount = gateway_config.min_amount
                    existing_config.max_amount = gateway_config.max_amount
                    existing_config.daily_limit = gateway_config.daily_limit
                    existing_config.webhook_url = gateway_config.webhook_url
                    existing_config.webhook_events = gateway_config.webhook_events
                else:
                    # Create new configuration
                    new_config = GatewayConfig(
                        tenant_id=tenant.id,
                        gateway_id=gateway_config.gateway_id,
                        enabled=gateway_config.enabled,
                        credentials=gateway_config.credentials,
                        configuration=gateway_config.configuration,
                        priority=gateway_config.priority,
                        min_amount=gateway_config.min_amount,
                        max_amount=gateway_config.max_amount,
                        daily_limit=gateway_config.daily_limit,
                        webhook_url=gateway_config.webhook_url,
                        webhook_events=gateway_config.webhook_events
                    )
                    session.add(new_config)
                
                return True
                
        except Exception as e:
            logger.error(f"Failed to configure gateway for tenant {tenant_id}: {e}")
            raise


# Global service instance
_tenant_service = None


def get_tenant_service() -> TenantService:
    """Get the global tenant service instance"""
    global _tenant_service
    if _tenant_service is None:
        _tenant_service = TenantService()
    return _tenant_service
