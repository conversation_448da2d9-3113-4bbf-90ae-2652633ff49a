<?php
// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once "includes/payment-gateway-manager.php";

if ($_SERVER["REQUEST_METHOD"] !== "POST") {
    header("Location: index.php");
    exit;
}

// Get form data
$gateway = $_POST["gateway"] ?? '';
$amount = floatval($_POST["amount"]);
$currency = $_POST["currency"] ?? '';
$email = $_POST["email"];
$payment_type = $_POST["payment_type"];
$description = $_POST["description"] ?? "";

// Build description based on payment type
if ($payment_type === "donation") {
    $full_description = "Donation to ELOH Processing LLC";
    if ($description) {
        $full_description .= " - " . $description;
    }
    $orderId = "DONATION_" . time() . "_" . rand(1000, 9999);
} else {
    $service = $_POST["service"] ?? "Service";
    $full_description = "Payment for: " . $service;
    if ($description) {
        $full_description .= " - " . $description;
    }
    $orderId = "SERVICE_" . time() . "_" . rand(1000, 9999);
}

// Validate inputs
$errors = [];
if (empty($gateway)) $errors[] = "No payment gateway selected";
if ($amount < 5) $errors[] = "Minimum amount is $5.00 USD";
if (empty($currency)) $errors[] = "No cryptocurrency selected";
if (!filter_var($email, FILTER_VALIDATE_EMAIL)) $errors[] = "Invalid email: $email";

if (!empty($errors)) {
    $error_details = implode(", ", $errors);
    header("Location: multi-gateway-payment-form.php?error=" . urlencode("Validation failed") . "&details=" . urlencode($error_details));
    exit;
}

try {
    // Create payment gateway manager
    $gatewayManager = new Payment_Gateway_Manager();

    // Validate gateway exists
    $availableGateways = $gatewayManager->getAvailableGateways();
    if (!isset($availableGateways[$gateway])) {
        header("Location: multi-gateway-payment-form.php?error=" . urlencode("Invalid payment gateway selected"));
        exit;
    }

    // Handle Square payments differently (requires frontend token generation)
    if ($gateway === 'square') {
        // Redirect to Square-specific payment form
        $params = http_build_query([
            'type' => $payment_type,
            'amount' => $amount,
            'service' => $service,
            'email' => $email,
            'description' => $description
        ]);
        header("Location: square-payment-form.php?$params");
        exit;
    }

    // Validate currency is supported by gateway
    $supportedCurrencies = $gatewayManager->getSupportedCurrencies($gateway);
    if (!in_array(strtoupper($currency), array_map('strtoupper', $supportedCurrencies))) {
        header("Location: multi-gateway-payment-form.php?error=" . urlencode("Currency not supported by selected gateway"));
        exit;
    }

    // Create payment
    $payment = $gatewayManager->createPayment(
        $gateway,
        $amount,
        $currency,
        $orderId,
        $full_description,
        $email
    );

    if ($payment['success']) {
        // Store payment data in session for tracking
        session_start();
        $_SESSION['multi_gateway_payments'][$orderId] = [
            'gateway' => $gateway,
            'order_id' => $orderId,
            'amount' => $amount,
            'currency' => $currency,
            'email' => $email,
            'description' => $full_description,
            'payment_type' => $payment_type,
            'created_at' => time(),
            'payment_data' => $payment
        ];

        // Redirect based on gateway type
        switch ($gateway) {
            case 'btcpay':
                // BTCPay Server - redirect to checkout
                if (isset($payment['checkout_link'])) {
                    header("Location: " . $payment['checkout_link']);
                } else {
                    header("Location: btcpay-payment-display.php?invoice=" . $payment['invoice_id']);
                }
                break;

            case 'nowpayments':
                // NowPayments - redirect to custom checkout page
                header("Location: nowpayments-checkout.php?payment_id=" . $payment['payment_id'] . "&order=" . $orderId);
                break;

            case 'square':
                // Square - redirect to success page (payment already processed)
                header("Location: payment-success.php?payment_id=" . $payment['payment_id'] . "&gateway=square");
                break;

            default:
                header("Location: payment-success.php?order=" . $orderId . "&gateway=" . $gateway);
        }
        exit;

    } else {
        $error_msg = isset($payment['error']) ? $payment['error'] : "Unknown error creating payment";
        if (isset($payment['details'])) {
            $error_msg .= " - " . json_encode($payment['details']);
        }

        header("Location: multi-gateway-payment-form.php?error=" . urlencode("Payment creation failed") . "&details=" . urlencode($error_msg));
        exit;
    }

} catch (Exception $e) {
    error_log("Multi-gateway payment processing error: " . $e->getMessage());
    header("Location: multi-gateway-payment-form.php?error=" . urlencode("System error") . "&details=" . urlencode($e->getMessage()));
    exit;
}
?>
