# ELOH Processing Payment Gateway - Render Optimized Dependencies

# Core FastAPI stack
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
pydantic-settings==2.1.0

# HTTP clients
httpx==0.25.2
aiohttp==3.9.1

# Database (PostgreSQL for Render)
sqlalchemy==2.0.23
psycopg2-binary==2.9.9
alembic==1.13.1

# Security
cryptography==41.0.7
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6

# Payment gateways
stripe==7.8.0

# Utilities
python-dotenv==1.0.0
click==8.1.7

# Monitoring (lightweight)
prometheus-client==0.19.0
