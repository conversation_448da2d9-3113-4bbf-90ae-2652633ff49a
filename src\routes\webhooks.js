const express = require('express');
const router = express.Router();
const { verifyBtcPaySignature } = require('../middleware/btcpay');
const { updateOrderStatus } = require('../services/orders');

router.post('/btcpay', verifyBtcPaySignature, async (req, res) => {
  try {
    const { invoiceId, status } = req.body;
    
    // Handle different payment statuses
    if (status === 'confirmed' || status === 'complete') {
      await updateOrderStatus(invoiceId, 'paid');
      // Notify merchant via their webhook if configured
    } else if (status === 'invalid') {
      await updateOrderStatus(invoiceId, 'failed');
    }
    
    res.status(200).send('Webhook received');
  } catch (error) {
    console.error('Webhook processing error:', error);
    res.status(500).send('Webhook processing failed');
  }
});

module.exports = router;