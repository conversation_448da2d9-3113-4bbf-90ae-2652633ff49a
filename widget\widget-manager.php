<?php
/**
 * ELOH Processing Widget Manager
 * Simple interface for creating and managing payment widgets
 */

require_once __DIR__ . '/widget-config.php';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'create_widget') {
        $widget_id = sanitize_text_field($_POST['widget_id'] ?? '');
        $config = $_POST['config'] ?? [];
        
        if (!empty($widget_id) && !empty($config)) {
            $widget_config = new Widget_Config();
            $result = $widget_config->saveConfig($widget_id, $config);
            
            if ($result) {
                $success_message = "Widget '{$widget_id}' created successfully!";
            } else {
                $error_message = "Failed to create widget configuration.";
            }
        } else {
            $error_message = "Widget ID and configuration are required.";
        }
    }
}

// Get existing widgets
$widgets = [];
$configs_dir = __DIR__ . '/configs';
if (is_dir($configs_dir)) {
    $files = glob($configs_dir . '/*.json');
    foreach ($files as $file) {
        $widget_id = basename($file, '.json');
        $config = json_decode(file_get_contents($file), true);
        $widgets[$widget_id] = $config;
    }
}

function sanitize_text_field($value) {
    return htmlspecialchars(strip_tags(trim($value)));
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ELOH Processing Widget Manager</title>
    <style>
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            line-height: 1.6;
            color: #333;
            background: #f8fafc;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 40px 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 12px;
        }
        
        .card {
            background: white;
            border-radius: 12px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-label {
            display: block;
            font-weight: 500;
            margin-bottom: 8px;
            color: #4a5568;
        }
        
        .form-input, .form-select, .form-textarea {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.2s ease;
        }
        
        .form-input:focus, .form-select:focus, .form-textarea:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .form-textarea {
            min-height: 100px;
            resize: vertical;
        }
        
        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 500;
            cursor: pointer;
            transition: transform 0.2s ease;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn:hover {
            transform: translateY(-1px);
        }
        
        .btn-secondary {
            background: #e2e8f0;
            color: #4a5568;
        }
        
        .alert {
            padding: 16px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .alert-success {
            background: #f0fdf4;
            color: #166534;
            border: 1px solid #bbf7d0;
        }
        
        .alert-error {
            background: #fef2f2;
            color: #dc2626;
            border: 1px solid #fecaca;
        }
        
        .widget-list {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
        }
        
        .widget-card {
            background: white;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            padding: 20px;
            transition: border-color 0.2s ease;
        }
        
        .widget-card:hover {
            border-color: #667eea;
        }
        
        .widget-id {
            font-size: 1.2rem;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 8px;
        }
        
        .widget-info {
            color: #718096;
            font-size: 14px;
            margin-bottom: 16px;
        }
        
        .widget-actions {
            display: flex;
            gap: 10px;
        }
        
        .code-snippet {
            background: #2d3748;
            color: #e2e8f0;
            padding: 12px;
            border-radius: 6px;
            font-family: monospace;
            font-size: 12px;
            overflow-x: auto;
            margin-top: 10px;
        }
        
        .checkbox-group {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;
        }
        
        .checkbox-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .checkbox-item input[type="checkbox"] {
            width: 16px;
            height: 16px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 Widget Manager</h1>
            <p>Create and manage ELOH Processing payment widgets</p>
        </div>
        
        <?php if (isset($success_message)): ?>
        <div class="alert alert-success"><?php echo htmlspecialchars($success_message); ?></div>
        <?php endif; ?>
        
        <?php if (isset($error_message)): ?>
        <div class="alert alert-error"><?php echo htmlspecialchars($error_message); ?></div>
        <?php endif; ?>
        
        <!-- Create New Widget -->
        <div class="card">
            <h2>Create New Widget</h2>
            <form method="POST">
                <input type="hidden" name="action" value="create_widget">
                
                <div class="form-group">
                    <label class="form-label">Widget ID</label>
                    <input type="text" name="widget_id" class="form-input" 
                           placeholder="my-widget-id" required
                           pattern="[a-zA-Z0-9_-]+" 
                           title="Only letters, numbers, hyphens, and underscores allowed">
                    <small style="color: #718096;">Unique identifier for your widget (letters, numbers, hyphens, underscores only)</small>
                </div>
                
                <div class="form-grid">
                    <div class="form-group">
                        <label class="form-label">Company Name</label>
                        <input type="text" name="config[company_name]" class="form-input" 
                               placeholder="Your Company Name">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">Theme</label>
                        <select name="config[theme]" class="form-select">
                            <option value="light">Light</option>
                            <option value="dark">Dark</option>
                            <option value="auto">Auto</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">Primary Color</label>
                        <input type="color" name="config[primary_color]" class="form-input" value="#667eea">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">Accent Color</label>
                        <input type="color" name="config[accent_color]" class="form-input" value="#764ba2">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">Min Amount ($)</label>
                        <input type="number" name="config[min_amount]" class="form-input" 
                               value="5.00" min="1" step="0.01">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">Max Amount ($)</label>
                        <input type="number" name="config[max_amount]" class="form-input" 
                               value="10000.00" min="1" step="0.01">
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">Enabled Payment Gateways</label>
                    <div class="checkbox-group">
                        <div class="checkbox-item">
                            <input type="checkbox" name="config[enabled_gateways][]" value="btcpay" id="gateway_btcpay" checked>
                            <label for="gateway_btcpay">BTCPay Server</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" name="config[enabled_gateways][]" value="nowpayments" id="gateway_nowpayments" checked>
                            <label for="gateway_nowpayments">NowPayments</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" name="config[enabled_gateways][]" value="square" id="gateway_square">
                            <label for="gateway_square">Square</label>
                        </div>
                    </div>
                </div>
                
                <div class="form-grid">
                    <div class="form-group">
                        <label class="form-label">Widget Title</label>
                        <input type="text" name="config[title]" class="form-input" 
                               value="Complete Your Payment">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">Button Text</label>
                        <input type="text" name="config[button_text]" class="form-input" 
                               value="Pay Now">
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">Description</label>
                    <input type="text" name="config[description]" class="form-input" 
                           value="Secure cryptocurrency payment processing">
                </div>
                
                <div class="form-group">
                    <label class="form-label">Webhook URL (Optional)</label>
                    <input type="url" name="config[webhook_url]" class="form-input" 
                           placeholder="https://yoursite.com/webhook">
                    <small style="color: #718096;">Receive payment notifications at this URL</small>
                </div>
                
                <div class="form-group">
                    <label class="form-label">Allowed Domains (Optional)</label>
                    <textarea name="config[allowed_domains_text]" class="form-textarea" 
                              placeholder="yoursite.com&#10;subdomain.yoursite.com&#10;&#10;Leave empty to allow all domains"></textarea>
                    <small style="color: #718096;">One domain per line. Leave empty to allow all domains.</small>
                </div>
                
                <button type="submit" class="btn">Create Widget</button>
            </form>
        </div>
        
        <!-- Existing Widgets -->
        <?php if (!empty($widgets)): ?>
        <div class="card">
            <h2>Existing Widgets</h2>
            <div class="widget-list">
                <?php foreach ($widgets as $widget_id => $config): ?>
                <div class="widget-card">
                    <div class="widget-id"><?php echo htmlspecialchars($widget_id); ?></div>
                    <div class="widget-info">
                        <strong>Company:</strong> <?php echo htmlspecialchars($config['company_name'] ?? 'Not set'); ?><br>
                        <strong>Theme:</strong> <?php echo htmlspecialchars($config['theme'] ?? 'light'); ?><br>
                        <strong>Gateways:</strong> <?php echo implode(', ', $config['enabled_gateways'] ?? []); ?><br>
                        <strong>Amount Range:</strong> $<?php echo number_format($config['min_amount'] ?? 5, 2); ?> - $<?php echo number_format($config['max_amount'] ?? 10000, 2); ?>
                    </div>
                    <div class="widget-actions">
                        <a href="checkout-widget.php?widget_id=<?php echo urlencode($widget_id); ?>&amount=50" 
                           target="_blank" class="btn btn-secondary">Preview</a>
                        <button onclick="showIntegrationCode('<?php echo htmlspecialchars($widget_id); ?>')" 
                                class="btn btn-secondary">Get Code</button>
                    </div>
                    <div id="code-<?php echo htmlspecialchars($widget_id); ?>" style="display: none;">
                        <div class="code-snippet">
&lt;script src="https://elohprocessing.infy.uk/widget/widget-embed.js"&gt;&lt;/script&gt;
&lt;div data-eloh-widget="<?php echo htmlspecialchars($widget_id); ?>" data-amount="100.00"&gt;&lt;/div&gt;
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>
        
        <!-- Documentation Link -->
        <div class="card" style="text-align: center;">
            <h3>Need Help?</h3>
            <p>Check out the widget demo and documentation for integration examples.</p>
            <a href="widget-demo.html" target="_blank" class="btn">View Demo & Documentation</a>
        </div>
    </div>
    
    <script>
        function showIntegrationCode(widgetId) {
            const codeDiv = document.getElementById('code-' + widgetId);
            if (codeDiv.style.display === 'none') {
                codeDiv.style.display = 'block';
            } else {
                codeDiv.style.display = 'none';
            }
        }
        
        // Handle allowed domains textarea
        document.querySelector('form').addEventListener('submit', function(e) {
            const domainsText = document.querySelector('textarea[name="config[allowed_domains_text]"]').value;
            const domains = domainsText.split('\n').map(d => d.trim()).filter(d => d.length > 0);
            
            // Create hidden inputs for domains array
            domains.forEach(domain => {
                const input = document.createElement('input');
                input.type = 'hidden';
                input.name = 'config[allowed_domains][]';
                input.value = domain;
                this.appendChild(input);
            });
        });
    </script>
</body>
</html>
