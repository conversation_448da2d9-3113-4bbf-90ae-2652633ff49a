<?php
/**
 * Square Business Management Class
 * ELOH Processing - Complete Business Management with Square APIs
 * 
 * Handles: Products, Inventory, Customers, Orders, Invoices, Bookings, Staff, etc.
 */

class Square_Business_Manager {
    private $squareGateway;
    private $config;
    
    public function __construct() {
        require_once 'square-gateway.php';
        $this->squareGateway = new Square_Gateway();
        $this->config = require_once 'square-config.php';
    }
    
    // ==================== CATALOG & PRODUCT MANAGEMENT ====================
    
    /**
     * Create service/product in Square catalog
     */
    public function createService($serviceData) {
        if (!$this->squareGateway->isAvailable()) {
            return ['success' => false, 'error' => 'Square not available'];
        }
        
        try {
            $catalogObject = new \Square\Models\CatalogObject();
            $catalogObject->setType('ITEM');
            $catalogObject->setId('#' . $serviceData['id']);
            
            $catalogItem = new \Square\Models\CatalogItem();
            $catalogItem->setName($serviceData['name']);
            $catalogItem->setDescription($serviceData['description']);
            $catalogItem->setProductType('REGULAR');
            
            // Create item variation (pricing)
            $itemVariation = new \Square\Models\CatalogItemVariation();
            $itemVariation->setName('Regular');
            $itemVariation->setItemId('#' . $serviceData['id']);
            
            if (isset($serviceData['price'])) {
                $priceMoney = new \Square\Models\Money();
                $priceMoney->setAmount($serviceData['price'] * 100); // Convert to cents
                $priceMoney->setCurrency('USD');
                $itemVariation->setPriceMoney($priceMoney);
            }
            
            $catalogItem->setVariations([$itemVariation]);
            $catalogObject->setItemData($catalogItem);
            
            $request = new \Square\Models\UpsertCatalogObjectRequest($catalogObject);
            $response = $this->squareGateway->catalogApi->upsertCatalogObject($request);
            
            if ($response->isSuccess()) {
                return [
                    'success' => true,
                    'catalog_object' => $response->getResult()->getCatalogObject()
                ];
            } else {
                return [
                    'success' => false,
                    'error' => $response->getErrors()[0]->getDetail()
                ];
            }
        } catch (Exception $e) {
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }
    
    /**
     * Get all services/products from catalog
     */
    public function getServices() {
        if (!$this->squareGateway->isAvailable()) {
            return $this->getMockServices();
        }
        
        try {
            $request = new \Square\Models\ListCatalogRequest();
            $request->setTypes(['ITEM']);
            
            $response = $this->squareGateway->catalogApi->listCatalog($request);
            
            if ($response->isSuccess()) {
                $services = [];
                $objects = $response->getResult()->getObjects() ?? [];
                
                foreach ($objects as $object) {
                    if ($object->getType() === 'ITEM') {
                        $itemData = $object->getItemData();
                        $variations = $itemData->getVariations() ?? [];
                        
                        $price = 0;
                        if (!empty($variations)) {
                            $priceMoney = $variations[0]->getPriceMoney();
                            if ($priceMoney) {
                                $price = $priceMoney->getAmount() / 100;
                            }
                        }
                        
                        $services[] = [
                            'id' => $object->getId(),
                            'name' => $itemData->getName(),
                            'description' => $itemData->getDescription(),
                            'price' => $price,
                            'category' => $itemData->getProductType()
                        ];
                    }
                }
                
                return ['success' => true, 'services' => $services];
            } else {
                return ['success' => false, 'error' => 'Failed to fetch services'];
            }
        } catch (Exception $e) {
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }
    
    /**
     * Get mock services when Square is not available
     */
    private function getMockServices() {
        return [
            'success' => true,
            'services' => [
                [
                    'id' => 'crypto_consulting',
                    'name' => 'Cryptocurrency & Forex Consulting',
                    'description' => 'Expert cryptocurrency and forex trading consultation',
                    'price' => 150.00,
                    'category' => 'consulting'
                ],
                [
                    'id' => 'mining_pool',
                    'name' => 'Mining Pool Membership',
                    'description' => 'Annual mining pool membership with profit sharing',
                    'price' => 200.00,
                    'category' => 'membership'
                ],
                [
                    'id' => 'mining_services',
                    'name' => 'Managed Mining Services',
                    'description' => 'Professional cryptocurrency mining management',
                    'price' => 500.00,
                    'category' => 'service'
                ],
                [
                    'id' => 'market_analysis',
                    'name' => 'Market Analysis Report',
                    'description' => 'Detailed cryptocurrency market analysis and recommendations',
                    'price' => 99.00,
                    'category' => 'report'
                ]
            ]
        ];
    }
    
    // ==================== CUSTOMER MANAGEMENT ====================
    
    /**
     * Create customer in Square
     */
    public function createCustomer($customerData) {
        if (!$this->squareGateway->isAvailable()) {
            return ['success' => false, 'error' => 'Square not available'];
        }
        
        try {
            $createCustomerRequest = new \Square\Models\CreateCustomerRequest();
            
            if (isset($customerData['given_name'])) {
                $createCustomerRequest->setGivenName($customerData['given_name']);
            }
            if (isset($customerData['family_name'])) {
                $createCustomerRequest->setFamilyName($customerData['family_name']);
            }
            if (isset($customerData['email'])) {
                $createCustomerRequest->setEmailAddress($customerData['email']);
            }
            if (isset($customerData['phone'])) {
                $createCustomerRequest->setPhoneNumber($customerData['phone']);
            }
            
            $response = $this->squareGateway->customersApi->createCustomer($createCustomerRequest);
            
            if ($response->isSuccess()) {
                return [
                    'success' => true,
                    'customer' => $response->getResult()->getCustomer()
                ];
            } else {
                return [
                    'success' => false,
                    'error' => $response->getErrors()[0]->getDetail()
                ];
            }
        } catch (Exception $e) {
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }
    
    /**
     * Search customers
     */
    public function searchCustomers($query = '') {
        if (!$this->squareGateway->isAvailable()) {
            return ['success' => false, 'error' => 'Square not available'];
        }
        
        try {
            $searchQuery = new \Square\Models\CustomerQuery();
            
            if (!empty($query)) {
                $filter = new \Square\Models\CustomerFilter();
                $textFilter = new \Square\Models\CustomerTextFilter();
                $textFilter->setExact($query);
                $filter->setEmailAddress($textFilter);
                $searchQuery->setFilter($filter);
            }
            
            $searchCustomersRequest = new \Square\Models\SearchCustomersRequest();
            $searchCustomersRequest->setQuery($searchQuery);
            
            $response = $this->squareGateway->customersApi->searchCustomers($searchCustomersRequest);
            
            if ($response->isSuccess()) {
                return [
                    'success' => true,
                    'customers' => $response->getResult()->getCustomers() ?? []
                ];
            } else {
                return ['success' => false, 'error' => 'Failed to search customers'];
            }
        } catch (Exception $e) {
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }
    
    // ==================== ORDER MANAGEMENT ====================
    
    /**
     * Create order
     */
    public function createOrder($orderData) {
        if (!$this->squareGateway->isAvailable()) {
            return ['success' => false, 'error' => 'Square not available'];
        }
        
        try {
            $order = new \Square\Models\Order();
            $order->setLocationId($this->config['location_id']);
            
            // Add line items
            $lineItems = [];
            foreach ($orderData['items'] as $item) {
                $lineItem = new \Square\Models\OrderLineItem();
                $lineItem->setQuantity((string)$item['quantity']);
                $lineItem->setCatalogObjectId($item['catalog_object_id']);
                
                if (isset($item['base_price_money'])) {
                    $basePriceMoney = new \Square\Models\Money();
                    $basePriceMoney->setAmount($item['base_price_money'] * 100);
                    $basePriceMoney->setCurrency('USD');
                    $lineItem->setBasePriceMoney($basePriceMoney);
                }
                
                $lineItems[] = $lineItem;
            }
            $order->setLineItems($lineItems);
            
            $createOrderRequest = new \Square\Models\CreateOrderRequest();
            $createOrderRequest->setOrder($order);
            
            $response = $this->squareGateway->ordersApi->createOrder($createOrderRequest);
            
            if ($response->isSuccess()) {
                return [
                    'success' => true,
                    'order' => $response->getResult()->getOrder()
                ];
            } else {
                return [
                    'success' => false,
                    'error' => $response->getErrors()[0]->getDetail()
                ];
            }
        } catch (Exception $e) {
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }
    
    // ==================== INVOICE MANAGEMENT ====================
    
    /**
     * Create invoice
     */
    public function createInvoice($invoiceData) {
        if (!$this->squareGateway->isAvailable()) {
            return ['success' => false, 'error' => 'Square not available'];
        }
        
        try {
            $invoice = new \Square\Models\Invoice();
            
            // Set invoice recipient
            if (isset($invoiceData['primary_recipient'])) {
                $recipient = new \Square\Models\InvoiceRecipient();
                $recipient->setCustomerId($invoiceData['primary_recipient']['customer_id']);
                $invoice->setPrimaryRecipient($recipient);
            }
            
            // Set payment requests
            $paymentRequest = new \Square\Models\InvoicePaymentRequest();
            $paymentRequest->setRequestMethod('EMAIL');
            $paymentRequest->setRequestType('BALANCE');
            $invoice->setPaymentRequests([$paymentRequest]);
            
            // Set delivery method
            $deliveryMethod = new \Square\Models\InvoiceDeliveryMethod();
            $deliveryMethod->setType('EMAIL');
            $invoice->setDeliveryMethod($deliveryMethod);
            
            // Set order
            if (isset($invoiceData['order_id'])) {
                $invoice->setOrderId($invoiceData['order_id']);
            }
            
            $createInvoiceRequest = new \Square\Models\CreateInvoiceRequest($invoice);
            
            $response = $this->squareGateway->invoicesApi->createInvoice($createInvoiceRequest);
            
            if ($response->isSuccess()) {
                return [
                    'success' => true,
                    'invoice' => $response->getResult()->getInvoice()
                ];
            } else {
                return [
                    'success' => false,
                    'error' => $response->getErrors()[0]->getDetail()
                ];
            }
        } catch (Exception $e) {
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }
    
    // ==================== UTILITY METHODS ====================
    
    /**
     * Get business dashboard data
     */
    public function getDashboardData() {
        return [
            'services' => $this->getServices(),
            'recent_customers' => $this->searchCustomers(),
            'square_status' => $this->squareGateway->getStatus(),
            'features_available' => $this->config['features']
        ];
    }
    
    /**
     * Initialize ELOH Processing services in Square catalog
     */
    public function initializeServices() {
        $services = $this->config['business_management']['services'];
        $results = [];
        
        foreach ($services as $serviceId => $serviceData) {
            $serviceData['id'] = $serviceId;
            $result = $this->createService($serviceData);
            $results[$serviceId] = $result;
        }
        
        return $results;
    }
}
?>
