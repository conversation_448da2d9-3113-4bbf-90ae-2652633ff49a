/* 🎨 FUTURISTIC DESIGN SYSTEM - ELOH Processing Static */

/* CSS Variables for Light Theme */
:root {
  /* Primary Brand Colors */
  --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --primary-color: #667eea;
  --primary-dark: #5a67d8;
  --accent-color: #764ba2;
  --accent-light: #9f7aea;

  /* Neutral Colors */
  --background-color: #fafbfc;
  --surface-color: #ffffff;
  --text-primary: #1a202c;
  --text-secondary: #4a5568;
  --text-muted: #718096;
  --border-color: #e2e8f0;
  --border-light: #f7fafc;

  /* Interactive States */
  --hover-bg: #f7fafc;
  --active-bg: #edf2f7;
  --focus-ring: rgba(102, 126, 234, 0.3);

  /* Shadows & Effects */
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 40px rgba(0, 0, 0, 0.1);
  --glow: 0 0 20px rgba(102, 126, 234, 0.3);

  /* Typography */
  --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  --font-mono: 'JetBrains Mono', 'Fira Code', monospace;

  /* Spacing Scale */
  --space-xs: 0.25rem;
  --space-sm: 0.5rem;
  --space-md: 1rem;
  --space-lg: 1.5rem;
  --space-xl: 2rem;
  --space-2xl: 3rem;
  --space-3xl: 4rem;

  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;

  /* Transitions */
  --transition-fast: 0.15s ease-out;
  --transition-base: 0.2s ease-out;
  --transition-slow: 0.3s ease-out;
}

/* Dark Theme Variables */
body.dark-theme {
  --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --primary-color: #7c3aed;
  --primary-dark: #6d28d9;
  --accent-color: #a855f7;
  --accent-light: #c084fc;

  --background-color: #0f0f23;
  --surface-color: #1a1a2e;
  --text-primary: #f7fafc;
  --text-secondary: #e2e8f0;
  --text-muted: #a0aec0;
  --border-color: #2d3748;
  --border-light: #4a5568;

  --hover-bg: #2d3748;
  --active-bg: #4a5568;
  --focus-ring: rgba(124, 58, 237, 0.4);

  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.3);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.3);
  --shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.3);
  --shadow-xl: 0 20px 40px rgba(0, 0, 0, 0.4);
  --glow: 0 0 30px rgba(124, 58, 237, 0.4);
}

/* 🎯 GLOBAL RESET & BASE STYLES */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  scroll-behavior: smooth;
  font-size: 16px;
}

body {
  background: var(--background-color);
  color: var(--text-primary);
  font-family: var(--font-primary);
  font-weight: 400;
  line-height: 1.6;
  margin: 0;
  transition: all var(--transition-slow);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Typography Scale */
h1, h2, h3, h4, h5, h6 {
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: var(--space-md);
  color: var(--text-primary);
}

h1 { font-size: 3.5rem; font-weight: 800; }
h2 { font-size: 2.5rem; font-weight: 700; }
h3 { font-size: 2rem; font-weight: 600; }
h4 { font-size: 1.5rem; font-weight: 600; }
h5 { font-size: 1.25rem; font-weight: 500; }
h6 { font-size: 1rem; font-weight: 500; }

p {
  margin-bottom: var(--space-md);
  color: var(--text-secondary);
}

a {
  color: var(--primary-color);
  text-decoration: none;
  transition: all var(--transition-fast);
}

a:hover {
  color: var(--primary-dark);
  text-decoration: underline;
}

/* 🚀 HEADER DESIGN */
header {
  background: var(--surface-color);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--border-color);
  padding: var(--space-lg) var(--space-xl);
  position: sticky;
  top: 0;
  z-index: 1000;
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-base);
}

.header-container {
  max-width: 1400px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* 🎯 LOGO DESIGN */
.logo {
  display: flex;
  align-items: center;
  gap: var(--space-md);
}

.logo-text {
  font-size: 1.5rem;
  font-weight: 800;
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  letter-spacing: -0.02em;
}

/* 🧭 NAVIGATION DESIGN */
.nav-container {
  display: flex;
  align-items: center;
  gap: var(--space-xl);
}

nav ul {
  display: flex;
  list-style: none;
  gap: var(--space-lg);
  margin: 0;
  padding: 0;
}

nav ul li a {
  color: var(--text-secondary);
  font-weight: 500;
  font-size: 0.95rem;
  padding: var(--space-sm) var(--space-md);
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
  position: relative;
  overflow: hidden;
}

nav ul li a:hover {
  color: var(--primary-color);
  background: var(--hover-bg);
  transform: translateY(-1px);
  text-decoration: none;
}

nav ul li a.active {
  color: var(--primary-color);
  background: var(--active-bg);
  font-weight: 600;
}

/* 🌙 THEME TOGGLE BUTTON */
.theme-toggle {
  background: var(--surface-color);
  border: 1px solid var(--border-color);
  color: var(--text-secondary);
  padding: var(--space-sm) var(--space-md);
  border-radius: var(--radius-lg);
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 500;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  gap: var(--space-xs);
  box-shadow: var(--shadow-sm);
}

.theme-toggle:hover {
  background: var(--hover-bg);
  border-color: var(--primary-color);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

/* 📱 MOBILE RESPONSIVE DESIGN */
.mobile-menu-toggle {
  display: none;
  background: none;
  border: none;
  cursor: pointer;
  padding: var(--space-sm);
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
}

.hamburger {
  display: flex;
  flex-direction: column;
  gap: 3px;
}

.hamburger span {
  width: 24px;
  height: 2px;
  background: var(--text-primary);
  border-radius: 2px;
  transition: all var(--transition-base);
}

/* 🎨 PWA INSTALL BANNER */
.pwa-install-banner {
  background: var(--primary-gradient);
  color: white;
  padding: var(--space-md);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1001;
  box-shadow: var(--shadow-lg);
  animation: slideInDown 0.5s ease-out;
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-100%);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.install-banner-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  gap: var(--space-md);
  position: relative;
}

.install-icon {
  font-size: 1.5rem;
  flex-shrink: 0;
}

.install-text {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--space-xs);
}

.install-text strong {
  font-weight: 600;
}

.install-text span {
  font-size: 0.9rem;
  opacity: 0.9;
}

.install-button {
  background: white;
  color: var(--primary-color);
  border: none;
  padding: var(--space-sm) var(--space-lg);
  border-radius: var(--radius-md);
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-fast);
  flex-shrink: 0;
}

.install-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(255, 255, 255, 0.3);
}

.dismiss-button {
  background: none;
  border: none;
  color: white;
  font-size: 1.2rem;
  cursor: pointer;
  padding: var(--space-xs);
  border-radius: 50%;
  width: 2rem;
  height: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--transition-fast);
}

.dismiss-button:hover {
  background: rgba(255, 255, 255, 0.2);
}

/* 🏠 HERO SECTION */
.hero {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
  overflow: hidden;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: -1;
}

.hero-gradient {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--primary-gradient);
  opacity: 0.1;
}

.hero-pattern {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: radial-gradient(circle at 25% 25%, rgba(102, 126, 234, 0.1) 0%, transparent 50%),
                    radial-gradient(circle at 75% 75%, rgba(118, 75, 162, 0.1) 0%, transparent 50%);
  animation: float 20s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(1deg); }
}

.hero-content {
  max-width: 1400px;
  margin: 0 auto;
  padding: var(--space-3xl) var(--space-xl);
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-3xl);
  align-items: center;
}

.hero-text {
  z-index: 1;
}

.hero-title {
  font-size: 4rem;
  font-weight: 900;
  line-height: 1.1;
  margin-bottom: var(--space-xl);
}

.title-line {
  display: block;
  margin-bottom: var(--space-sm);
}

.gradient-text {
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-description {
  font-size: 1.25rem;
  color: var(--text-secondary);
  margin-bottom: var(--space-2xl);
  line-height: 1.6;
}

.hero-actions {
  display: flex;
  gap: var(--space-lg);
  margin-bottom: var(--space-2xl);
}

.hero-stats {
  display: flex;
  gap: var(--space-xl);
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 2rem;
  font-weight: 800;
  color: var(--primary-color);
  margin-bottom: var(--space-xs);
}

.stat-label {
  font-size: 0.9rem;
  color: var(--text-muted);
  font-weight: 500;
}

/* 🎯 BUTTONS */
.cta-button {
  display: inline-flex;
  align-items: center;
  gap: var(--space-sm);
  padding: var(--space-md) var(--space-xl);
  border-radius: var(--radius-lg);
  font-weight: 600;
  font-size: 1rem;
  text-decoration: none;
  transition: all var(--transition-base);
  border: none;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.cta-button.primary {
  background: var(--primary-gradient);
  color: white;
  box-shadow: var(--shadow-md);
}

.cta-button.primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  text-decoration: none;
}

.cta-button.secondary {
  background: var(--surface-color);
  color: var(--primary-color);
  border: 2px solid var(--primary-color);
}

.cta-button.secondary:hover {
  background: var(--primary-color);
  color: white;
  transform: translateY(-2px);
  text-decoration: none;
}

.cta-button.large {
  padding: var(--space-lg) var(--space-2xl);
  font-size: 1.1rem;
}

.button-icon {
  font-size: 1.2rem;
}

/* 📊 SECTIONS */
.section {
  padding: var(--space-3xl) 0;
}

.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 var(--space-xl);
}

.section-header {
  text-align: center;
  margin-bottom: var(--space-3xl);
}

.section-header h2 {
  font-size: 3rem;
  margin-bottom: var(--space-md);
}

.section-header p {
  font-size: 1.2rem;
  color: var(--text-secondary);
  max-width: 600px;
  margin: 0 auto;
}

/* 📈 STATS GRID */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--space-xl);
}

.stat-card {
  background: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-xl);
  padding: var(--space-xl);
  text-align: center;
  transition: all var(--transition-base);
  box-shadow: var(--shadow-sm);
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
  border-color: var(--primary-color);
}

.stat-card .stat-icon {
  font-size: 3rem;
  margin-bottom: var(--space-lg);
}

.stat-card .stat-value {
  font-size: 2.5rem;
  font-weight: 800;
  color: var(--primary-color);
  margin-bottom: var(--space-sm);
}

.stat-card .stat-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: var(--space-sm);
}

.stat-card .stat-description {
  color: var(--text-muted);
  font-size: 0.95rem;
}

/* 💼 SERVICES GRID */
.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-xl);
}

.service-card {
  background: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-xl);
  padding: var(--space-xl);
  transition: all var(--transition-base);
  box-shadow: var(--shadow-sm);
}

.service-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
  border-color: var(--primary-color);
}

.service-card .service-icon {
  font-size: 3rem;
  margin-bottom: var(--space-lg);
}

.service-card h3 {
  margin-bottom: var(--space-md);
}

.service-card p {
  color: var(--text-muted);
  margin-bottom: var(--space-lg);
}

.service-price {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: var(--space-lg);
}

.service-link {
  color: var(--primary-color);
  font-weight: 600;
  text-decoration: none;
  transition: all var(--transition-fast);
}

.service-link:hover {
  color: var(--primary-dark);
}

/* 💰 INVESTMENT GRID */
.investment-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: var(--space-xl);
}

.investment-card {
  background: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-xl);
  padding: var(--space-xl);
  transition: all var(--transition-base);
  box-shadow: var(--shadow-sm);
  position: relative;
}

.investment-card.featured {
  border-color: var(--primary-color);
  box-shadow: var(--shadow-lg);
  transform: scale(1.05);
}

.investment-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.investment-card.featured:hover {
  transform: scale(1.05) translateY(-4px);
}

.investment-badge {
  position: absolute;
  top: -10px;
  right: var(--space-lg);
  background: var(--primary-gradient);
  color: white;
  padding: var(--space-xs) var(--space-md);
  border-radius: var(--radius-lg);
  font-size: 0.8rem;
  font-weight: 600;
}

.investment-card .investment-icon {
  font-size: 3rem;
  margin-bottom: var(--space-lg);
}

.investment-card h3 {
  margin-bottom: var(--space-md);
}

.investment-amount {
  font-size: 2rem;
  font-weight: 800;
  color: var(--primary-color);
  margin-bottom: var(--space-lg);
}

.investment-card p {
  color: var(--text-muted);
  margin-bottom: var(--space-lg);
}

.investment-features {
  list-style: none;
  margin-bottom: var(--space-xl);
}

.investment-features li {
  padding: var(--space-xs) 0;
  color: var(--text-secondary);
  font-size: 0.95rem;
}

.investment-button {
  display: block;
  width: 100%;
  text-align: center;
  background: var(--primary-gradient);
  color: white;
  padding: var(--space-md);
  border-radius: var(--radius-lg);
  font-weight: 600;
  text-decoration: none;
  transition: all var(--transition-base);
}

.investment-button:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  text-decoration: none;
}

/* 🎯 CTA SECTION */
.cta-section {
  background: var(--primary-gradient);
  color: white;
}

.cta-content {
  text-align: center;
  max-width: 800px;
  margin: 0 auto;
}

.cta-content h2 {
  color: white;
  font-size: 3rem;
  margin-bottom: var(--space-md);
}

.cta-content p {
  font-size: 1.25rem;
  margin-bottom: var(--space-2xl);
  opacity: 0.9;
}

.cta-actions {
  display: flex;
  gap: var(--space-lg);
  justify-content: center;
}

.cta-section .cta-button.primary {
  background: white;
  color: var(--primary-color);
}

.cta-section .cta-button.secondary {
  background: transparent;
  color: white;
  border-color: white;
}

.cta-section .cta-button.secondary:hover {
  background: white;
  color: var(--primary-color);
}

/* 🦶 FOOTER */
footer {
  background: var(--surface-color);
  border-top: 1px solid var(--border-color);
  padding: var(--space-3xl) 0 var(--space-xl);
}

.footer-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 var(--space-xl);
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-xl);
  margin-bottom: var(--space-2xl);
}

.footer-section h4,
.footer-section h5 {
  margin-bottom: var(--space-md);
  color: var(--text-primary);
}

.footer-section ul {
  list-style: none;
}

.footer-section ul li {
  margin-bottom: var(--space-sm);
}

.footer-section ul li a {
  color: var(--text-muted);
  text-decoration: none;
  transition: all var(--transition-fast);
}

.footer-section ul li a:hover {
  color: var(--primary-color);
}

.social-links {
  display: flex;
  flex-direction: column;
  gap: var(--space-sm);
}

.social-link {
  color: var(--text-muted);
  text-decoration: none;
  transition: all var(--transition-fast);
}

.social-link:hover {
  color: var(--primary-color);
}

.footer-bottom {
  text-align: center;
  padding-top: var(--space-xl);
  border-top: 1px solid var(--border-color);
  color: var(--text-muted);
}

.footer-bottom p {
  margin-bottom: var(--space-sm);
}

/* 📱 RESPONSIVE DESIGN */
@media (max-width: 768px) {
  .mobile-menu-toggle {
    display: block;
  }

  .nav-container {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: var(--surface-color);
    border-top: 1px solid var(--border-color);
    box-shadow: var(--shadow-lg);
    transform: translateY(-100%);
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-base);
    z-index: 999;
  }

  .nav-container.active {
    transform: translateY(0);
    opacity: 1;
    visibility: visible;
  }

  nav ul {
    flex-direction: column;
    padding: var(--space-lg);
    gap: var(--space-sm);
  }

  nav ul li a {
    display: block;
    padding: var(--space-md);
    border-radius: var(--radius-md);
    text-align: center;
  }

  .theme-toggle {
    margin: var(--space-md) var(--space-lg);
  }

  .hero-content {
    grid-template-columns: 1fr;
    gap: var(--space-xl);
    text-align: center;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .hero-actions {
    flex-direction: column;
    align-items: center;
  }

  .hero-stats {
    justify-content: center;
  }

  .cta-actions {
    flex-direction: column;
    align-items: center;
  }

  .install-banner-content {
    flex-direction: column;
    text-align: center;
    gap: var(--space-sm);
  }

  .install-text {
    order: -1;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 var(--space-md);
  }

  .hero-content {
    padding: var(--space-2xl) var(--space-md);
  }

  .section-header h2 {
    font-size: 2rem;
  }

  .hero-title {
    font-size: 2rem;
  }

  .stats-grid,
  .services-grid,
  .investment-grid {
    grid-template-columns: 1fr;
  }
}

/* 💳 PAYMENT WIDGET SECTION */
.payment-section {
  background: var(--surface-color);
  padding: var(--space-3xl) 0;
}

.payment-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-3xl);
  align-items: start;
  margin-bottom: var(--space-3xl);
}

.payment-info h3 {
  color: var(--text-primary);
  margin-bottom: var(--space-lg);
  font-size: 1.5rem;
  font-weight: 600;
}

.payment-info p {
  color: var(--text-secondary);
  margin-bottom: var(--space-xl);
  line-height: 1.7;
}

.payment-features {
  display: grid;
  gap: var(--space-md);
  margin-bottom: var(--space-xl);
}

.feature-item {
  display: flex;
  align-items: center;
  gap: var(--space-md);
  padding: var(--space-md);
  background: var(--hover-bg);
  border-radius: var(--radius-lg);
  transition: all var(--transition-base);
}

.feature-item:hover {
  background: var(--active-bg);
  transform: translateX(4px);
}

.feature-icon {
  font-size: 1.25rem;
  width: 2rem;
  text-align: center;
}

.feature-text {
  font-weight: 500;
  color: var(--text-primary);
}

.integration-note {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
  padding: var(--space-lg);
  border-radius: var(--radius-lg);
  border-left: 4px solid var(--primary-color);
}

.integration-note p {
  margin: 0;
  color: var(--text-primary);
}

.payment-widget-container {
  position: relative;
}

.widget-frame {
  background: white;
  border-radius: var(--radius-xl);
  overflow: hidden;
  box-shadow: var(--shadow-xl);
  border: 1px solid var(--border-color);
}

.widget-info {
  margin-top: var(--space-lg);
  text-align: center;
}

.widget-demo-note {
  color: var(--text-muted);
  font-size: 0.9rem;
  margin: 0;
}

.demo-badge {
  background: linear-gradient(135deg, #fbbf24, #f59e0b);
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: var(--radius-sm);
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  margin-right: var(--space-xs);
  box-shadow: 0 2px 4px rgba(251, 191, 36, 0.3);
}

.integration-examples {
  margin-top: var(--space-3xl);
  padding-top: var(--space-3xl);
  border-top: 1px solid var(--border-color);
}

.integration-examples h3 {
  color: var(--text-primary);
  margin-bottom: var(--space-xl);
  font-size: 1.5rem;
  font-weight: 600;
  text-align: center;
}

.code-examples {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: var(--space-xl);
}

.code-example h4 {
  color: var(--text-primary);
  margin-bottom: var(--space-md);
  font-size: 1.1rem;
  font-weight: 600;
}

.code-block {
  background: #2d3748;
  color: #e2e8f0;
  padding: var(--space-lg);
  border-radius: var(--radius-lg);
  overflow-x: auto;
  font-family: var(--font-mono);
  font-size: 0.875rem;
  line-height: 1.6;
  border: 1px solid var(--border-color);
}

.code-block code {
  white-space: pre;
  font-family: inherit;
}

/* Responsive Design for Payment Section */
@media (max-width: 768px) {
  .payment-content {
    grid-template-columns: 1fr;
    gap: var(--space-xl);
  }

  .code-examples {
    grid-template-columns: 1fr;
  }

  .widget-frame iframe {
    height: 500px;
  }

  .payment-info {
    order: 2;
  }

  .payment-widget-container {
    order: 1;
  }
}
