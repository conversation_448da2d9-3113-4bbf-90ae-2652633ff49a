#!/bin/bash

# ELOH Processing Payment Gateway - Render Build Script

set -e  # Exit on any error

echo "🚀 Starting ELOH Processing Payment Gateway build..."

# Install Python dependencies
echo "📦 Installing Python dependencies..."
pip install --upgrade pip
pip install -r requirements.txt

# Verify installation
echo "🔍 Verifying installation..."
python -c "import fastapi; print(f'FastAPI version: {fastapi.__version__}')"
python -c "import sqlalchemy; print(f'SQLAlchemy version: {sqlalchemy.__version__}')"

# Run database setup
echo "🗄️ Setting up database..."
python setup_database.py --sample-data

# Verify database
echo "✅ Verifying database setup..."
python setup_database.py --info

# Run basic tests
echo "🧪 Running basic tests..."
python -c "
import asyncio
from app.core.config import get_settings
from app.database.connection import get_database_manager

async def test():
    settings = get_settings()
    print(f'App: {settings.app_name}')
    print(f'Environment: {settings.environment}')
    
    db_manager = get_database_manager()
    health = db_manager.health_check()
    print(f'Database Health: {\"✅ Healthy\" if health else \"❌ Unhealthy\"}')

asyncio.run(test())
"

echo "🎉 Build completed successfully!"
echo "📊 Build Summary:"
echo "   - Dependencies installed"
echo "   - Database initialized"
echo "   - Sample data created"
echo "   - Health checks passed"

# Display important information
echo ""
echo "🔧 Post-deployment setup required:"
echo "   1. Set gateway credentials in Render dashboard"
echo "   2. Configure custom domain (optional)"
echo "   3. Set up monitoring and alerts"
echo "   4. Test API endpoints"
echo ""
echo "📚 Useful endpoints after deployment:"
echo "   - API Docs: https://your-app.onrender.com/docs"
echo "   - Tenant Portal: https://your-app.onrender.com/v1/portal"
echo "   - Health Check: https://your-app.onrender.com/health"
echo "   - Gateway Status: https://your-app.onrender.com/v1/portal/status"
