# TinyHost Deployment Guide
## ELOH Processing LLC - Complete Deployment Package

### 🎯 **DEPLOYMENT PACKAGE READY**

This folder contains a clean, production-ready deployment of your ELOH Processing website optimized for TinyHost hosting.

### 📁 **DEPLOYMENT STRUCTURE:**

```
ELOH_TinyHost_Deployment/
├── Core Website Pages
│   ├── index.php ✅ (Homepage)
│   ├── about.php ✅ (About page)
│   ├── services.php ✅ (Services page)
│   ├── investors.php ✅ (Investors page)
│   ├── operations.php ✅ (Operations page)
│   ├── contact.php ✅ (Contact page)
│   ├── header.php ✅ (Site header)
│   ├── footer.php ✅ (Site footer)
│   └── error.php ✅ (Error handling)
├── Multi-Gateway Payment System
│   ├── multi-gateway-payment-form.php ✅ (Unified payment form)
│   ├── multi-gateway-process-payment.php ✅ (Payment processor)
│   ├── nowpayments-checkout.php ✅ (NowPayments checkout)
│   ├── nowpayments-status.php ✅ (Payment status API)
│   └── nowpayments-test.php ✅ (Testing page)
├── BTCPay Server Integration
│   ├── btcpay-payment-form.php ✅ (BTCPay payment form)
│   ├── btcpay-process-payment.php ✅ (BTCPay processor)
│   ├── btcpay-webhook.php ✅ (Webhook handler)
│   ├── btcpay-test.php ✅ (Testing page)
│   └── btcpay-troubleshoot.php ✅ (Diagnostics)
├── Payment Success & Support
│   ├── payment-success.php ✅ (Success page)
│   └── payment-cancelled.php ✅ (Cancellation page)
├── Configuration (includes/ folder)
│   ├── btcpay-config.php ✅ (BTCPay settings)
│   ├── btcpay-gateway.php ✅ (BTCPay API client)
│   ├── nowpayments-config.php ✅ (NowPayments settings)
│   ├── nowpayments-gateway.php ✅ (NowPayments API client)
│   └── payment-gateway-manager.php ✅ (Multi-gateway manager)
└── Documentation
    ├── TINYHOST_DEPLOYMENT_GUIDE.md ✅ (This file)
    ├── NOWPAYMENTS_LIVE_SETUP_GUIDE.md ✅ (NowPayments setup)
    └── MINIMUM_AMOUNTS_INFO.md ✅ (Payment minimums info)
```

### 🚀 **FEATURES INCLUDED:**

#### **Multi-Gateway Payment System:**
- ⚡ **BTCPay Server** - Self-hosted Bitcoin payments
- 🌐 **NowPayments** - 300+ cryptocurrencies
- 🎯 **Customer Choice** - Gateway selection interface
- 📱 **Mobile Optimized** - Responsive design

#### **Payment Methods:**
- **Bitcoin Lightning Network** (instant, no fees)
- **On-chain Bitcoin** payments
- **300+ Cryptocurrencies** via NowPayments
- **Professional checkout** experiences

#### **Security Features:**
- ✅ **API Authentication** for both gateways
- ✅ **Webhook Validation** with HMAC signatures
- ✅ **Input Validation** and sanitization
- ✅ **Error Handling** with logging

### 🔧 **PRE-DEPLOYMENT CONFIGURATION:**

#### **1. Update Domain References:**
Before uploading, you need to update domain references from InfinityFree to TinyHost.

**Files to update:**
- `includes/btcpay-config.php` - Update webhook URL
- `includes/nowpayments-gateway.php` - Update callback URLs
- `btcpay-gateway.php` - Update return URLs

**Find and replace:**
- `elohprocessing.infy.uk` → `your-tinyhost-domain.com`

#### **2. Configure API Credentials:**

**BTCPay Server** (`includes/btcpay-config.php`):
```php
'host' => 'https://mainnet.demo.btcpayserver.org',
'api_key' => '12bc889ef6802d08dccd36b803579516a0f039ba',
'store_id' => 'DzeKFNoYV91vVvp2wf5PXUp33P4EvurrfVGvbPWd8RQS',
'webhook_url' => 'https://your-tinyhost-domain.com/btcpay-webhook.php',
```

**NowPayments** (`includes/nowpayments-config.php`):
```php
'live_api_key' => 'your_nowpayments_api_key',
'ipn_secret' => 'your_ipn_secret',
```

### 📤 **DEPLOYMENT STEPS:**

#### **Step 1: Upload Files**
1. **Compress** this entire folder to ZIP
2. **Upload** to TinyHost file manager
3. **Extract** in your domain's root directory
4. **Set permissions** (755 for directories, 644 for files)

#### **Step 2: Update Domain References**
1. **Edit** configuration files with your TinyHost domain
2. **Test** file access via browser
3. **Verify** all pages load correctly

#### **Step 3: Configure Payment Gateways**
1. **BTCPay Server**: Update webhook URL in BTCPay dashboard
2. **NowPayments**: Add your API credentials
3. **Test** both payment systems

#### **Step 4: Test Complete System**
1. **Visit** `multi-gateway-payment-form.php`
2. **Test** BTCPay Server payments
3. **Test** NowPayments with different cryptocurrencies
4. **Verify** webhooks and notifications work

### 🧪 **TESTING CHECKLIST:**

- [ ] All pages load without errors
- [ ] Payment form displays correctly
- [ ] BTCPay Server integration works
- [ ] NowPayments API connection successful
- [ ] Payment processing completes
- [ ] Webhooks receive notifications
- [ ] Success/error pages display properly
- [ ] Mobile responsiveness verified

### 🎯 **TINYHOST SPECIFIC NOTES:**

#### **File Permissions:**
- **Directories**: 755
- **PHP Files**: 644
- **Configuration Files**: 600 (if supported)

#### **PHP Requirements:**
- **PHP 7.4+** (recommended 8.0+)
- **cURL extension** (for API calls)
- **JSON extension** (for data processing)
- **OpenSSL** (for webhook validation)

#### **Database:**
- **Not required** - System uses session-based tracking
- **Optional**: Add database logging for payments

### 🔒 **SECURITY RECOMMENDATIONS:**

1. **Protect Configuration Files:**
   - Move sensitive configs outside web root if possible
   - Use environment variables for API keys

2. **Enable HTTPS:**
   - Essential for payment processing
   - Required by both BTCPay and NowPayments

3. **Regular Updates:**
   - Keep PHP updated
   - Monitor API changes
   - Update webhook URLs if domain changes

### 📞 **SUPPORT RESOURCES:**

- **BTCPay Server**: https://docs.btcpayserver.org/
- **NowPayments**: https://documenter.getpostman.com/view/7907941/S1a32n38
- **TinyHost Support**: Check their documentation for PHP/hosting specifics

### 🎉 **READY FOR PRODUCTION:**

This deployment package includes:
- ✅ **Complete multi-gateway payment system**
- ✅ **Professional website design**
- ✅ **Mobile-optimized interface**
- ✅ **Comprehensive error handling**
- ✅ **Security best practices**
- ✅ **Testing and diagnostic tools**

**Your ELOH Processing website is ready for professional cryptocurrency payment processing on TinyHost! 🚀**
