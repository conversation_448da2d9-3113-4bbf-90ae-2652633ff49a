#!/usr/bin/env python3
"""
Simple startup script for ELOH Processing Payment Gateway

This script handles common startup tasks and launches the development server.
"""

import os
import sys
import subprocess
from pathlib import Path

def main():
    """Main startup function"""
    print("🚀 ELOH Processing Payment Gateway - Startup")
    print("=" * 50)
    
    # Check if we're in the right directory
    if not Path("main.py").exists():
        print("❌ Error: main.py not found!")
        print("💡 Make sure you're in the api-gateway directory")
        print("   Command: cd api-gateway")
        return 1
    
    # Check if requirements are installed
    try:
        import fastapi
        print("✅ FastAPI found")
    except ImportError:
        print("📦 Installing dependencies...")
        try:
            subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], check=True)
            print("✅ Dependencies installed")
        except subprocess.CalledProcessError:
            print("❌ Failed to install dependencies")
            print("💡 Try manually: pip install -r requirements.txt")
            return 1
    
    # Check for .env file
    if not Path(".env").exists():
        print("📝 Creating .env file...")
        env_content = """APP_NAME=ELOH Processing Payment Gateway
ENVIRONMENT=development
DEBUG=true
SECRET_KEY=dev-secret-key-change-in-production
DATABASE_URL=sqlite:///./eloh_gateway.db
LOG_LEVEL=DEBUG
"""
        with open(".env", "w") as f:
            f.write(env_content)
        print("✅ .env file created")
    
    # Setup database
    if not Path("eloh_gateway.db").exists():
        print("🗄️ Setting up database...")
        try:
            subprocess.run([sys.executable, "setup_database.py", "--sample-data"], check=True)
            print("✅ Database setup complete")
        except subprocess.CalledProcessError:
            print("⚠️ Database setup had issues, but continuing...")
    
    # Start the server
    print("\n🌐 Starting development server...")
    print("📚 API Docs: http://localhost:8000/docs")
    print("🏢 Tenant Portal: http://localhost:8000/v1/portal")
    print("❤️ Health Check: http://localhost:8000/health")
    print("\n🛑 Press Ctrl+C to stop the server")
    print("-" * 50)
    
    try:
        # Start uvicorn
        subprocess.run([
            sys.executable, "-m", "uvicorn", 
            "main:app", 
            "--reload", 
            "--host", "0.0.0.0", 
            "--port", "8000"
        ])
    except KeyboardInterrupt:
        print("\n👋 Server stopped")
    except FileNotFoundError:
        print("❌ uvicorn not found")
        print("💡 Install with: pip install uvicorn")
        return 1

if __name__ == "__main__":
    sys.exit(main())
