"""
Abstract Gateway Adapter Interface for ELOH Processing Payment Gateway API

This module defines the standard interface that all payment gateway adapters must implement.
It uses the Adapter Pattern to provide a unified interface across different payment providers.

Future AI Integration Note:
The adapter interface is designed to be AI-friendly, with standardized inputs/outputs
that can be easily analyzed and routed by future AI systems. The rule-based routing
will serve as a reliable fallback when AI routing is implemented.
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List
from datetime import datetime

from ..models.payment import PaymentRequest, PaymentResponse, RefundRequest, RefundResponse
from ..models.customer import CustomerRequest, CustomerResponse


class GatewayCredentials(ABC):
    """Abstract base class for gateway credentials"""

    @abstractmethod
    def is_valid(self) -> bool:
        """Validate that credentials are properly configured"""
        pass

    @abstractmethod
    def to_dict(self) -> Dict[str, Any]:
        """Convert credentials to dictionary (for secure storage)"""
        pass


class GatewayAdapter(ABC):
    """
    Abstract base class for all payment gateway adapters.

    This interface ensures consistency across all gateway implementations
    and provides a standard way for the routing system (both rule-based
    and future AI-driven) to interact with different payment providers.

    All methods are async to support high-performance concurrent processing.
    """

    def __init__(self, credentials: GatewayCredentials, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the gateway adapter with credentials and configuration.

        Args:
            credentials: Gateway-specific credentials (API keys, secrets, etc.)
            config: Optional configuration parameters
        """
        self.credentials = credentials
        self.config = config or {}
        self.gateway_name = self._get_gateway_name()

    @abstractmethod
    def _get_gateway_name(self) -> str:
        """Return the gateway name (e.g., 'stripe', 'square', 'btcpay', 'nowpayments')"""
        pass

    @abstractmethod
    async def validate_credentials(self) -> bool:
        """
        Validate that the provided credentials are valid and active.

        Returns:
            bool: True if credentials are valid, False otherwise
        """
        pass

    @abstractmethod
    async def process_payment(self, payment_request: PaymentRequest, customer_id: Optional[str] = None) -> PaymentResponse:
        """
        Process a payment through this gateway.

        This method should:
        1. Translate the standard PaymentRequest to gateway-specific format
        2. Make the API call to the gateway
        3. Handle gateway-specific errors and translate to standard exceptions
        4. Translate the gateway response back to standard PaymentResponse

        Args:
            payment_request: Standard payment request model
            customer_id: Optional customer ID for this gateway

        Returns:
            PaymentResponse: Standard payment response model

        Raises:
            PaymentGatewayException: For any payment processing errors
        """
        pass

    @abstractmethod
    async def get_payment_status(self, gateway_payment_id: str) -> PaymentResponse:
        """
        Retrieve the current status of a payment from the gateway.

        Args:
            gateway_payment_id: Gateway-specific payment identifier

        Returns:
            PaymentResponse: Current payment status and details

        Raises:
            PaymentGatewayException: If payment not found or other errors
        """
        pass

    @abstractmethod
    async def refund_payment(self, refund_request: RefundRequest, gateway_payment_id: str) -> RefundResponse:
        """
        Process a refund for a payment through this gateway.

        Args:
            refund_request: Standard refund request model
            gateway_payment_id: Gateway-specific payment identifier

        Returns:
            RefundResponse: Standard refund response model

        Raises:
            PaymentGatewayException: For any refund processing errors
        """
        pass

    @abstractmethod
    async def create_customer(self, customer_request: CustomerRequest) -> CustomerResponse:
        """
        Create a customer in the gateway system.

        Args:
            customer_request: Standard customer creation request

        Returns:
            CustomerResponse: Standard customer response with gateway customer ID

        Raises:
            PaymentGatewayException: For any customer creation errors
        """
        pass

    @abstractmethod
    async def get_customer(self, gateway_customer_id: str) -> CustomerResponse:
        """
        Retrieve customer details from the gateway.

        Args:
            gateway_customer_id: Gateway-specific customer identifier

        Returns:
            CustomerResponse: Customer details

        Raises:
            PaymentGatewayException: If customer not found or other errors
        """
        pass

    @abstractmethod
    async def process_webhook(self, webhook_data: Dict[str, Any], signature: Optional[str] = None) -> Dict[str, Any]:
        """
        Process and validate a webhook from this gateway.

        This method should:
        1. Verify the webhook signature (if applicable)
        2. Parse the webhook payload
        3. Translate gateway-specific events to standard format
        4. Return standardized event data

        Args:
            webhook_data: Raw webhook payload from the gateway
            signature: Optional webhook signature for verification

        Returns:
            Dict[str, Any]: Standardized webhook event data

        Raises:
            PaymentGatewayException: For invalid webhooks or verification failures
        """
        pass

    # Optional methods that gateways can implement if supported

    async def cancel_payment(self, gateway_payment_id: str) -> PaymentResponse:
        """
        Cancel a pending payment (if supported by the gateway).

        Args:
            gateway_payment_id: Gateway-specific payment identifier

        Returns:
            PaymentResponse: Updated payment status

        Raises:
            NotImplementedError: If gateway doesn't support cancellation
            PaymentGatewayException: For any cancellation errors
        """
        raise NotImplementedError(f"Payment cancellation not supported by {self.gateway_name}")

    async def get_supported_currencies(self) -> List[str]:
        """
        Get list of currencies supported by this gateway.

        Returns:
            List[str]: List of supported currency codes
        """
        # Default implementation - gateways should override with actual supported currencies
        return ["USD"]

    async def get_supported_payment_methods(self) -> List[str]:
        """
        Get list of payment methods supported by this gateway.

        Returns:
            List[str]: List of supported payment method types
        """
        # Default implementation - gateways should override with actual supported methods
        return ["card"]

    async def estimate_fees(self, amount: float, currency: str, payment_method: str) -> Dict[str, float]:
        """
        Estimate fees for a payment (if supported by the gateway).

        Args:
            amount: Payment amount
            currency: Payment currency
            payment_method: Payment method type

        Returns:
            Dict[str, float]: Fee breakdown
        """
        # Default implementation - gateways can override with actual fee calculation
        return {"gateway_fee": 0.0, "processing_fee": 0.0, "total_fee": 0.0}

    def get_gateway_info(self) -> Dict[str, Any]:
        """
        Get information about this gateway adapter.

        Returns:
            Dict[str, Any]: Gateway information for routing decisions
        """
        return {
            "name": self.gateway_name,
            "type": self.__class__.__name__,
            "config": self.config,
            "credentials_valid": self.credentials.is_valid() if self.credentials else False
        }

    def __str__(self) -> str:
        """String representation of the adapter"""
        return f"{self.__class__.__name__}({self.gateway_name})"

    def __repr__(self) -> str:
        """Detailed string representation of the adapter"""
        return f"{self.__class__.__name__}(gateway='{self.gateway_name}', config={self.config})"
