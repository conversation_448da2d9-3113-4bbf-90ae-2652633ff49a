<?php
// BTCPay Server URL Construction Test
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once "includes/btcpay-gateway.php";

echo "<h1>BTCPay Server URL Construction Test</h1>";

try {
    $gateway = new BTCPay_Gateway();
    $debug = $gateway->getDebugInfo();
    
    echo "<h2>Configuration Check:</h2>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    foreach ($debug as $key => $value) {
        $status = is_bool($value) ? ($value ? '✅ Yes' : '❌ No') : htmlspecialchars($value);
        echo "<tr>";
        echo "<td style='padding: 8px; font-weight: bold;'>" . ucfirst(str_replace('_', ' ', $key)) . ":</td>";
        echo "<td style='padding: 8px;'>$status</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Test URL construction manually
    echo "<h2>URL Construction Test:</h2>";
    
    $config = require "includes/btcpay-config.php";
    $host = $config['host'];
    $storeId = $config['store_id'];
    
    echo "<p><strong>Raw Store ID:</strong> " . htmlspecialchars($storeId) . "</p>";
    echo "<p><strong>URL Encoded Store ID:</strong> " . htmlspecialchars(urlencode($storeId)) . "</p>";
    
    $testUrl = $host . '/api/v1/stores/' . urlencode($storeId) . '/invoices';
    echo "<p><strong>Constructed URL:</strong> " . htmlspecialchars($testUrl) . "</p>";
    
    // Validate the URL
    if (filter_var($testUrl, FILTER_VALIDATE_URL)) {
        echo "<p style='color: green;'>✅ URL is valid!</p>";
    } else {
        echo "<p style='color: red;'>❌ URL is invalid!</p>";
    }
    
    // Test with a simple API call (just to see if URL works)
    echo "<h2>Simple API Test:</h2>";
    
    if (isset($_POST['test_api'])) {
        echo "<p>Testing API connection...</p>";
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $testUrl);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Authorization: token ' . $config['api_key'],
            'Content-Type: application/json',
            'Accept: application/json'
        ]);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        curl_setopt($ch, CURLOPT_USERAGENT, 'ELOH-URL-Test/1.0');
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        echo "<h3>API Response:</h3>";
        echo "<p><strong>HTTP Code:</strong> $httpCode</p>";
        
        if ($error) {
            echo "<p style='color: red;'><strong>cURL Error:</strong> " . htmlspecialchars($error) . "</p>";
        } else {
            echo "<p style='color: green;'>✅ No cURL errors</p>";
        }
        
        if ($httpCode == 200) {
            echo "<p style='color: green;'>✅ API connection successful!</p>";
            echo "<p><strong>Response:</strong> " . htmlspecialchars(substr($response, 0, 500)) . "...</p>";
        } elseif ($httpCode == 401) {
            echo "<p style='color: orange;'>⚠️ Authentication error (401) - Check API key</p>";
        } elseif ($httpCode == 403) {
            echo "<p style='color: orange;'>⚠️ Permission error (403) - Check API permissions</p>";
        } elseif ($httpCode == 404) {
            echo "<p style='color: orange;'>⚠️ Not found (404) - Check Store ID</p>";
        } else {
            echo "<p style='color: red;'>❌ HTTP Error: $httpCode</p>";
            echo "<p><strong>Response:</strong> " . htmlspecialchars($response) . "</p>";
        }
    } else {
        echo "<form method='POST'>";
        echo "<button type='submit' name='test_api' style='background: #0077cc; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;'>Test API Connection</button>";
        echo "</form>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<hr>";
echo "<p><a href='btcpay-troubleshoot.php'>← Back to Troubleshooting</a> | <a href='index.php'>Homepage</a></p>";
?>
