# 🚀 ELOH Processing Payment Gateway - Deployment Guide

## 📋 Overview

This guide covers deploying the ELOH Processing Multi-Tenant Payment Gateway API with proper database setup, regional gateway availability, and tenant portal.

## 🏗️ Architecture Summary

```
┌─────────────────────────────────────────────────────────────────┐
│                    ELOH Processing Platform                     │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐              │
│  │   Tenant A  │  │   Tenant B  │  │   Tenant C  │   ...        │
│  │ (E-commerce)│  │   (SaaS)    │  │  (Crypto)   │              │
│  └─────────────┘  └─────────────┘  └─────────────┘              │
├─────────────────────────────────────────────────────────────────┤
│                    Tenant Portal & API                          │
│  • Web Interface  • API Docs  • Testing Tools                  │
├─────────────────────────────────────────────────────────────────┤
│                    Regional Gateway Availability                │
│  ✅ BTCPay (Global)  ✅ NowPayments (Global)                   │
│  ⚠️  Stripe (Limited)  ⚠️  Square (Limited)                    │
├─────────────────────────────────────────────────────────────────┤
│                    Database Layer                               │
│  • Tenant Management  • Gateway Configs  • Payments           │
└─────────────────────────────────────────────────────────────────┘
```

## 🛠️ Prerequisites

### System Requirements
- Python 3.11+
- SQLite (development) or PostgreSQL/MySQL (production)
- Redis (optional, for caching)
- 2GB+ RAM
- 10GB+ disk space

### Gateway Requirements
- **BTCPay Server**: Self-hosted instance or hosted service
- **NowPayments**: API account and credentials
- **Stripe**: Account (limited in Dominica)
- **Square**: Account (limited in Dominica)

## 📦 Installation

### 1. Clone and Setup

```bash
# Clone repository
git clone https://github.com/elohprocessing/payment-gateway-api.git
cd payment-gateway-api

# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt
```

### 2. Environment Configuration

```bash
# Copy environment template
cp .env.example .env

# Edit configuration
nano .env
```

**Key Configuration:**

```env
# Application
APP_NAME="ELOH Processing Payment Gateway"
ENVIRONMENT=production
DEBUG=false

# Database (choose one)
DATABASE_URL=sqlite:///./eloh_gateway.db  # Development
# DATABASE_URL=postgresql://user:pass@localhost/eloh_gateway  # Production

# Security
SECRET_KEY=your-super-secret-key-change-this-in-production

# Available Gateways (configure only what you have)
# BTCPay Server (Recommended for Dominica)
BTCPAY_SERVER_URL=https://your-btcpay-server.com
BTCPAY_API_KEY=your_btcpay_api_key
BTCPAY_STORE_ID=your_btcpay_store_id

# NowPayments (Recommended for Dominica)
NOWPAYMENTS_API_KEY=your_nowpayments_api_key
NOWPAYMENTS_ENVIRONMENT=production

# Stripe (Limited availability)
# STRIPE_SECRET_KEY=sk_live_your_stripe_key
# STRIPE_PUBLISHABLE_KEY=pk_live_your_stripe_key

# Square (Limited availability)
# SQUARE_ACCESS_TOKEN=your_square_access_token
# SQUARE_APPLICATION_ID=your_square_app_id
```

### 3. Database Setup

```bash
# Initialize database and create sample data
python setup_database.py --sample-data

# Or just initialize without sample data
python setup_database.py

# Check database status
python setup_database.py --info
```

### 4. Start the API

```bash
# Development mode
uvicorn main:app --reload --host 0.0.0.0 --port 8000

# Production mode
uvicorn main:app --host 0.0.0.0 --port 8000 --workers 4
```

## 🌐 Regional Gateway Availability

### Available in Dominica ✅

#### BTCPay Server
- **Status**: Fully Available
- **Currencies**: BTC, Lightning Network, USD (via conversion)
- **Setup**: Self-hosted or use hosted service
- **Benefits**: No regional restrictions, full control

#### NowPayments
- **Status**: Fully Available  
- **Currencies**: 300+ cryptocurrencies
- **Setup**: API account required
- **Benefits**: Global coverage, instant settlements

### Limited in Dominica ⚠️

#### Stripe
- **Status**: Not officially supported in Dominica
- **Workaround**: May work with international business setup
- **Recommendation**: Use BTCPay/NowPayments instead

#### Square
- **Status**: Not available in Dominica
- **Alternative**: Focus on crypto gateways

## 🏢 Tenant Management

### Creating Tenants (Admin)

```bash
curl -X POST "http://localhost:8000/v1/tenants" \
  -H "Authorization: Admin eloh_admin_token_123" \
  -H "Content-Type: application/json" \
  -d '{
    "company_name": "Caribbean E-Store",
    "business_type": "E-commerce",
    "contact_email": "<EMAIL>",
    "contact_name": "Store Admin",
    "country": "DM",
    "plan": "professional"
  }'
```

### Tenant Portal Access

1. **Portal URL**: `http://localhost:8000/v1/portal`
2. **API Docs**: `http://localhost:8000/docs`
3. **Authentication**: Use tenant API key

### Gateway Configuration

Tenants can configure available gateways:

```bash
curl -X POST "http://localhost:8000/v1/tenants/me/gateways" \
  -H "Authorization: Bearer tenant_api_key" \
  -d '{
    "gateway_id": "btcpay",
    "enabled": true,
    "credentials": {
      "server_url": "https://btcpay.example.com",
      "api_key": "your_api_key",
      "store_id": "your_store_id"
    }
  }'
```

## 💳 Payment Processing

### Create Payment

```bash
curl -X POST "http://localhost:8000/v1/payments" \
  -H "Authorization: Bearer tenant_api_key" \
  -d '{
    "amount": 100.00,
    "currency": "USD",
    "description": "Order #12345",
    "email": "<EMAIL>"
  }'
```

### Gateway Routing

The system automatically routes payments based on:

1. **Regional Availability**: Prefers available gateways
2. **Currency Support**: Matches currency to gateway
3. **Tenant Configuration**: Uses tenant preferences
4. **Amount Limits**: Respects gateway limits

**Routing Priority for Dominica:**
1. BTCPay Server (for BTC/USD)
2. NowPayments (for crypto)
3. Stripe (if configured, limited)
4. Square (not available)

## 🔧 Production Deployment

### Database Migration

```bash
# For PostgreSQL production
pip install psycopg2-binary

# Update .env
DATABASE_URL=postgresql://user:password@localhost/eloh_gateway

# Run migration
python setup_database.py
```

### Docker Deployment

```dockerfile
FROM python:3.11-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .

# Initialize database
RUN python setup_database.py

EXPOSE 8000
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
```

### Nginx Configuration

```nginx
server {
    listen 80;
    server_name api.elohprocessing.com;
    
    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## 📊 Monitoring

### Health Checks

```bash
# API Health
curl http://localhost:8000/health

# Database Health
python -c "from app.database.connection import get_database_manager; print(get_database_manager().health_check())"

# Gateway Status
curl -H "Authorization: Bearer tenant_api_key" \
     http://localhost:8000/v1/gateways
```

### Logging

Logs are structured JSON format:

```json
{
  "timestamp": "2024-01-01T12:00:00Z",
  "level": "INFO",
  "logger": "app.api.v1.payments",
  "message": "Payment routed to btcpay",
  "extra": {
    "tenant_id": "tenant_abc123",
    "gateway": "btcpay",
    "amount": 100.00,
    "currency": "USD"
  }
}
```

## 🔒 Security

### API Key Management

- **Tenant Keys**: `eloh_` prefix, 32-character random
- **Admin Keys**: Separate authentication system
- **Rotation**: Implement key rotation policy

### Credential Encryption

Gateway credentials are encrypted using Fernet:

```python
from cryptography.fernet import Fernet
# Credentials encrypted before database storage
```

### Rate Limiting

Configure rate limits per plan:

```env
RATE_LIMIT_ENABLED=true
RATE_LIMIT_REQUESTS_PER_MINUTE=100
```

## 🚨 Troubleshooting

### Common Issues

1. **Gateway Not Available**
   - Check regional availability
   - Verify credentials
   - Test gateway connectivity

2. **Database Connection**
   - Verify DATABASE_URL
   - Check database permissions
   - Run health check

3. **Payment Routing Fails**
   - Check enabled gateways
   - Verify currency support
   - Review tenant configuration

### Debug Mode

```env
DEBUG=true
LOG_LEVEL=DEBUG
```

### Support

- **Documentation**: `/docs` endpoint
- **Portal**: `/v1/portal` for tenants
- **Logs**: Check structured logs
- **Database**: Use `setup_database.py --info`

## 📈 Scaling

### Horizontal Scaling

- Multiple API instances behind load balancer
- Shared database and Redis
- Session affinity not required

### Database Optimization

- Use PostgreSQL for production
- Implement connection pooling
- Add database indexes
- Regular maintenance

### Caching

- Redis for session storage
- Gateway response caching
- Rate limit storage

---

**🎉 Your ELOH Processing Payment Gateway is now ready for production!**

The system provides a complete multi-tenant payment gateway service optimized for Dominica's regulatory environment with global cryptocurrency support.
