# 🚀 ELOH Processing Payment Gateway - Render Quick Start

## 📋 Overview

Deploy your ELOH Processing Multi-Tenant Payment Gateway to Render in under 10 minutes! This guide provides the fastest path to get your payment gateway live and processing payments.

## ⚡ Quick Deploy (5 Minutes)

### Option 1: One-Click Deploy
[![Deploy to Render](https://render.com/images/deploy-to-render-button.svg)](https://render.com/deploy)

### Option 2: Script Deploy
```bash
# Windows PowerShell
./deploy-to-render.ps1

# Linux/Mac
./deploy-to-render.sh
```

## 🛠️ Manual Deploy (10 Minutes)

### Step 1: Push to GitHub (2 minutes)
```bash
git add .
git commit -m "Deploy to Render"
git push origin main
```

### Step 2: Create Render Services (3 minutes)

1. **Go to [Render Dashboard](https://dashboard.render.com)**
2. **Create PostgreSQL Database:**
   - Click "New +" → "PostgreSQL"
   - Name: `eloh-gateway-db`
   - Database: `eloh_gateway`
   - Plan: Free
   - Click "Create Database"

3. **Create Web Service:**
   - Click "New +" → "Web Service"
   - Connect GitHub repository
   - Name: `eloh-payment-gateway-api`
   - Environment: Python 3
   - Build Command: `chmod +x build.sh && ./build.sh`
   - Start Command: `uvicorn main:app --host 0.0.0.0 --port $PORT`
   - Plan: Free
   - Click "Create Web Service"

### Step 3: Configure Environment (3 minutes)

In your Render web service, add these environment variables:

**Required:**
```env
APP_NAME=ELOH Processing Payment Gateway
ENVIRONMENT=production
DEBUG=false
LOG_LEVEL=INFO
```

**Gateway Credentials (set your real values):**
```env
# BTCPay Server (Recommended for Dominica)
BTCPAY_SERVER_URL=https://your-btcpay-server.com
BTCPAY_API_KEY=your_btcpay_api_key
BTCPAY_STORE_ID=your_btcpay_store_id

# NowPayments (Recommended for Dominica)
NOWPAYMENTS_API_KEY=your_nowpayments_api_key
NOWPAYMENTS_ENVIRONMENT=production
```

### Step 4: Deploy & Test (2 minutes)

1. **Wait for deployment** (Render will build automatically)
2. **Test your deployment:**
   - Health: `https://your-app.onrender.com/health`
   - API Docs: `https://your-app.onrender.com/docs`
   - Portal: `https://your-app.onrender.com/v1/portal`

## 🎯 Post-Deployment Setup

### 1. Test Gateway Availability
```bash
curl https://your-app.onrender.com/v1/portal/status
```

**Expected Response for Dominica:**
```json
{
  "gateways": {
    "btcpay": {"status": "available", "message": "Available globally"},
    "nowpayments": {"status": "available", "message": "Available globally"},
    "stripe": {"status": "limited", "message": "Not available in your region"},
    "square": {"status": "limited", "message": "Not available in your region"}
  },
  "region": "Dominica"
}
```

### 2. Create Your First Tenant
```bash
curl -X POST "https://your-app.onrender.com/v1/tenants" \
  -H "Authorization: Admin eloh_admin_token_123" \
  -H "Content-Type: application/json" \
  -d '{
    "company_name": "My Caribbean Store",
    "business_type": "E-commerce",
    "contact_email": "<EMAIL>",
    "contact_name": "Store Owner",
    "country": "DM",
    "plan": "professional"
  }'
```

### 3. Configure Tenant Gateway
```bash
curl -X POST "https://your-app.onrender.com/v1/tenants/me/gateways" \
  -H "Authorization: Bearer tenant_api_key_from_step_2" \
  -H "Content-Type: application/json" \
  -d '{
    "gateway_id": "btcpay",
    "enabled": true,
    "credentials": {
      "server_url": "https://your-btcpay.com",
      "api_key": "your_api_key",
      "store_id": "your_store_id"
    }
  }'
```

### 4. Test Payment Creation
```bash
curl -X POST "https://your-app.onrender.com/v1/payments" \
  -H "Authorization: Bearer tenant_api_key" \
  -H "Content-Type: application/json" \
  -d '{
    "amount": 10.00,
    "currency": "USD",
    "description": "Test payment",
    "email": "<EMAIL>"
  }'
```

## 🌍 Regional Optimization for Dominica

### Recommended Gateway Priority:
1. **BTCPay Server** 🥇 - Bitcoin/Lightning, global availability
2. **NowPayments** 🥈 - 300+ cryptocurrencies, global availability
3. **Stripe** ⚠️ - Limited availability in Dominica
4. **Square** ❌ - Not available in Dominica

### Gateway Configuration Tips:
- **Focus on crypto gateways** for maximum regional coverage
- **BTCPay Server** for Bitcoin payments and self-hosting
- **NowPayments** for altcoin diversity
- **Keep Stripe configured** for future expansion

## 💰 Render Pricing

### Free Tier (Perfect for Testing):
- **Web Service**: 750 hours/month
- **PostgreSQL**: 1GB storage
- **Bandwidth**: 100GB/month
- **SSL**: Free certificates

### Production Upgrade ($7/month):
- **Always-on service** (no sleeping)
- **Custom domains**
- **Better performance**
- **Priority support**

## 🔧 Customization

### Custom Domain Setup:
1. Go to service Settings → Custom Domains
2. Add your domain: `api.yourdomain.com`
3. Update DNS records as instructed
4. SSL automatically provisioned

### Environment Variables:
- Set in Render dashboard under Environment
- Use for gateway credentials
- Never commit secrets to Git

### Monitoring:
- View logs in Render dashboard
- Set up health check alerts
- Monitor resource usage

## 🚨 Troubleshooting

### Common Issues:

**Build Fails:**
- Check `build.sh` permissions
- Verify Python dependencies
- Review build logs

**Database Connection:**
- Verify `DATABASE_URL` is set
- Check database service status
- Review connection logs

**Gateway Not Available:**
- Check environment variables
- Verify gateway credentials
- Review regional availability

### Debug Commands:
```bash
# Check health
curl https://your-app.onrender.com/health

# Check gateway status
curl https://your-app.onrender.com/v1/portal/status

# View logs in Render dashboard
```

## 📚 Next Steps

1. **Set up monitoring** and alerts
2. **Configure custom domain** for production
3. **Upgrade to paid plan** for always-on service
4. **Implement backup strategy** for database
5. **Set up CI/CD** with GitHub Actions
6. **Add more tenants** and start processing payments

## 🆘 Support

- **Render Docs**: [render.com/docs](https://render.com/docs)
- **API Documentation**: `https://your-app.onrender.com/docs`
- **Tenant Portal**: `https://your-app.onrender.com/v1/portal`
- **ELOH Support**: [<EMAIL>](mailto:<EMAIL>)

---

## 🎉 Success!

Your ELOH Processing Payment Gateway is now live on Render! 

**Your URLs:**
- 🌐 **API**: `https://your-app.onrender.com`
- 📚 **Docs**: `https://your-app.onrender.com/docs`
- 🏢 **Portal**: `https://your-app.onrender.com/v1/portal`
- ❤️ **Health**: `https://your-app.onrender.com/health`

**Ready to process payments globally with crypto-first approach optimized for the Caribbean! 🚀💰**
