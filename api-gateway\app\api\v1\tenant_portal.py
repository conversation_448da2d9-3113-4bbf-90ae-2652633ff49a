"""
Tenant Portal endpoints for ELOH Processing Multi-Tenant Payment Gateway API

This module provides a web interface for tenants to view API documentation,
test endpoints, and manage their gateway configurations.
"""

from fastapi import APIRouter, Depends, Request, HTTPException
from fastapi.responses import HTMLResponse, JSONResponse
from typing import Optional, Dict, Any
import logging

from ...models.tenant import TenantResponse
from ...services.tenant_service import get_tenant_service
from ...core.adapter_registry import get_adapter_registry
from ...core.config import get_settings
from ...core.exceptions import PaymentGatewayException

router = APIRouter()
logger = logging.getLogger(__name__)


# Authentication dependency (same as tenants.py)
async def get_current_tenant(
    authorization: Optional[str] = None,
    x_api_key: Optional[str] = None
) -> TenantResponse:
    """Get current tenant from API key authentication"""
    from fastapi import Header
    
    tenant_service = get_tenant_service()
    
    # Extract API key from headers or query params for web interface
    api_key = authorization or x_api_key
    if authorization and authorization.startswith("Bearer "):
        api_key = authorization[7:]
    
    if not api_key:
        raise HTTPException(
            status_code=401,
            detail={"error": {"code": "MISSING_API_KEY", "message": "API key required"}}
        )
    
    try:
        tenant = await tenant_service.get_tenant_by_api_key(api_key)
        return tenant
    except PaymentGatewayException:
        raise HTTPException(
            status_code=401,
            detail={"error": {"code": "INVALID_API_KEY", "message": "Invalid API key"}}
        )


@router.get("/portal", response_class=HTMLResponse)
async def tenant_portal_home():
    """
    Tenant portal home page with API information and testing interface.
    
    This page provides tenants with:
    - API documentation links
    - Gateway status information
    - Quick testing interface
    - Integration examples
    """
    
    html_content = """
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>ELOH Processing - Tenant Portal</title>
        <style>
            * { margin: 0; padding: 0; box-sizing: border-box; }
            body { 
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                min-height: 100vh;
                color: #333;
            }
            .container { 
                max-width: 1200px; 
                margin: 0 auto; 
                padding: 20px;
            }
            .header {
                background: rgba(255, 255, 255, 0.95);
                border-radius: 15px;
                padding: 30px;
                margin-bottom: 30px;
                box-shadow: 0 10px 30px rgba(0,0,0,0.1);
                text-align: center;
            }
            .header h1 {
                color: #2c3e50;
                font-size: 2.5em;
                margin-bottom: 10px;
            }
            .header p {
                color: #7f8c8d;
                font-size: 1.2em;
            }
            .grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
                gap: 25px;
                margin-bottom: 30px;
            }
            .card {
                background: rgba(255, 255, 255, 0.95);
                border-radius: 15px;
                padding: 25px;
                box-shadow: 0 8px 25px rgba(0,0,0,0.1);
                transition: transform 0.3s ease;
            }
            .card:hover {
                transform: translateY(-5px);
            }
            .card h3 {
                color: #2c3e50;
                margin-bottom: 15px;
                font-size: 1.4em;
            }
            .gateway-status {
                display: flex;
                align-items: center;
                margin: 10px 0;
                padding: 10px;
                border-radius: 8px;
                background: #f8f9fa;
            }
            .status-indicator {
                width: 12px;
                height: 12px;
                border-radius: 50%;
                margin-right: 10px;
            }
            .status-available { background: #27ae60; }
            .status-unavailable { background: #e74c3c; }
            .status-limited { background: #f39c12; }
            .btn {
                display: inline-block;
                padding: 12px 24px;
                background: #3498db;
                color: white;
                text-decoration: none;
                border-radius: 8px;
                margin: 5px;
                transition: background 0.3s ease;
                border: none;
                cursor: pointer;
                font-size: 14px;
            }
            .btn:hover { background: #2980b9; }
            .btn-success { background: #27ae60; }
            .btn-success:hover { background: #229954; }
            .btn-warning { background: #f39c12; }
            .btn-warning:hover { background: #e67e22; }
            .code-block {
                background: #2c3e50;
                color: #ecf0f1;
                padding: 15px;
                border-radius: 8px;
                font-family: 'Courier New', monospace;
                font-size: 12px;
                overflow-x: auto;
                margin: 10px 0;
            }
            .auth-section {
                background: #fff3cd;
                border: 1px solid #ffeaa7;
                border-radius: 8px;
                padding: 15px;
                margin: 15px 0;
            }
            .footer {
                text-align: center;
                color: rgba(255, 255, 255, 0.8);
                margin-top: 40px;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🚀 ELOH Processing</h1>
                <p>Multi-Tenant Payment Gateway API Portal</p>
            </div>
            
            <div class="grid">
                <div class="card">
                    <h3>🌐 Gateway Status</h3>
                    <div id="gateway-status">
                        <div class="gateway-status">
                            <div class="status-indicator status-limited"></div>
                            <strong>Stripe:</strong> Limited (Not available in your region)
                        </div>
                        <div class="gateway-status">
                            <div class="status-indicator status-limited"></div>
                            <strong>Square:</strong> Limited (Not available in your region)
                        </div>
                        <div class="gateway-status">
                            <div class="status-indicator status-available"></div>
                            <strong>BTCPay Server:</strong> Available (Global)
                        </div>
                        <div class="gateway-status">
                            <div class="status-indicator status-available"></div>
                            <strong>NowPayments:</strong> Available (Global)
                        </div>
                    </div>
                    <button class="btn" onclick="checkGatewayStatus()">Refresh Status</button>
                </div>
                
                <div class="card">
                    <h3>🔑 API Authentication</h3>
                    <div class="auth-section">
                        <strong>Your API Key:</strong><br>
                        <input type="password" id="api-key" placeholder="Enter your API key" style="width: 100%; padding: 8px; margin: 10px 0; border: 1px solid #ddd; border-radius: 4px;">
                        <button class="btn btn-success" onclick="testAuth()">Test Authentication</button>
                    </div>
                    <div id="auth-result"></div>
                </div>
                
                <div class="card">
                    <h3>📚 API Documentation</h3>
                    <p>Access comprehensive API documentation and testing interfaces:</p>
                    <a href="/docs" class="btn" target="_blank">Swagger UI</a>
                    <a href="/redoc" class="btn" target="_blank">ReDoc</a>
                    <a href="/v1/portal/examples" class="btn btn-warning">Code Examples</a>
                </div>
                
                <div class="card">
                    <h3>💳 Quick Payment Test</h3>
                    <div>
                        <input type="number" id="test-amount" placeholder="Amount (USD)" style="width: 100%; padding: 8px; margin: 5px 0; border: 1px solid #ddd; border-radius: 4px;">
                        <select id="test-currency" style="width: 100%; padding: 8px; margin: 5px 0; border: 1px solid #ddd; border-radius: 4px;">
                            <option value="USD">USD</option>
                            <option value="BTC">BTC</option>
                            <option value="ETH">ETH</option>
                        </select>
                        <button class="btn btn-success" onclick="testPayment()">Test Payment</button>
                    </div>
                    <div id="payment-result"></div>
                </div>
                
                <div class="card">
                    <h3>📊 Usage Statistics</h3>
                    <div id="usage-stats">
                        <p>Loading usage statistics...</p>
                    </div>
                    <button class="btn" onclick="loadUsageStats()">Refresh Stats</button>
                </div>
                
                <div class="card">
                    <h3>⚙️ Gateway Configuration</h3>
                    <p>Configure your available payment gateways:</p>
                    <button class="btn" onclick="showGatewayConfig('btcpay')">Configure BTCPay</button>
                    <button class="btn" onclick="showGatewayConfig('nowpayments')">Configure NowPayments</button>
                    <div id="gateway-config"></div>
                </div>
            </div>
            
            <div class="footer">
                <p>&copy; 2024 ELOH Processing. Multi-Tenant Payment Gateway Service.</p>
                <p>Need help? Contact: <a href="mailto:<EMAIL>" style="color: #3498db;"><EMAIL></a></p>
            </div>
        </div>
        
        <script>
            let currentApiKey = '';
            
            function getApiKey() {
                const key = document.getElementById('api-key').value;
                if (!key) {
                    alert('Please enter your API key first');
                    return null;
                }
                return key;
            }
            
            async function testAuth() {
                const apiKey = getApiKey();
                if (!apiKey) return;
                
                try {
                    const response = await fetch('/v1/tenants/me', {
                        headers: {
                            'Authorization': `Bearer ${apiKey}`
                        }
                    });
                    
                    const result = document.getElementById('auth-result');
                    if (response.ok) {
                        const data = await response.json();
                        result.innerHTML = `<div style="color: green; margin-top: 10px;">✅ Authentication successful!<br>Company: ${data.company_name}<br>Plan: ${data.plan}</div>`;
                        currentApiKey = apiKey;
                        loadUsageStats();
                    } else {
                        result.innerHTML = `<div style="color: red; margin-top: 10px;">❌ Authentication failed</div>`;
                    }
                } catch (error) {
                    document.getElementById('auth-result').innerHTML = `<div style="color: red; margin-top: 10px;">❌ Error: ${error.message}</div>`;
                }
            }
            
            async function checkGatewayStatus() {
                const apiKey = getApiKey();
                if (!apiKey) return;
                
                try {
                    const response = await fetch('/v1/gateways', {
                        headers: {
                            'Authorization': `Bearer ${apiKey}`
                        }
                    });
                    
                    if (response.ok) {
                        const data = await response.json();
                        // Update gateway status based on response
                        console.log('Gateway status:', data);
                    }
                } catch (error) {
                    console.error('Error checking gateway status:', error);
                }
            }
            
            async function testPayment() {
                const apiKey = getApiKey();
                if (!apiKey) return;
                
                const amount = document.getElementById('test-amount').value;
                const currency = document.getElementById('test-currency').value;
                
                if (!amount) {
                    alert('Please enter an amount');
                    return;
                }
                
                try {
                    const response = await fetch('/v1/payments', {
                        method: 'POST',
                        headers: {
                            'Authorization': `Bearer ${apiKey}`,
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            amount: parseFloat(amount),
                            currency: currency,
                            description: 'Test payment from portal',
                            email: '<EMAIL>'
                        })
                    });
                    
                    const result = document.getElementById('payment-result');
                    if (response.ok) {
                        const data = await response.json();
                        result.innerHTML = `<div style="color: green; margin-top: 10px;">✅ Payment created!<br>ID: ${data.payment_id}<br>Gateway: ${data.gateway_used}</div>`;
                    } else {
                        const error = await response.json();
                        result.innerHTML = `<div style="color: red; margin-top: 10px;">❌ Payment failed: ${error.detail?.error?.message || 'Unknown error'}</div>`;
                    }
                } catch (error) {
                    document.getElementById('payment-result').innerHTML = `<div style="color: red; margin-top: 10px;">❌ Error: ${error.message}</div>`;
                }
            }
            
            async function loadUsageStats() {
                const apiKey = getApiKey();
                if (!apiKey) return;
                
                try {
                    const response = await fetch('/v1/tenants/me/usage', {
                        headers: {
                            'Authorization': `Bearer ${apiKey}`
                        }
                    });
                    
                    if (response.ok) {
                        const data = await response.json();
                        document.getElementById('usage-stats').innerHTML = `
                            <p><strong>Total Transactions:</strong> ${data.total_transactions}</p>
                            <p><strong>Total Volume:</strong> $${data.total_volume.toFixed(2)}</p>
                            <p><strong>Average Transaction:</strong> $${data.average_transaction.toFixed(2)}</p>
                        `;
                    }
                } catch (error) {
                    document.getElementById('usage-stats').innerHTML = `<p style="color: red;">Error loading stats</p>`;
                }
            }
            
            function showGatewayConfig(gateway) {
                const configDiv = document.getElementById('gateway-config');
                configDiv.innerHTML = `
                    <div style="margin-top: 15px; padding: 15px; background: #f8f9fa; border-radius: 8px;">
                        <h4>Configure ${gateway.toUpperCase()}</h4>
                        <p>Gateway configuration interface would be implemented here.</p>
                        <button class="btn" onclick="this.parentElement.style.display='none'">Close</button>
                    </div>
                `;
            }
            
            // Load initial data
            window.onload = function() {
                // Auto-load if API key is in URL params (for testing)
                const urlParams = new URLSearchParams(window.location.search);
                const apiKey = urlParams.get('api_key');
                if (apiKey) {
                    document.getElementById('api-key').value = apiKey;
                }
            };
        </script>
    </body>
    </html>
    """
    
    return HTMLResponse(content=html_content)


@router.get("/portal/examples", response_class=HTMLResponse)
async def api_examples():
    """API integration examples for tenants"""
    
    html_content = """
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>ELOH Processing - API Examples</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
            .container { max-width: 1000px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; }
            .code-block { background: #2c3e50; color: #ecf0f1; padding: 20px; border-radius: 8px; overflow-x: auto; margin: 15px 0; }
            .language-tab { display: inline-block; padding: 8px 16px; background: #3498db; color: white; margin: 5px; border-radius: 4px; cursor: pointer; }
            .language-tab.active { background: #2980b9; }
            h2 { color: #2c3e50; border-bottom: 2px solid #3498db; padding-bottom: 10px; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🚀 ELOH Processing API Examples</h1>
            
            <h2>Authentication</h2>
            <p>All API requests require authentication using your tenant API key:</p>
            <div class="code-block">
Authorization: Bearer your_tenant_api_key_here
# OR
X-API-Key: your_tenant_api_key_here
            </div>
            
            <h2>Create Payment</h2>
            <div class="language-tab active" onclick="showLanguage('curl')">cURL</div>
            <div class="language-tab" onclick="showLanguage('python')">Python</div>
            <div class="language-tab" onclick="showLanguage('javascript')">JavaScript</div>
            <div class="language-tab" onclick="showLanguage('php')">PHP</div>
            
            <div id="curl" class="code-block">
curl -X POST "https://api.elohprocessing.com/v1/payments" \\
  -H "Authorization: Bearer your_api_key" \\
  -H "Content-Type: application/json" \\
  -d '{
    "amount": 100.00,
    "currency": "USD",
    "description": "Order #12345",
    "email": "<EMAIL>",
    "metadata": {
      "order_id": "12345",
      "customer_id": "cust_123"
    }
  }'
            </div>
            
            <div id="python" class="code-block" style="display: none;">
import requests

url = "https://api.elohprocessing.com/v1/payments"
headers = {
    "Authorization": "Bearer your_api_key",
    "Content-Type": "application/json"
}
data = {
    "amount": 100.00,
    "currency": "USD", 
    "description": "Order #12345",
    "email": "<EMAIL>",
    "metadata": {
        "order_id": "12345",
        "customer_id": "cust_123"
    }
}

response = requests.post(url, headers=headers, json=data)
payment = response.json()
print(f"Payment ID: {payment['payment_id']}")
            </div>
            
            <div id="javascript" class="code-block" style="display: none;">
const response = await fetch('https://api.elohprocessing.com/v1/payments', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer your_api_key',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    amount: 100.00,
    currency: 'USD',
    description: 'Order #12345',
    email: '<EMAIL>',
    metadata: {
      order_id: '12345',
      customer_id: 'cust_123'
    }
  })
});

const payment = await response.json();
console.log('Payment ID:', payment.payment_id);
            </div>
            
            <div id="php" class="code-block" style="display: none;">
<?php
$url = 'https://api.elohprocessing.com/v1/payments';
$data = [
    'amount' => 100.00,
    'currency' => 'USD',
    'description' => 'Order #12345',
    'email' => '<EMAIL>',
    'metadata' => [
        'order_id' => '12345',
        'customer_id' => 'cust_123'
    ]
];

$options = [
    'http' => [
        'header' => [
            'Authorization: Bearer your_api_key',
            'Content-Type: application/json'
        ],
        'method' => 'POST',
        'content' => json_encode($data)
    ]
];

$context = stream_context_create($options);
$response = file_get_contents($url, false, $context);
$payment = json_decode($response, true);

echo "Payment ID: " . $payment['payment_id'];
?>
            </div>
            
            <h2>Available Gateways by Region</h2>
            <div class="code-block">
# Global Gateways (Available Worldwide)
✅ BTCPay Server - Bitcoin & Lightning Network
✅ NowPayments - 300+ Cryptocurrencies

# Regional Gateways (Limited Availability)
⚠️  Stripe - Not available in your region
⚠️  Square - Not available in your region

# Check your available gateways:
curl -H "Authorization: Bearer your_api_key" \\
     https://api.elohprocessing.com/v1/gateways
            </div>
            
            <h2>Webhook Integration</h2>
            <p>Configure webhooks to receive real-time payment notifications:</p>
            <div class="code-block">
# Configure webhook URL
curl -X POST "https://api.elohprocessing.com/v1/tenants/me/gateways" \\
  -H "Authorization: Bearer your_api_key" \\
  -d '{
    "gateway_id": "btcpay",
    "webhook_url": "https://yoursite.com/webhooks/btcpay",
    "webhook_events": ["payment.completed", "payment.failed"]
  }'
            </div>
            
            <a href="/v1/portal" style="display: inline-block; padding: 12px 24px; background: #3498db; color: white; text-decoration: none; border-radius: 8px; margin-top: 20px;">← Back to Portal</a>
        </div>
        
        <script>
            function showLanguage(lang) {
                // Hide all code blocks
                document.querySelectorAll('.code-block[id]').forEach(block => {
                    block.style.display = 'none';
                });
                
                // Remove active class from all tabs
                document.querySelectorAll('.language-tab').forEach(tab => {
                    tab.classList.remove('active');
                });
                
                // Show selected language and mark tab as active
                document.getElementById(lang).style.display = 'block';
                event.target.classList.add('active');
            }
        </script>
    </body>
    </html>
    """
    
    return HTMLResponse(content=html_content)


@router.get("/portal/status")
async def gateway_status_api():
    """API endpoint to check gateway availability status"""
    
    registry = get_adapter_registry()
    settings = get_settings()
    
    # Check which gateways are configured and available
    gateway_status = {}
    
    for gateway_id in ["stripe", "square", "btcpay", "nowpayments"]:
        if registry.is_gateway_registered(gateway_id):
            gateway_info = registry.get_gateway_info(gateway_id)
            is_configured = settings.is_gateway_configured(gateway_id)
            
            # Determine availability based on region and configuration
            if gateway_id in ["stripe", "square"]:
                # These are region-limited
                status = "limited"
                message = "Not available in your region"
            elif gateway_id in ["btcpay", "nowpayments"]:
                # These are globally available
                status = "available" if is_configured else "not_configured"
                message = "Available globally" if is_configured else "Not configured"
            else:
                status = "unknown"
                message = "Status unknown"
            
            gateway_status[gateway_id] = {
                "name": gateway_info["name"],
                "status": status,
                "message": message,
                "configured": is_configured,
                "supported_currencies": gateway_info["supported_currencies"],
                "supported_methods": gateway_info["supported_methods"],
                "regions": gateway_info.get("regions", ["global"])
            }
    
    return {
        "gateways": gateway_status,
        "region": "Dominica",  # Your region
        "available_count": len([g for g in gateway_status.values() if g["status"] == "available"]),
        "total_count": len(gateway_status)
    }
