"""
Custom exceptions for ELOH Processing Payment Gateway API

This module defines custom exception classes for handling various error
conditions in the payment gateway system.
"""

from typing import Optional, Dict, Any
from fastapi import Request, HTTPException
from fastapi.responses import JSONResponse
import logging

logger = logging.getLogger(__name__)


class PaymentGatewayException(Exception):
    """
    Base exception for payment gateway errors.
    
    This exception is used for all payment-related errors that occur
    during gateway operations.
    """
    
    def __init__(
        self,
        message: str,
        error_code: Optional[str] = None,
        gateway: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
        original_error: Optional[Exception] = None
    ):
        super().__init__(message)
        self.message = message
        self.error_code = error_code or "PAYMENT_ERROR"
        self.gateway = gateway
        self.details = details or {}
        self.original_error = original_error
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert exception to dictionary for API responses"""
        return {
            "error": {
                "code": self.error_code,
                "message": self.message,
                "gateway": self.gateway,
                "details": self.details
            }
        }


class GatewayNotAvailableException(PaymentGatewayException):
    """Raised when a payment gateway is not available or configured"""
    
    def __init__(self, gateway: str, message: Optional[str] = None):
        super().__init__(
            message or f"Gateway '{gateway}' is not available or configured",
            error_code="GATEWAY_NOT_AVAILABLE",
            gateway=gateway
        )


class InvalidCredentialsException(PaymentGatewayException):
    """Raised when gateway credentials are invalid"""
    
    def __init__(self, gateway: str, message: Optional[str] = None):
        super().__init__(
            message or f"Invalid credentials for gateway '{gateway}'",
            error_code="INVALID_CREDENTIALS",
            gateway=gateway
        )


class PaymentProcessingException(PaymentGatewayException):
    """Raised when payment processing fails"""
    
    def __init__(self, message: str, gateway: Optional[str] = None, details: Optional[Dict] = None):
        super().__init__(
            message,
            error_code="PAYMENT_PROCESSING_FAILED",
            gateway=gateway,
            details=details
        )


class InsufficientFundsException(PaymentGatewayException):
    """Raised when payment fails due to insufficient funds"""
    
    def __init__(self, gateway: Optional[str] = None):
        super().__init__(
            "Payment failed due to insufficient funds",
            error_code="INSUFFICIENT_FUNDS",
            gateway=gateway
        )


class PaymentDeclinedException(PaymentGatewayException):
    """Raised when payment is declined by the gateway"""
    
    def __init__(self, reason: Optional[str] = None, gateway: Optional[str] = None):
        message = f"Payment declined: {reason}" if reason else "Payment declined"
        super().__init__(
            message,
            error_code="PAYMENT_DECLINED",
            gateway=gateway,
            details={"decline_reason": reason} if reason else {}
        )


class PaymentNotFoundException(PaymentGatewayException):
    """Raised when a payment is not found"""
    
    def __init__(self, payment_id: str, gateway: Optional[str] = None):
        super().__init__(
            f"Payment '{payment_id}' not found",
            error_code="PAYMENT_NOT_FOUND",
            gateway=gateway,
            details={"payment_id": payment_id}
        )


class RefundException(PaymentGatewayException):
    """Raised when refund processing fails"""
    
    def __init__(self, message: str, gateway: Optional[str] = None, details: Optional[Dict] = None):
        super().__init__(
            message,
            error_code="REFUND_FAILED",
            gateway=gateway,
            details=details
        )


class WebhookVerificationException(PaymentGatewayException):
    """Raised when webhook verification fails"""
    
    def __init__(self, gateway: str, message: Optional[str] = None):
        super().__init__(
            message or f"Webhook verification failed for gateway '{gateway}'",
            error_code="WEBHOOK_VERIFICATION_FAILED",
            gateway=gateway
        )


class RoutingException(PaymentGatewayException):
    """Raised when payment routing fails"""
    
    def __init__(self, message: str, details: Optional[Dict] = None):
        super().__init__(
            message,
            error_code="ROUTING_FAILED",
            details=details
        )


class ConfigurationException(PaymentGatewayException):
    """Raised when there are configuration errors"""
    
    def __init__(self, message: str, component: Optional[str] = None):
        super().__init__(
            message,
            error_code="CONFIGURATION_ERROR",
            details={"component": component} if component else {}
        )


# Exception handlers for FastAPI

async def handle_payment_exception(request: Request, exc: PaymentGatewayException) -> JSONResponse:
    """
    Global exception handler for PaymentGatewayException.
    
    This handler converts payment gateway exceptions into appropriate
    HTTP responses with standardized error format.
    """
    
    # Log the exception
    logger.error(
        f"Payment gateway exception: {exc.error_code} - {exc.message}",
        extra={
            "gateway": exc.gateway,
            "error_code": exc.error_code,
            "details": exc.details,
            "path": request.url.path,
            "method": request.method
        }
    )
    
    # Map error codes to HTTP status codes
    status_code_mapping = {
        "PAYMENT_ERROR": 400,
        "GATEWAY_NOT_AVAILABLE": 503,
        "INVALID_CREDENTIALS": 401,
        "PAYMENT_PROCESSING_FAILED": 402,
        "INSUFFICIENT_FUNDS": 402,
        "PAYMENT_DECLINED": 402,
        "PAYMENT_NOT_FOUND": 404,
        "REFUND_FAILED": 400,
        "WEBHOOK_VERIFICATION_FAILED": 400,
        "ROUTING_FAILED": 500,
        "CONFIGURATION_ERROR": 500
    }
    
    status_code = status_code_mapping.get(exc.error_code, 500)
    
    return JSONResponse(
        status_code=status_code,
        content=exc.to_dict()
    )


def create_http_exception(exc: PaymentGatewayException) -> HTTPException:
    """
    Convert PaymentGatewayException to HTTPException.
    
    Args:
        exc: The payment gateway exception
        
    Returns:
        HTTPException: FastAPI HTTP exception
    """
    status_code_mapping = {
        "PAYMENT_ERROR": 400,
        "GATEWAY_NOT_AVAILABLE": 503,
        "INVALID_CREDENTIALS": 401,
        "PAYMENT_PROCESSING_FAILED": 402,
        "INSUFFICIENT_FUNDS": 402,
        "PAYMENT_DECLINED": 402,
        "PAYMENT_NOT_FOUND": 404,
        "REFUND_FAILED": 400,
        "WEBHOOK_VERIFICATION_FAILED": 400,
        "ROUTING_FAILED": 500,
        "CONFIGURATION_ERROR": 500
    }
    
    status_code = status_code_mapping.get(exc.error_code, 500)
    
    return HTTPException(
        status_code=status_code,
        detail=exc.to_dict()
    )
