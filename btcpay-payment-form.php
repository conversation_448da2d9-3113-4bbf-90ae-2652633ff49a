<?php
require_once "includes/btcpay-gateway.php";
include "header.php";

$payment_type = $_GET["type"] ?? "service";
$is_donation = ($payment_type === "donation");
$preset_amount = $_GET["amount"] ?? "";
?>

<main>
  <section class="hero">
    <h1><?php echo $is_donation ? "Make a Donation" : "Pay for Services"; ?></h1>
  </section>

  <section class="section" id="payment-form">
    <h2><?php echo $is_donation ? "Support ELOH Processing with Bitcoin" : "Bitcoin Payment for Services"; ?></h2>

    <?php
    // Display error messages if any
    if (isset($_GET['error'])) {
        $error = $_GET['error'];
        $details = $_GET['details'] ?? '';

        echo '<div style="background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin-bottom: 20px;">';
        echo '<h3>❌ Error</h3>';

        switch ($error) {
            case 'invalid_data':
                echo '<p>Please check your input data:</p>';
                if ($details) echo '<p><strong>Details:</strong> ' . htmlspecialchars($details) . '</p>';
                break;
            case 'btcpay_error':
                echo '<p>BTCPay Server error occurred.</p>';
                if ($details) echo '<p><strong>Details:</strong> ' . htmlspecialchars($details) . '</p>';
                break;
            case 'configuration':
                echo '<p>Payment system configuration error.</p>';
                echo '<p>Please contact support.</p>';
                break;
            default:
                echo '<p>An unknown error occurred.</p>';
        }
        echo '</div>';
    }
    ?>

    <div style="background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin-bottom: 20px;">
        <h3>🚀 Powered by BTCPay Server</h3>
        <p>Secure, self-hosted Bitcoin payment processing. Your payment goes directly to our Bitcoin wallet.</p>
        <ul style="margin: 10px 0; padding-left: 20px;">
            <li>✅ No intermediaries or third parties</li>
            <li>✅ Lightning Network support for instant payments</li>
            <li>✅ On-chain Bitcoin payments</li>
            <li>✅ Real-time payment confirmation</li>
        </ul>
    </div>

    <?php if ($is_donation): ?>
    <p>Your Bitcoin donation helps us expand our sustainable crypto mining operations and transition to 100% renewable energy.</p>
    <?php else: ?>
    <p>Pay for our consulting services, mining pool memberships, and other professional offerings using Bitcoin.</p>
    <?php endif; ?>

    <div class="card">
      <form method="POST" action="btcpay-process-payment.php">
        <input type="hidden" name="payment_type" value="<?php echo htmlspecialchars($payment_type); ?>">

        <div class="form-group">
          <label for="amount" class="form-label required">💰 Amount (USD)</label>
          <div class="input-group">
            <div class="input-group-addon">$</div>
            <input type="number" id="amount" name="amount" min="1" step="0.01" required
                   value="<?php echo htmlspecialchars($preset_amount); ?>"
                   class="form-input" placeholder="0.00">
          </div>
          <small class="text-muted">Minimum: $1.00 USD</small>
        </div>

        <div class="form-group">
          <label for="email" class="form-label required">📧 Email Address</label>
          <input type="email" id="email" name="email" required
                 class="form-input" placeholder="<EMAIL>">
          <small class="text-muted">For payment confirmation and receipt</small>
        </div>

        <?php if (!$is_donation): ?>
        <div class="form-group">
          <label for="service" class="form-label required">💼 Service Type</label>
          <select id="service" name="service" required class="form-input form-select">
            <option value="">Select a service...</option>
            <option value="consulting">💡 Crypto & Forex Consulting ($150/hour)</option>
            <option value="mining-pool">⛏️ Mining Pool Membership ($200/year)</option>
            <option value="mining-services">🔧 Mining Services ($500/month)</option>
            <option value="analysis">📊 Market Analysis Report ($99/report)</option>
            <option value="other">🔧 Other Services</option>
          </select>
        </div>
        <?php endif; ?>

        <div class="form-group">
          <label for="description" class="form-label">📝 Description/Notes</label>
          <textarea id="description" name="description" rows="4"
                    class="form-input form-textarea"
                    placeholder="<?php echo $is_donation ? "Optional message with your donation..." : "Describe the service you are paying for..."; ?>"></textarea>
        </div>

        <!-- Payment Methods Info -->
        <div class="alert alert-info">
          <h4>⚡ Bitcoin Payment Methods</h4>
          <div class="two-col">
            <div>
              <h5>⚡ Lightning Network</h5>
              <p class="text-muted">Instant, low-fee payments</p>
            </div>
            <div>
              <h5>🔗 On-Chain Bitcoin</h5>
              <p class="text-muted">Traditional Bitcoin transactions</p>
            </div>
          </div>
          <div class="mt-md">
            <small class="text-muted">🔒 You'll be redirected to BTCPay Server to complete your payment securely.</small>
          </div>
        </div>

        <!-- Submit Button -->
        <div class="text-center mt-xl">
          <button type="submit" class="cta-button" style="padding: var(--space-lg) var(--space-xl); font-size: 1.1rem; min-width: 250px;">
            <?php echo $is_donation ? "💝 Proceed to Bitcoin Donation" : "🚀 Proceed to Bitcoin Payment"; ?>
          </button>
          <div class="mt-md">
            <small class="text-muted">🔒 Secure Bitcoin payment processing via BTCPay Server</small>
          </div>
        </div>
      </form>
    </div>

    <!-- Navigation -->
    <div class="text-center mt-xl">
      <div class="flex gap-md justify-center">
        <a href="index.php" class="text-muted">← Back to Homepage</a>
        <span class="text-muted">|</span>
        <a href="multi-gateway-payment-form.php" class="text-muted">Other Payment Options</a>
      </div>
    </div>
  </section>
</main>

<?php include "footer.php"; ?>
