<?php
require_once "includes/nowpayments-gateway.php";

header("Content-Type: application/json");

$payment_id = $_GET["payment_id"] ?? "";
if (!$payment_id) {
    echo json_encode(["success" => false, "message" => "No payment ID provided"]);
    exit;
}

try {
    $gateway = new NowPayments_Gateway(false); // Live mode
    $result = $gateway->getPaymentStatus($payment_id);

    echo json_encode($result);

} catch (Exception $e) {
    echo json_encode([
        "success" => false,
        "error" => $e->getMessage()
    ]);
}
?>
