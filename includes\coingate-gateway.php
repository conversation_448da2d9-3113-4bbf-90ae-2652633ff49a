<?php
/**
 * CoinGate Gateway for ELOH Processing
 * Free sandbox available, supports 50+ cryptocurrencies
 */

class CoinGate_Gateway {
    
    private $apiKey;
    private $apiUrl;
    private $sandboxMode;
    
    public function __construct($sandboxMode = true) {
        $config = require __DIR__ . '/coingate-config.php';
        $this->sandboxMode = $sandboxMode;
        $this->apiKey = $sandboxMode ? $config['sandbox_api_key'] : $config['live_api_key'];
        $this->apiUrl = $sandboxMode ? $config['sandbox_url'] : $config['live_url'];
    }
    
    /**
     * Get available currencies
     */
    public function getAvailableCurrencies() {
        $response = $this->makeApiCall('GET', '/currencies');
        
        if ($response && is_array($response)) {
            return [
                'success' => true,
                'currencies' => $response
            ];
        }
        
        return [
            'success' => false,
            'error' => 'Failed to fetch currencies',
            'details' => $response
        ];
    }
    
    /**
     * Create payment order
     */
    public function createPayment($amount, $currency, $orderId, $description = null, $customerEmail = null) {
        $orderData = [
            'order_id' => $orderId,
            'price_amount' => (float)$amount,
            'price_currency' => 'USD',
            'receive_currency' => strtoupper($currency),
            'title' => $description ?: 'ELOH Processing Payment',
            'description' => $description ?: 'Payment for ELOH Processing services',
            'callback_url' => $this->getCallbackUrl(),
            'success_url' => $this->getSuccessUrl($orderId),
            'cancel_url' => $this->getCancelUrl($orderId)
        ];
        
        if ($customerEmail) {
            $orderData['purchaser_email'] = $customerEmail;
        }
        
        $response = $this->makeApiCall('POST', '/orders', $orderData);
        
        if ($response && isset($response['id'])) {
            return [
                'success' => true,
                'order_id' => $response['id'],
                'payment_url' => $response['payment_url'],
                'status' => $response['status'],
                'price_amount' => $response['price_amount'],
                'price_currency' => $response['price_currency'],
                'receive_currency' => $response['receive_currency'],
                'receive_amount' => $response['receive_amount'] ?? null,
                'created_at' => $response['created_at'],
                'expires_at' => $response['expires_at'] ?? null
            ];
        }
        
        return [
            'success' => false,
            'error' => 'Failed to create payment order',
            'details' => $response
        ];
    }
    
    /**
     * Get payment order status
     */
    public function getPaymentStatus($orderId) {
        $response = $this->makeApiCall('GET', "/orders/{$orderId}");
        
        if ($response && isset($response['status'])) {
            return [
                'success' => true,
                'status' => $response['status'],
                'price_amount' => $response['price_amount'],
                'receive_amount' => $response['receive_amount'] ?? null,
                'receive_currency' => $response['receive_currency'],
                'payment_address' => $response['payment_address'] ?? null,
                'created_at' => $response['created_at'],
                'expires_at' => $response['expires_at'] ?? null
            ];
        }
        
        return [
            'success' => false,
            'error' => 'Order not found'
        ];
    }
    
    /**
     * Validate callback signature
     */
    public function validateCallback($payload, $signature) {
        $expectedSignature = hash_hmac('sha256', $payload, $this->apiKey);
        return hash_equals($expectedSignature, $signature);
    }
    
    /**
     * Make API call to CoinGate
     */
    private function makeApiCall($method, $endpoint, $data = null) {
        $url = $this->apiUrl . $endpoint;
        
        $headers = [
            'Authorization: Token ' . $this->apiKey,
            'Content-Type: application/json',
            'Accept: application/json'
        ];
        
        // Log the request for debugging
        error_log("CoinGate API Request: $method $url");
        if ($data) {
            error_log("CoinGate API Data: " . json_encode($data));
        }
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_USERAGENT, 'ELOH-Processing-CoinGate/1.0');
        
        if ($method === 'POST') {
            curl_setopt($ch, CURLOPT_POST, true);
            if ($data) {
                curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
            }
        }
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        // Log the response for debugging
        error_log("CoinGate API Response: HTTP $httpCode - " . substr($response, 0, 500));
        
        if ($error) {
            error_log("CoinGate API cURL Error: " . $error);
            return [
                'error' => 'Connection error: ' . $error,
                'http_code' => 0
            ];
        }
        
        if ($httpCode >= 400) {
            error_log("CoinGate API HTTP Error: " . $httpCode . " - " . $response);
            return [
                'error' => 'HTTP Error ' . $httpCode,
                'http_code' => $httpCode,
                'response' => $response
            ];
        }
        
        $decoded = json_decode($response, true);
        if ($decoded === null && json_last_error() !== JSON_ERROR_NONE) {
            error_log("CoinGate API JSON Error: " . json_last_error_msg());
            return [
                'error' => 'Invalid JSON response: ' . json_last_error_msg(),
                'response' => $response
            ];
        }
        
        return $decoded;
    }
    
    /**
     * Get callback URL
     */
    private function getCallbackUrl() {
        return 'https://elohprocessing.infy.uk/coingate-callback.php';
    }
    
    /**
     * Get success URL
     */
    private function getSuccessUrl($orderId) {
        return 'https://elohprocessing.infy.uk/payment-success.php?order=' . $orderId . '&gateway=coingate';
    }
    
    /**
     * Get cancel URL
     */
    private function getCancelUrl($orderId) {
        return 'https://elohprocessing.infy.uk/payment-cancelled.php?order=' . $orderId;
    }
    
    /**
     * Get supported currencies (popular ones)
     */
    public function getPopularCurrencies() {
        return [
            'BTC' => 'Bitcoin',
            'ETH' => 'Ethereum',
            'LTC' => 'Litecoin',
            'BCH' => 'Bitcoin Cash',
            'XRP' => 'Ripple',
            'ADA' => 'Cardano',
            'DOT' => 'Polkadot',
            'USDT' => 'Tether',
            'USDC' => 'USD Coin',
            'BNB' => 'Binance Coin',
            'MATIC' => 'Polygon',
            'TRX' => 'Tron'
        ];
    }
    
    /**
     * Get debug information
     */
    public function getDebugInfo() {
        return [
            'api_url' => $this->apiUrl,
            'sandbox_mode' => $this->sandboxMode,
            'api_key_set' => !empty($this->apiKey)
        ];
    }
}
