"""
Payment Gateway Adapters for ELOH Processing

This module contains all payment gateway adapter implementations following
the Adapter Pattern for consistent interface across different payment providers.
"""

from .gateway_adapter import GatewayAdapter, GatewayCredentials
from .stripe_adapter import Stripe<PERSON>dapter, StripeCredentials
from .btcpay_adapter import BTCPayAdapter, BTCPayCredentials

__all__ = [
    "GatewayAdapter",
    "GatewayCredentials", 
    "StripeAdapter",
    "StripeCredentials",
    "BTCPayAdapter", 
    "BTCPayCredentials"
]
