<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ELOH Processing - Offline</title>
    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --primary-color: #667eea;
            --text-primary: #1a202c;
            --text-secondary: #4a5568;
            --surface-color: #ffffff;
            --border-color: #e2e8f0;
            --shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.1);
            --space-sm: 0.5rem;
            --space-md: 1rem;
            --space-lg: 1.5rem;
            --space-xl: 2rem;
            --space-2xl: 3rem;
            --radius-lg: 0.75rem;
            --radius-xl: 1rem;
            --transition-base: 0.2s ease-out;
            --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: var(--font-primary);
            background: var(--primary-gradient);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            text-align: center;
            padding: var(--space-lg);
        }

        .offline-container {
            max-width: 500px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: var(--radius-xl);
            padding: var(--space-2xl);
            box-shadow: var(--shadow-lg);
        }

        .offline-icon {
            font-size: 5rem;
            margin-bottom: var(--space-lg);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        h1 {
            font-size: 2.5rem;
            font-weight: 800;
            margin-bottom: var(--space-md);
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        p {
            font-size: 1.2rem;
            margin-bottom: var(--space-xl);
            opacity: 0.9;
            line-height: 1.6;
        }

        .offline-features {
            background: rgba(255, 255, 255, 0.1);
            border-radius: var(--radius-lg);
            padding: var(--space-lg);
            margin: var(--space-xl) 0;
            text-align: left;
        }

        .offline-features h3 {
            font-size: 1.3rem;
            margin-bottom: var(--space-md);
            text-align: center;
        }

        .feature-list {
            list-style: none;
            padding: 0;
        }

        .feature-list li {
            padding: var(--space-sm) 0;
            display: flex;
            align-items: center;
            gap: var(--space-sm);
        }

        .feature-list li::before {
            content: '⚡';
            font-size: 1.2rem;
        }

        .action-buttons {
            display: flex;
            flex-direction: column;
            gap: var(--space-md);
            margin-top: var(--space-xl);
        }

        .btn {
            padding: var(--space-md) var(--space-xl);
            border: none;
            border-radius: var(--radius-lg);
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all var(--transition-base);
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: var(--space-sm);
        }

        .btn-primary {
            background: white;
            color: var(--primary-color);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(255, 255, 255, 0.3);
        }

        .btn-secondary {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .btn-secondary:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .connection-status {
            margin-top: var(--space-xl);
            padding: var(--space-md);
            background: rgba(255, 255, 255, 0.1);
            border-radius: var(--radius-lg);
            font-size: 0.9rem;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #ef4444;
            margin-right: var(--space-sm);
            animation: blink 1.5s infinite;
        }

        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0.3; }
        }

        .status-indicator.online {
            background: #10b981;
            animation: none;
        }

        @media (max-width: 768px) {
            .offline-container {
                padding: var(--space-xl);
                margin: var(--space-md);
            }

            h1 {
                font-size: 2rem;
            }

            p {
                font-size: 1.1rem;
            }

            .offline-icon {
                font-size: 4rem;
            }
        }
    </style>
</head>
<body>
    <div class="offline-container">
        <div class="offline-icon">⚡</div>
        <h1>You're Offline</h1>
        <p>ELOH Processing is not available right now. Don't worry - we've got you covered with offline features!</p>
        
        <div class="offline-features">
            <h3>🚀 Available Offline</h3>
            <ul class="feature-list">
                <li>Browse cached pages and content</li>
                <li>View service information and pricing</li>
                <li>Access company information</li>
                <li>Queue payments for when you're back online</li>
                <li>Save contact form submissions</li>
            </ul>
        </div>

        <div class="action-buttons">
            <button class="btn btn-primary" onclick="checkConnection()">
                <span>🔄</span>
                <span>Try Again</span>
            </button>
            
            <a href="/" class="btn btn-secondary">
                <span>🏠</span>
                <span>Go to Homepage</span>
            </a>
            
            <button class="btn btn-secondary" onclick="showCachedPages()">
                <span>📄</span>
                <span>View Cached Pages</span>
            </button>
        </div>

        <div class="connection-status">
            <span class="status-indicator" id="status-indicator"></span>
            <span id="connection-text">Checking connection...</span>
        </div>
    </div>

    <script>
        // Connection monitoring
        function updateConnectionStatus() {
            const indicator = document.getElementById('status-indicator');
            const text = document.getElementById('connection-text');
            
            if (navigator.onLine) {
                indicator.classList.add('online');
                text.textContent = 'Connection restored! You can now access all features.';
                
                // Show reload option when back online
                setTimeout(() => {
                    if (confirm('Connection restored! Would you like to reload the page?')) {
                        window.location.reload();
                    }
                }, 1000);
            } else {
                indicator.classList.remove('online');
                text.textContent = 'No internet connection detected.';
            }
        }

        // Check connection manually
        function checkConnection() {
            updateConnectionStatus();
            
            if (navigator.onLine) {
                window.location.reload();
            } else {
                // Try to fetch a small resource to test connectivity
                fetch('/manifest.json', { 
                    method: 'HEAD',
                    cache: 'no-cache'
                })
                .then(() => {
                    window.location.reload();
                })
                .catch(() => {
                    alert('Still offline. Please check your internet connection and try again.');
                });
            }
        }

        // Show cached pages
        function showCachedPages() {
            const cachedPages = [
                { name: 'Homepage', url: '/' },
                { name: 'About Us', url: '/about.php' },
                { name: 'Services', url: '/services.php' },
                { name: 'Operations', url: '/operations.php' },
                { name: 'Investors', url: '/investors.php' },
                { name: 'Contact', url: '/contact.php' }
            ];

            let pagesList = 'Available cached pages:\n\n';
            cachedPages.forEach((page, index) => {
                pagesList += `${index + 1}. ${page.name}\n`;
            });

            const choice = prompt(pagesList + '\nEnter the number of the page you want to visit:');
            const pageIndex = parseInt(choice) - 1;

            if (pageIndex >= 0 && pageIndex < cachedPages.length) {
                window.location.href = cachedPages[pageIndex].url;
            }
        }

        // Event listeners
        window.addEventListener('online', updateConnectionStatus);
        window.addEventListener('offline', updateConnectionStatus);

        // Initial status check
        updateConnectionStatus();

        // Periodic connection check
        setInterval(updateConnectionStatus, 5000);

        // Service worker messaging
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.addEventListener('message', event => {
                if (event.data && event.data.type === 'CACHE_UPDATED') {
                    console.log('Cache updated:', event.data.url);
                }
            });
        }
    </script>
</body>
</html>
