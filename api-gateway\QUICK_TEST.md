# 🧪 Quick Test & Debug Guide

## Step 1: Navigate to Correct Directory

**Windows Command Prompt:**
```cmd
cd Documents\augment-projects\ELOH Processing\api-gateway
dir
```

**Windows PowerShell:**
```powershell
cd "Documents\augment-projects\ELOH Processing\api-gateway"
Get-ChildItem
```

**You should see these files:**
- main.py
- requirements.txt
- setup_database.py
- debug.py
- test_local.py
- start.py

## Step 2: Run Debug Script

```bash
python debug.py
```

**This script will:**
- ✅ Check if you're in the right directory
- ✅ Install missing dependencies
- ✅ Create .env file if needed
- ✅ Set up database
- ✅ Test all imports

## Step 3: Run Local Tests

```bash
python test_local.py
```

**This will test:**
- ✅ File existence
- ✅ Python version
- ✅ All imports
- ✅ Database connection
- ✅ Gateway registry
- ✅ FastAPI app

## Step 4: Start the Server

```bash
python start.py
```

**Or manually:**
```bash
uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

## Step 5: Test Endpoints

**Open in browser:**
- http://localhost:8000/health
- http://localhost:8000/docs
- http://localhost:8000/v1/portal

**Or use curl:**
```bash
curl http://localhost:8000/health
curl http://localhost:8000/v1/portal/status
```

## 🚨 If You Get Errors:

### "requirements.txt not found"
```bash
# Check current directory
pwd
# Should show: .../api-gateway

# If not, navigate to api-gateway:
cd api-gateway
```

### "Module not found"
```bash
# Install dependencies
pip install -r requirements.txt

# Or use debug script
python debug.py
```

### "Database error"
```bash
# Reset database
python setup_database.py --reset --sample-data
```

### "Port in use"
```bash
# Use different port
uvicorn main:app --port 8001
```

## 🎯 Expected Results:

### Health Check Response:
```json
{
  "status": "healthy",
  "service": "ELOH Processing Payment Gateway",
  "version": "1.0.0",
  "environment": "development",
  "database": "healthy",
  "gateways": {
    "available": ["btcpay", "nowpayments"],
    "count": 2
  }
}
```

### Gateway Status Response:
```json
{
  "gateways": {
    "btcpay": {
      "status": "available",
      "message": "Available globally"
    },
    "nowpayments": {
      "status": "available", 
      "message": "Available globally"
    },
    "stripe": {
      "status": "limited",
      "message": "Not available in your region"
    },
    "square": {
      "status": "limited",
      "message": "Not available in your region"
    }
  },
  "region": "Dominica"
}
```

## 🔧 One-Command Fix:

If everything fails, try this complete reset:

```bash
# Navigate to api-gateway directory first!
cd api-gateway

# Run comprehensive debug and setup
python debug.py && python test_local.py && python start.py
```

This will:
1. Fix common issues
2. Test everything
3. Start the server

## 📞 Need Help?

Run these commands and share the output:

```bash
# System info
python --version
pip --version
pwd

# App info
python debug.py
python test_local.py
```

**Most common issue**: Not being in the `api-gateway` directory!

**Quick check**: `ls` or `dir` should show `main.py` file.
