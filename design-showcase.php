<?php include "header.php"; ?>

<main>
  <!-- Hero Section -->
  <section class="hero">
    <div class="hero-content">
      <h1>🚀 Futuristic Design System</h1>
      <p>Experience the new minimalistic, bold, and futuristic design of ELOH Processing</p>
      <div class="flex gap-md justify-center mt-xl">
        <a href="#components" class="cta-button">Explore Components</a>
        <a href="index.php" class="cta-button secondary">Back to Home</a>
      </div>
    </div>
  </section>

  <!-- Components Showcase -->
  <section class="section" id="components">
    <h2>Design Components</h2>
    
    <!-- Cards Grid -->
    <div class="three-col">
      <div class="card">
        <h3>🎨 Modern Cards</h3>
        <p>Sleek card components with hover effects and gradient borders.</p>
        <div class="badge badge-primary">New</div>
      </div>
      
      <div class="card">
        <h3>⚡ Interactive Buttons</h3>
        <p>Futuristic buttons with shimmer effects and smooth animations.</p>
        <a href="#" class="cta-button mt-md">Try Button</a>
      </div>
      
      <div class="card">
        <h3>🌙 Dark Mode</h3>
        <p>Seamless dark/light theme switching with smooth transitions.</p>
        <div class="badge badge-success">Active</div>
      </div>
    </div>

    <!-- Form Components -->
    <div class="section">
      <h3>Form Elements</h3>
      <div class="two-col">
        <div>
          <div class="form-group">
            <label class="form-label">Modern Input</label>
            <input type="text" class="form-input" placeholder="Enter your text...">
          </div>
          
          <div class="form-group">
            <label class="form-label">Select Dropdown</label>
            <select class="form-input form-select">
              <option>Choose an option</option>
              <option>Bitcoin Payment</option>
              <option>Crypto Processing</option>
              <option>Mining Services</option>
            </select>
          </div>
        </div>
        
        <div>
          <div class="form-group">
            <label class="form-label">Progress Indicator</label>
            <div class="progress">
              <div class="progress-bar" style="width: 75%"></div>
            </div>
          </div>
          
          <div class="form-group">
            <label class="form-label">Loading States</label>
            <div class="flex gap-lg items-center">
              <div class="loading-spinner"></div>
              <div class="loading-dots">
                <div class="loading-dot"></div>
                <div class="loading-dot"></div>
                <div class="loading-dot"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Alert Components -->
    <div class="section">
      <h3>Alert Messages</h3>
      <div class="alert alert-success">
        <strong>Success!</strong> Your payment has been processed successfully.
      </div>
      
      <div class="alert alert-warning">
        <strong>Warning!</strong> Please verify your wallet address before proceeding.
      </div>
      
      <div class="alert alert-error">
        <strong>Error!</strong> Transaction failed. Please try again.
      </div>
      
      <div class="alert alert-info">
        <strong>Info!</strong> New features are now available in your dashboard.
      </div>
    </div>

    <!-- Badge Showcase -->
    <div class="section">
      <h3>Status Badges</h3>
      <div class="flex gap-md">
        <span class="badge badge-primary">Primary</span>
        <span class="badge badge-secondary">Secondary</span>
        <span class="badge badge-success">Success</span>
        <span class="badge badge-warning">Warning</span>
        <span class="badge badge-error">Error</span>
      </div>
    </div>

    <!-- Typography -->
    <div class="section">
      <h3>Typography Scale</h3>
      <h1>Heading 1 - Bold & Futuristic</h1>
      <h2>Heading 2 - Modern Design</h2>
      <h3>Heading 3 - Clean Typography</h3>
      <h4>Heading 4 - Readable Text</h4>
      <h5>Heading 5 - Consistent Style</h5>
      <h6>Heading 6 - Perfect Hierarchy</h6>
      <p>Body text with perfect line height and spacing for optimal readability. The Inter font family provides excellent legibility across all devices.</p>
    </div>

    <!-- Color Palette -->
    <div class="section">
      <h3>Color System</h3>
      <div class="three-col">
        <div class="card bg-gradient text-center" style="color: white;">
          <h4>Primary Gradient</h4>
          <p>Main brand colors</p>
        </div>
        
        <div class="card bg-surface text-center">
          <h4>Surface Colors</h4>
          <p>Background elements</p>
        </div>
        
        <div class="card text-center" style="background: var(--hover-bg);">
          <h4>Interactive States</h4>
          <p>Hover & focus effects</p>
        </div>
      </div>
    </div>

    <!-- Call to Action -->
    <div class="section text-center">
      <h2>Ready to Experience the Future?</h2>
      <p>Explore our payment processing services with this new futuristic interface.</p>
      <div class="flex gap-md justify-center mt-xl">
        <a href="multi-gateway-payment-form.php" class="cta-button">
          💳 Try Payment Form
        </a>
        <a href="index.php" class="cta-button secondary">
          🏠 Back to Home
        </a>
      </div>
    </div>
  </section>
</main>

<script>
// Demo interactions
document.addEventListener('DOMContentLoaded', function() {
  // Animate progress bar
  const progressBar = document.querySelector('.progress-bar');
  let width = 0;
  const interval = setInterval(() => {
    if (width >= 75) {
      clearInterval(interval);
    } else {
      width += 1;
      progressBar.style.width = width + '%';
    }
  }, 20);
  
  // Add click effects to cards
  document.querySelectorAll('.card').forEach(card => {
    card.addEventListener('click', function() {
      this.style.transform = 'scale(0.98)';
      setTimeout(() => {
        this.style.transform = '';
      }, 150);
    });
  });
});
</script>

<?php include "footer.php"; ?>
