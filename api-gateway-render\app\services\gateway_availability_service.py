"""
Gateway availability service for regional gateway detection
"""

from typing import List, Dict, Any
from enum import Enum
import logging

logger = logging.getLogger(__name__)


class GatewayStatus(Enum):
    """Gateway availability status"""
    AVAILABLE = "available"
    LIMITED = "limited"
    UNAVAILABLE = "unavailable"


class GatewayAvailability:
    """Gateway availability information"""
    
    def __init__(
        self,
        gateway_id: str,
        name: str,
        status: GatewayStatus,
        message: str,
        supported_regions: List[str] = None,
        supported_currencies: List[str] = None
    ):
        self.gateway_id = gateway_id
        self.name = name
        self.status = status
        self.message = message
        self.supported_regions = supported_regions or []
        self.supported_currencies = supported_currencies or []


class GatewayAvailabilityService:
    """Service for checking gateway availability by region"""
    
    def __init__(self):
        self._gateway_regions = self._initialize_gateway_regions()
    
    def _initialize_gateway_regions(self) -> Dict[str, Dict[str, Any]]:
        """Initialize gateway regional availability data"""
        return {
            "btcpay": {
                "name": "BTCPay Server",
                "global": True,
                "regions": ["global"],
                "currencies": ["BTC", "LTC", "USD"],
                "restrictions": []
            },
            "nowpayments": {
                "name": "NowPayments",
                "global": True,
                "regions": ["global"],
                "currencies": ["BTC", "ETH", "LTC", "XMR", "USD", "EUR"],
                "restrictions": []
            },
            "stripe": {
                "name": "Stripe",
                "global": False,
                "regions": ["US", "CA", "GB", "AU", "EU"],
                "currencies": ["USD", "EUR", "GBP", "CAD"],
                "restrictions": ["DM", "Caribbean"]  # Dominica and Caribbean restricted
            },
            "square": {
                "name": "Square",
                "global": False,
                "regions": ["US", "CA", "GB", "AU"],
                "currencies": ["USD", "CAD", "GBP", "EUR"],
                "restrictions": ["DM", "Caribbean"]  # Not available in Caribbean
            }
        }
    
    def get_gateway_availability(self, gateway_id: str, region: str = "DM") -> GatewayAvailability:
        """
        Get availability status for a specific gateway in a region.
        
        Args:
            gateway_id: Gateway identifier
            region: Region code (default: DM for Dominica)
            
        Returns:
            GatewayAvailability: Availability information
        """
        gateway_info = self._gateway_regions.get(gateway_id)
        
        if not gateway_info:
            return GatewayAvailability(
                gateway_id=gateway_id,
                name=gateway_id.title(),
                status=GatewayStatus.UNAVAILABLE,
                message="Gateway not found"
            )
        
        # Check if gateway is globally available
        if gateway_info.get("global", False):
            return GatewayAvailability(
                gateway_id=gateway_id,
                name=gateway_info["name"],
                status=GatewayStatus.AVAILABLE,
                message="Available globally",
                supported_regions=gateway_info["regions"],
                supported_currencies=gateway_info["currencies"]
            )
        
        # Check regional restrictions
        restrictions = gateway_info.get("restrictions", [])
        if region in restrictions or "Caribbean" in restrictions:
            return GatewayAvailability(
                gateway_id=gateway_id,
                name=gateway_info["name"],
                status=GatewayStatus.LIMITED,
                message=f"Not available in your region ({region})",
                supported_regions=gateway_info["regions"],
                supported_currencies=gateway_info["currencies"]
            )
        
        # Check if region is supported
        supported_regions = gateway_info.get("regions", [])
        if region in supported_regions:
            return GatewayAvailability(
                gateway_id=gateway_id,
                name=gateway_info["name"],
                status=GatewayStatus.AVAILABLE,
                message=f"Available in {region}",
                supported_regions=supported_regions,
                supported_currencies=gateway_info["currencies"]
            )
        
        # Default to limited availability
        return GatewayAvailability(
            gateway_id=gateway_id,
            name=gateway_info["name"],
            status=GatewayStatus.LIMITED,
            message=f"Limited availability in {region}",
            supported_regions=supported_regions,
            supported_currencies=gateway_info["currencies"]
        )
    
    def get_available_gateways(self, region: str = "DM") -> List[GatewayAvailability]:
        """
        Get all available gateways for a region.
        
        Args:
            region: Region code (default: DM for Dominica)
            
        Returns:
            List[GatewayAvailability]: List of gateway availability information
        """
        gateways = []
        
        for gateway_id in self._gateway_regions.keys():
            availability = self.get_gateway_availability(gateway_id, region)
            gateways.append(availability)
        
        # Sort by availability status (available first)
        gateways.sort(key=lambda x: (
            0 if x.status == GatewayStatus.AVAILABLE else
            1 if x.status == GatewayStatus.LIMITED else 2
        ))
        
        return gateways
    
    def get_recommended_gateways(self, region: str = "DM") -> List[str]:
        """
        Get recommended gateways for a region.
        
        Args:
            region: Region code (default: DM for Dominica)
            
        Returns:
            List[str]: List of recommended gateway IDs
        """
        available_gateways = self.get_available_gateways(region)
        
        # Return only available gateways
        return [
            gw.gateway_id for gw in available_gateways 
            if gw.status == GatewayStatus.AVAILABLE
        ]


# Global service instance
_gateway_availability_service = None


def get_gateway_availability_service() -> GatewayAvailabilityService:
    """Get the global gateway availability service instance"""
    global _gateway_availability_service
    if _gateway_availability_service is None:
        _gateway_availability_service = GatewayAvailabilityService()
    return _gateway_availability_service
