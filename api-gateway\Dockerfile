# ELOH Processing Payment Gateway - Render Deployment
FROM python:3.11-slim

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1
ENV PORT=8000

# Set work directory
WORKDIR /app

# Install system dependencies
RUN apt-get update \
    && apt-get install -y --no-install-recommends \
        build-essential \
        libpq-dev \
        curl \
    && rm -rf /var/lib/apt/lists/*

# Install Python dependencies
COPY requirements.txt .
COPY requirements-minimal.txt .

# Try minimal requirements first, fall back to full requirements
RUN pip install --upgrade pip && \
    (pip install --no-cache-dir -r requirements-minimal.txt || \
     pip install --no-cache-dir -r requirements.txt)

# Copy project
COPY . .

# Create non-root user
RUN adduser --disabled-password --gecos '' appuser && chown -R appuser /app
USER appuser

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:$PORT/health || exit 1

# Expose port
EXPOSE $PORT

# Run the application
CMD ["sh", "-c", "python setup_database.py && uvicorn main:app --host 0.0.0.0 --port $PORT"]
