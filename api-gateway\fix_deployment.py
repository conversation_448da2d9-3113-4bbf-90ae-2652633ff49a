#!/usr/bin/env python3
"""
Fix Deployment Issues for ELOH Processing Payment Gateway

This script fixes common deployment issues including dependency conflicts.
"""

import os
import sys
import subprocess
from pathlib import Path

def print_status(message, level="INFO"):
    colors = {
        "INFO": "\033[94m",
        "SUCCESS": "\033[92m", 
        "WARNING": "\033[93m",
        "ERROR": "\033[91m",
        "RESET": "\033[0m"
    }
    print(f"{colors.get(level, '')}{level}: {message}{colors['RESET']}")

def fix_requirements():
    """Fix requirements.txt with known working versions"""
    print_status("🔧 Fixing requirements.txt with known working versions...", "INFO")
    
    # Create a clean requirements.txt with verified versions
    clean_requirements = """# ELOH Processing Payment Gateway - Fixed Dependencies

# Core FastAPI stack (verified working versions)
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
pydantic-settings==2.1.0

# HTTP clients
aiohttp==3.9.1
httpx==0.25.2

# Database (essential for production)
sqlalchemy==2.0.23
psycopg2-binary==2.9.9
alembic==1.13.1

# Security
cryptography==41.0.7
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6

# Utilities
python-dotenv==1.0.0
click==8.1.7

# Payment gateways (install only if needed)
stripe==7.8.0
# squareup==30.0.0.20231115  # Commented out - can cause issues

# Optional: Development tools (comment out for production)
# pytest==7.4.3
# black==23.11.0
# isort==5.12.0
"""
    
    try:
        with open("requirements.txt", "w") as f:
            f.write(clean_requirements)
        print_status("✅ requirements.txt fixed with verified versions", "SUCCESS")
        return True
    except Exception as e:
        print_status(f"❌ Failed to fix requirements.txt: {e}", "ERROR")
        return False

def create_production_requirements():
    """Create production-only requirements"""
    print_status("📦 Creating production requirements...", "INFO")
    
    prod_requirements = """# ELOH Processing Payment Gateway - Production Dependencies

# Core FastAPI stack
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
pydantic-settings==2.1.0

# HTTP clients
httpx==0.25.2

# Database
sqlalchemy==2.0.23
psycopg2-binary==2.9.9

# Security
cryptography==41.0.7
python-jose[cryptography]==3.3.0
python-dotenv==1.0.0

# Payment gateways
stripe==7.8.0
"""
    
    try:
        with open("requirements-production.txt", "w") as f:
            f.write(prod_requirements)
        print_status("✅ requirements-production.txt created", "SUCCESS")
        return True
    except Exception as e:
        print_status(f"❌ Failed to create production requirements: {e}", "ERROR")
        return False

def fix_dockerfile():
    """Fix Dockerfile for better dependency handling"""
    print_status("🐳 Fixing Dockerfile...", "INFO")
    
    dockerfile_content = """# ELOH Processing Payment Gateway - Render Deployment
FROM python:3.11-slim

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1
ENV PORT=8000

# Set work directory
WORKDIR /app

# Install system dependencies
RUN apt-get update \\
    && apt-get install -y --no-install-recommends \\
        build-essential \\
        libpq-dev \\
        curl \\
    && rm -rf /var/lib/apt/lists/*

# Upgrade pip first
RUN pip install --upgrade pip

# Copy requirements files
COPY requirements*.txt ./

# Install Python dependencies with fallback strategy
RUN pip install --no-cache-dir -r requirements-production.txt || \\
    pip install --no-cache-dir -r requirements-minimal.txt || \\
    pip install --no-cache-dir -r requirements.txt

# Copy project
COPY . .

# Create non-root user
RUN adduser --disabled-password --gecos '' appuser && chown -R appuser /app
USER appuser

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \\
    CMD curl -f http://localhost:$PORT/health || exit 1

# Expose port
EXPOSE $PORT

# Run the application
CMD ["sh", "-c", "python setup_database.py && uvicorn main:app --host 0.0.0.0 --port $PORT"]
"""
    
    try:
        with open("Dockerfile", "w") as f:
            f.write(dockerfile_content)
        print_status("✅ Dockerfile fixed", "SUCCESS")
        return True
    except Exception as e:
        print_status(f"❌ Failed to fix Dockerfile: {e}", "ERROR")
        return False

def test_requirements():
    """Test if requirements can be installed"""
    print_status("🧪 Testing requirements installation...", "INFO")
    
    test_files = ["requirements-production.txt", "requirements-minimal.txt", "requirements.txt"]
    
    for req_file in test_files:
        if not Path(req_file).exists():
            continue
            
        print_status(f"Testing {req_file}...", "INFO")
        try:
            # Test in a virtual environment simulation
            result = subprocess.run([
                sys.executable, "-m", "pip", "install", "--dry-run", "-r", req_file
            ], capture_output=True, text=True, timeout=60)
            
            if result.returncode == 0:
                print_status(f"✅ {req_file} looks good", "SUCCESS")
                return req_file
            else:
                print_status(f"⚠️ {req_file} has issues: {result.stderr[:200]}...", "WARNING")
        except subprocess.TimeoutExpired:
            print_status(f"⚠️ {req_file} test timed out", "WARNING")
        except Exception as e:
            print_status(f"⚠️ {req_file} test failed: {e}", "WARNING")
    
    return None

def create_render_yaml():
    """Create updated render.yaml with better dependency handling"""
    print_status("🌐 Creating updated render.yaml...", "INFO")
    
    render_config = """services:
  # Main API Service
  - type: web
    name: eloh-payment-gateway-api
    env: python
    plan: starter
    buildCommand: |
      pip install --upgrade pip
      pip install -r requirements-production.txt || pip install -r requirements-minimal.txt || pip install -r requirements.txt
      python setup_database.py --sample-data
    startCommand: uvicorn main:app --host 0.0.0.0 --port $PORT
    envVars:
      - key: APP_NAME
        value: ELOH Processing Payment Gateway
      - key: ENVIRONMENT
        value: production
      - key: DEBUG
        value: false
      - key: SECRET_KEY
        generateValue: true
      - key: DATABASE_URL
        fromDatabase:
          name: eloh-gateway-db
          property: connectionString
      - key: LOG_LEVEL
        value: INFO
      - key: BTCPAY_SERVER_URL
        sync: false
      - key: BTCPAY_API_KEY
        sync: false
      - key: NOWPAYMENTS_API_KEY
        sync: false
    healthCheckPath: /health

databases:
  - name: eloh-gateway-db
    databaseName: eloh_gateway
    user: eloh_user
    plan: starter
"""
    
    try:
        with open("render.yaml", "w") as f:
            f.write(render_config)
        print_status("✅ render.yaml updated", "SUCCESS")
        return True
    except Exception as e:
        print_status(f"❌ Failed to update render.yaml: {e}", "ERROR")
        return False

def main():
    """Main fix function"""
    print("🔧 ELOH Processing Payment Gateway - Deployment Fix")
    print("=" * 60)
    
    # Check if we're in the right directory
    if not Path("main.py").exists():
        print_status("❌ Not in api-gateway directory!", "ERROR")
        print_status("💡 Please run from api-gateway directory: cd api-gateway", "WARNING")
        return 1
    
    success_count = 0
    total_fixes = 5
    
    # Fix 1: Requirements
    if fix_requirements():
        success_count += 1
    
    # Fix 2: Production requirements
    if create_production_requirements():
        success_count += 1
    
    # Fix 3: Dockerfile
    if fix_dockerfile():
        success_count += 1
    
    # Fix 4: Test requirements
    working_req = test_requirements()
    if working_req:
        print_status(f"✅ Found working requirements: {working_req}", "SUCCESS")
        success_count += 1
    
    # Fix 5: Render config
    if create_render_yaml():
        success_count += 1
    
    # Summary
    print("\n" + "=" * 60)
    print_status(f"Fixes completed: {success_count}/{total_fixes}", 
                "SUCCESS" if success_count == total_fixes else "WARNING")
    
    if success_count >= 4:
        print_status("🎉 Deployment should work now!", "SUCCESS")
        print_status("\n📋 Next steps:", "INFO")
        print_status("1. Test locally: python debug.py", "INFO")
        print_status("2. Commit changes: git add . && git commit -m 'Fix deployment'", "INFO")
        print_status("3. Push to GitHub: git push origin main", "INFO")
        print_status("4. Deploy to Render", "INFO")
        
        if working_req:
            print_status(f"\n💡 Recommended: Use {working_req} for deployment", "INFO")
    else:
        print_status("⚠️ Some fixes failed. Check the errors above.", "WARNING")
    
    return 0 if success_count >= 4 else 1

if __name__ == "__main__":
    sys.exit(main())
