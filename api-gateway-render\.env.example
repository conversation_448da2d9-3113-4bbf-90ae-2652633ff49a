# ELOH Processing Payment Gateway - Environment Configuration (Render)

# Application Settings
APP_NAME=ELOH Processing Payment Gateway
APP_VERSION=1.0.0
ENVIRONMENT=production
DEBUG=false

# API Settings
API_HOST=0.0.0.0
API_PORT=8000
API_PREFIX=/v1

# Security Settings
SECRET_KEY=your-super-secret-key-change-this-in-production
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Database Settings (Ren<PERSON> will auto-populate DATABASE_URL)
# DATABASE_URL=postgresql://user:password@host:port/database

# Logging Settings
LOG_LEVEL=INFO
LOG_FORMAT=json

# Gateway Credentials - SET THESE IN RENDER DASHBOARD

# BTCPay Server (Recommended for Dominica)
BTCPAY_SERVER_URL=https://your-btcpay-server.com
BTCPAY_API_KEY=your_btcpay_api_key
BTCPAY_STORE_ID=your_btcpay_store_id
BTCPAY_WEBHOOK_SECRET=your_btcpay_webhook_secret

# NowPayments (Recommended for Dominica)
NOWPAYMENTS_API_KEY=your_nowpayments_api_key
NOWPAYMENTS_IPN_SECRET=your_nowpayments_ipn_secret
NOWPAYMENTS_ENVIRONMENT=production

# Stripe (Limited availability in Dominica - optional)
# STRIPE_SECRET_KEY=sk_live_your_stripe_secret_key
# STRIPE_PUBLISHABLE_KEY=pk_live_your_stripe_publishable_key
# STRIPE_WEBHOOK_SECRET=whsec_your_stripe_webhook_secret

# Square (Not available in Dominica - optional)
# SQUARE_APPLICATION_ID=your_square_application_id
# SQUARE_ACCESS_TOKEN=your_square_access_token
# SQUARE_WEBHOOK_SIGNATURE_KEY=your_square_webhook_signature_key
# SQUARE_ENVIRONMENT=production

# Rate Limiting
RATE_LIMIT_ENABLED=true
RATE_LIMIT_REQUESTS_PER_MINUTE=100

# Monitoring and Observability
ENABLE_METRICS=true
METRICS_ENDPOINT=/metrics

# Cache Settings
CACHE_TTL=300
CACHE_ENABLED=true
