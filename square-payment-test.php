<?php
require_once "includes/square-gateway.php";
include "header.php";

$squareGateway = new Square_Gateway();
?>

<main>
  <section class="hero">
    <h1>Square Payment Form Test</h1>
  </section>

  <section class="section">
    <div style="max-width: 800px; margin: 0 auto;">
      
      <!-- Test Status -->
      <div style="background: #d4edda; color: #155724; padding: 20px; border-radius: 10px; margin-bottom: 30px;">
        <h3>🧪 Square Payment Form Testing</h3>
        <p>This page tests the complete Square payment form with card details entry and digital wallet options.</p>
        <p><strong>Status:</strong> <?php echo $squareGateway->getStatus(); ?></p>
      </div>

      <!-- Test Instructions -->
      <div style="background: #fff3cd; color: #856404; padding: 20px; border-radius: 10px; margin-bottom: 30px;">
        <h3>📋 Test Instructions</h3>
        <ol>
          <li><strong>Card Form:</strong> Should show card number, expiry, CVV input fields</li>
          <li><strong>Apple Pay:</strong> Should show Apple Pay button (if supported)</li>
          <li><strong>Google Pay:</strong> Should show Google Pay button (if supported)</li>
          <li><strong>Tokenization:</strong> Should generate tokens before backend submission</li>
          <li><strong>Error Handling:</strong> Should show clear error messages</li>
        </ol>
        
        <h4>💳 Test Card Numbers (Sandbox):</h4>
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; font-family: monospace; font-size: 0.9em;">
          <div>
            <strong>✅ Successful:</strong>
            <ul>
              <li>Visa: 4111 1111 1111 1111</li>
              <li>Mastercard: 5555 5555 5555 4444</li>
            </ul>
          </div>
          <div>
            <strong>❌ Test Failures:</strong>
            <ul>
              <li>Declined: 4000 0000 0000 0002</li>
              <li>Insufficient: 4000 0000 0000 9995</li>
            </ul>
          </div>
        </div>
        <p><strong>Expiry:</strong> Any future date (12/25) | <strong>CVV:</strong> Any 3 digits (123)</p>
      </div>

      <!-- Test Payment Form -->
      <div style="background: white; padding: 30px; border-radius: 10px; border: 1px solid #ddd; margin-bottom: 30px;">
        <h3>💳 Test Payment Form</h3>
        
        <!-- Customer Information -->
        <div style="margin-bottom: 25px;">
          <h4>Customer Information</h4>
          
          <div style="margin-bottom: 15px;">
            <label for="test-email" style="display: block; margin-bottom: 5px; font-weight: bold;">Email:</label>
            <input type="email" id="email" value="<EMAIL>"
                   style="width: 100%; padding: 10px; border: 1px solid #ccc; border-radius: 5px;">
          </div>
          
          <div style="margin-bottom: 15px;">
            <label for="test-service" style="display: block; margin-bottom: 5px; font-weight: bold;">Service:</label>
            <select id="service" style="width: 100%; padding: 10px; border: 1px solid #ccc; border-radius: 5px;">
              <option value="consulting">Crypto Consulting ($150)</option>
              <option value="mining-pool">Mining Pool ($200)</option>
              <option value="analysis">Market Analysis ($99)</option>
            </select>
          </div>
          
          <div style="margin-bottom: 15px;">
            <label for="test-description" style="display: block; margin-bottom: 5px; font-weight: bold;">Description:</label>
            <textarea id="description" rows="2" style="width: 100%; padding: 10px; border: 1px solid #ccc; border-radius: 5px;">Test payment for Square integration</textarea>
          </div>
          
          <input type="hidden" name="payment_type" value="service">
        </div>

        <!-- Square Payment Form -->
        <?php 
        echo $squareGateway->generatePaymentForm(150, 'USD', [
            'payment_type' => 'service',
            'show_customer_fields' => false
        ]); 
        ?>
      </div>

      <!-- Expected Results -->
      <div style="background: #e7f3ff; color: #0c5460; padding: 20px; border-radius: 10px; margin-bottom: 30px;">
        <h3>✅ Expected Results</h3>
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
          <div>
            <h4>Card Payment Section:</h4>
            <ul>
              <li>✅ Card number input field</li>
              <li>✅ Expiry date input field</li>
              <li>✅ CVV input field</li>
              <li>✅ "Pay $150 with Card" button</li>
              <li>✅ Real-time validation</li>
            </ul>
          </div>
          <div>
            <h4>Digital Wallets:</h4>
            <ul>
              <li>✅ Apple Pay button (if supported)</li>
              <li>✅ Google Pay button (if supported)</li>
              <li>✅ Automatic detection</li>
              <li>✅ Proper styling</li>
              <li>✅ Click handlers working</li>
            </ul>
          </div>
        </div>
        
        <h4>JavaScript Console Output:</h4>
        <div style="background: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace; font-size: 0.8em;">
          <p>Expected console messages:</p>
          <ul>
            <li>"Square Payment Form initializing..."</li>
            <li>"Initializing Square with config: {...}"</li>
            <li>"Card payment method initialized"</li>
            <li>"Apple Pay initialized" or "Apple Pay not available"</li>
            <li>"Google Pay initialized" or "Google Pay not available"</li>
            <li>"Square initialization complete"</li>
          </ul>
        </div>
      </div>

      <!-- Troubleshooting -->
      <div style="background: #f8d7da; color: #721c24; padding: 20px; border-radius: 10px; margin-bottom: 30px;">
        <h3>🔧 Troubleshooting</h3>
        
        <h4>If Card Form Doesn't Appear:</h4>
        <ul>
          <li>Check browser console for JavaScript errors</li>
          <li>Verify Square.js is loading from CDN</li>
          <li>Ensure Square credentials are configured</li>
          <li>Check network connectivity</li>
        </ul>
        
        <h4>If Digital Wallets Don't Show:</h4>
        <ul>
          <li>Apple Pay requires Safari or iOS device</li>
          <li>Google Pay requires Chrome or Android device</li>
          <li>Both require HTTPS in production</li>
          <li>Check console for availability messages</li>
        </ul>
        
        <h4>If Payment Processing Fails:</h4>
        <ul>
          <li>Verify Square SDK is installed: <code>composer require square/square</code></li>
          <li>Check Square credentials in <code>includes/square-config.php</code></li>
          <li>Ensure <code>square-process-payment.php</code> is accessible</li>
          <li>Check server error logs</li>
        </ul>
      </div>

      <!-- Navigation -->
      <div style="text-align: center; margin: 30px 0;">
        <a href="square-payment-form.php?type=service&service=consulting&amount=150" 
           class="cta-button" style="text-decoration: none; margin-right: 10px;">
          🧪 Test Full Payment Form
        </a>
        <a href="square-test.php" 
           class="cta-button" style="text-decoration: none; margin-right: 10px; background: #28a745;">
          📊 Square Integration Test
        </a>
        <a href="multi-gateway-payment-form.php" 
           class="cta-button" style="text-decoration: none; background: #6c757d;">
          💳 Multi-Gateway Form
        </a>
      </div>
    </div>
  </section>
</main>

<script>
console.log('Square Payment Test Page loaded');

// Monitor for Square initialization
let checkCount = 0;
const checkInterval = setInterval(() => {
  checkCount++;
  
  if (window.Square) {
    console.log('✅ Square.js loaded successfully');
    clearInterval(checkInterval);
  } else if (checkCount > 10) {
    console.error('❌ Square.js failed to load after 10 seconds');
    clearInterval(checkInterval);
  }
}, 1000);

// Monitor for payment form initialization
setTimeout(() => {
  const cardContainer = document.getElementById('card-container');
  const applePayButton = document.getElementById('apple-pay-button');
  const googlePayButton = document.getElementById('google-pay-button');
  const cardButton = document.getElementById('card-button');
  
  console.log('=== SQUARE FORM VERIFICATION ===');
  console.log('Card container:', cardContainer ? '✅ Found' : '❌ Missing');
  console.log('Apple Pay button:', applePayButton ? '✅ Found' : '❌ Missing');
  console.log('Google Pay button:', googlePayButton ? '✅ Found' : '❌ Missing');
  console.log('Card button:', cardButton ? '✅ Found' : '❌ Missing');
  
  if (cardButton) {
    console.log('Card button text:', cardButton.textContent);
    console.log('Card button disabled:', cardButton.disabled);
  }
}, 3000);
</script>

<?php include "footer.php"; ?>
