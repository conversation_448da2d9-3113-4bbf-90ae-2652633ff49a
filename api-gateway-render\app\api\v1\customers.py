"""
Customer management API endpoints
"""

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import Dict, Any
import logging

from ...database.connection import get_db_session
from ...database.models import Customer

router = APIRouter()
logger = logging.getLogger(__name__)


@router.post("/customers")
async def create_customer(
    customer_data: Dict[str, Any],
    db: Session = Depends(get_db_session)
):
    """Create a new customer"""
    try:
        customer = Customer(
            email=customer_data.get("email"),
            name=customer_data.get("name"),
            phone=customer_data.get("phone"),
            tenant_id=1  # Default tenant for now
        )
        
        db.add(customer)
        db.commit()
        db.refresh(customer)
        
        return {
            "customer_id": customer.customer_id,
            "email": customer.email,
            "name": customer.name,
            "created_at": customer.created_at.isoformat()
        }
        
    except Exception as e:
        logger.error(f"Customer creation failed: {e}")
        raise HTTPException(status_code=400, detail="Customer creation failed")


@router.get("/customers/{customer_id}")
async def get_customer(
    customer_id: str,
    db: Session = Depends(get_db_session)
):
    """Get customer details"""
    customer = db.query(Customer).filter(Customer.customer_id == customer_id).first()
    
    if not customer:
        raise HTTPException(status_code=404, detail="Customer not found")
    
    return {
        "customer_id": customer.customer_id,
        "email": customer.email,
        "name": customer.name,
        "phone": customer.phone,
        "created_at": customer.created_at.isoformat()
    }
