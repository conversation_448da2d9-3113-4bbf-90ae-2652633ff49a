<?php
/**
 * ELOH Processing Checkout Widget Configuration
 * Handles widget settings, validation, and customization options
 */

class Widget_Config {
    
    private $default_config = [
        // Appearance
        'theme' => 'light', // light, dark, auto
        'primary_color' => '#667eea',
        'accent_color' => '#764ba2',
        'border_radius' => '12px',
        'font_family' => 'Inter, sans-serif',
        
        // Branding
        'show_logo' => true,
        'logo_url' => '',
        'company_name' => '',
        'custom_css' => '',
        
        // Payment Options
        'enabled_gateways' => ['btcpay', 'nowpayments'],
        'default_gateway' => 'btcpay',
        'enabled_currencies' => ['BTC', 'ETH', 'USDT'],
        'min_amount' => 5.00,
        'max_amount' => 10000.00,
        'currency' => 'USD',
        
        // Widget Behavior
        'auto_resize' => true,
        'show_powered_by' => true,
        'success_redirect' => '',
        'cancel_redirect' => '',
        'webhook_url' => '',
        
        // Security
        'allowed_domains' => [],
        'api_key' => '',
        'require_email' => true,
        'require_description' => false,
        
        // Customization
        'title' => 'Complete Your Payment',
        'description' => 'Secure cryptocurrency payment processing',
        'button_text' => 'Pay Now',
        'success_message' => 'Payment completed successfully!',
        'error_message' => 'Payment failed. Please try again.',
        
        // Advanced
        'debug_mode' => false,
        'timeout' => 300, // 5 minutes
        'language' => 'en'
    ];
    
    private $config;
    
    public function __construct($widget_id = null) {
        $this->config = $this->default_config;
        
        if ($widget_id) {
            $this->loadConfig($widget_id);
        }
    }
    
    /**
     * Load configuration from database or file
     */
    private function loadConfig($widget_id) {
        // For now, load from JSON file
        // In production, this would load from database
        $config_file = __DIR__ . "/configs/{$widget_id}.json";
        
        if (file_exists($config_file)) {
            $saved_config = json_decode(file_get_contents($config_file), true);
            if ($saved_config) {
                $this->config = array_merge($this->config, $saved_config);
            }
        }
    }
    
    /**
     * Save configuration
     */
    public function saveConfig($widget_id, $config) {
        $config_dir = __DIR__ . "/configs";
        if (!is_dir($config_dir)) {
            mkdir($config_dir, 0755, true);
        }
        
        $validated_config = $this->validateConfig($config);
        $config_file = $config_dir . "/{$widget_id}.json";
        
        return file_put_contents($config_file, json_encode($validated_config, JSON_PRETTY_PRINT));
    }
    
    /**
     * Validate configuration values
     */
    private function validateConfig($config) {
        $validated = [];
        
        // Validate theme
        $validated['theme'] = in_array($config['theme'] ?? '', ['light', 'dark', 'auto']) 
            ? $config['theme'] : $this->default_config['theme'];
        
        // Validate colors (hex format)
        $validated['primary_color'] = $this->validateColor($config['primary_color'] ?? '') 
            ?: $this->default_config['primary_color'];
        $validated['accent_color'] = $this->validateColor($config['accent_color'] ?? '') 
            ?: $this->default_config['accent_color'];
        
        // Validate amounts
        $validated['min_amount'] = max(1.00, floatval($config['min_amount'] ?? 5.00));
        $validated['max_amount'] = min(100000.00, floatval($config['max_amount'] ?? 10000.00));
        
        // Validate gateways
        $available_gateways = ['btcpay', 'nowpayments', 'square'];
        $enabled_gateways = $config['enabled_gateways'] ?? [];
        $validated['enabled_gateways'] = array_intersect($enabled_gateways, $available_gateways);
        
        if (empty($validated['enabled_gateways'])) {
            $validated['enabled_gateways'] = ['btcpay'];
        }
        
        // Validate default gateway
        $validated['default_gateway'] = in_array($config['default_gateway'] ?? '', $validated['enabled_gateways'])
            ? $config['default_gateway'] : $validated['enabled_gateways'][0];
        
        // Validate domains
        $validated['allowed_domains'] = array_filter(
            $config['allowed_domains'] ?? [],
            function($domain) {
                return filter_var($domain, FILTER_VALIDATE_DOMAIN, FILTER_FLAG_HOSTNAME);
            }
        );
        
        // Validate URLs
        $validated['success_redirect'] = filter_var($config['success_redirect'] ?? '', FILTER_VALIDATE_URL) 
            ? $config['success_redirect'] : '';
        $validated['cancel_redirect'] = filter_var($config['cancel_redirect'] ?? '', FILTER_VALIDATE_URL) 
            ? $config['cancel_redirect'] : '';
        $validated['webhook_url'] = filter_var($config['webhook_url'] ?? '', FILTER_VALIDATE_URL) 
            ? $config['webhook_url'] : '';
        
        // Sanitize text fields
        $text_fields = ['company_name', 'title', 'description', 'button_text', 'success_message', 'error_message'];
        foreach ($text_fields as $field) {
            $validated[$field] = htmlspecialchars(strip_tags($config[$field] ?? $this->default_config[$field]));
        }
        
        // Boolean fields
        $bool_fields = ['show_logo', 'auto_resize', 'show_powered_by', 'require_email', 'require_description', 'debug_mode'];
        foreach ($bool_fields as $field) {
            $validated[$field] = !empty($config[$field]);
        }
        
        // Merge with defaults for any missing fields
        return array_merge($this->default_config, $validated);
    }
    
    /**
     * Validate hex color format
     */
    private function validateColor($color) {
        return preg_match('/^#[a-f0-9]{6}$/i', $color) ? $color : false;
    }
    
    /**
     * Get configuration value
     */
    public function get($key, $default = null) {
        return $this->config[$key] ?? $default;
    }
    
    /**
     * Get all configuration
     */
    public function getAll() {
        return $this->config;
    }
    
    /**
     * Check if domain is allowed
     */
    public function isDomainAllowed($domain) {
        $allowed_domains = $this->get('allowed_domains', []);
        
        // If no domains specified, allow all
        if (empty($allowed_domains)) {
            return true;
        }
        
        // Check exact match or subdomain
        foreach ($allowed_domains as $allowed) {
            if ($domain === $allowed || str_ends_with($domain, '.' . $allowed)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Generate widget CSS based on configuration
     */
    public function generateCSS() {
        $css = ":root {\n";
        $css .= "  --widget-primary: {$this->get('primary_color')};\n";
        $css .= "  --widget-accent: {$this->get('accent_color')};\n";
        $css .= "  --widget-radius: {$this->get('border_radius')};\n";
        $css .= "  --widget-font: {$this->get('font_family')};\n";
        $css .= "}\n\n";
        
        // Add theme-specific styles
        if ($this->get('theme') === 'dark') {
            $css .= "body { background: #1a1a2e; color: #f7fafc; }\n";
        }
        
        // Add custom CSS
        $custom_css = $this->get('custom_css');
        if ($custom_css) {
            $css .= "\n/* Custom CSS */\n" . $custom_css . "\n";
        }
        
        return $css;
    }
    
    /**
     * Generate JavaScript configuration
     */
    public function generateJSConfig() {
        return [
            'theme' => $this->get('theme'),
            'autoResize' => $this->get('auto_resize'),
            'debugMode' => $this->get('debug_mode'),
            'timeout' => $this->get('timeout'),
            'successRedirect' => $this->get('success_redirect'),
            'cancelRedirect' => $this->get('cancel_redirect')
        ];
    }
}
?>
