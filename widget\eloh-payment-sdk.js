/**
 * ELOH Processing Universal Payment SDK
 * Framework-agnostic JavaScript SDK for Node.js, React, Flutter, etc.
 * 
 * @version 2.0.0
 * <AUTHOR> Processing
 */

class ELOHPaymentSDK {
    constructor(config = {}) {
        this.config = {
            baseUrl: config.baseUrl || 'https://elohprocessing.infy.uk',
            apiEndpoint: config.apiEndpoint || 'https://elohprocessing.infy.uk/api',
            widgetUrl: config.widgetUrl || 'https://elohprocessing.infy.uk/widget/universal-payment-widget.html',
            environment: config.environment || 'production', // 'production' or 'sandbox'
            debug: config.debug || false,
            timeout: config.timeout || 30000,
            ...config
        };
        
        this.instances = new Map();
        
        if (this.config.debug) {
            console.log('🚀 ELOH Payment SDK initialized', this.config);
        }
    }
    
    /**
     * Create a payment widget instance
     */
    createWidget(options) {
        const config = this.validateWidgetConfig(options);
        const instanceId = this.generateInstanceId();
        
        const widget = new ELOHPaymentWidget(instanceId, config, this.config);
        this.instances.set(instanceId, widget);
        
        return widget;
    }
    
    /**
     * Create payment directly via API (for server-side)
     */
    async createPayment(paymentData) {
        try {
            const orderId = this.generateOrderId();
            const payload = {
                ...paymentData,
                order_id: orderId,
                currency: paymentData.currency || 'USD',
                source: 'sdk'
            };
            
            const endpoint = `${this.config.apiEndpoint}/${paymentData.gateway}-create.php`;
            
            const response = await fetch(endpoint, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                },
                body: JSON.stringify(payload),
                signal: AbortSignal.timeout(this.config.timeout)
            });
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const result = await response.json();
            
            if (this.config.debug) {
                console.log('Payment created:', result);
            }
            
            return result;
            
        } catch (error) {
            if (this.config.debug) {
                console.error('Payment creation failed:', error);
            }
            throw error;
        }
    }
    
    /**
     * Get payment status
     */
    async getPaymentStatus(orderId) {
        try {
            const response = await fetch(`${this.config.apiEndpoint}/payment-status.php`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ order_id: orderId }),
                signal: AbortSignal.timeout(this.config.timeout)
            });
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            return await response.json();
            
        } catch (error) {
            if (this.config.debug) {
                console.error('Status check failed:', error);
            }
            throw error;
        }
    }
    
    /**
     * Get available payment gateways
     */
    async getGateways() {
        try {
            const response = await fetch(`${this.config.apiEndpoint}/gateways.php`, {
                signal: AbortSignal.timeout(this.config.timeout)
            });
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            return await response.json();
            
        } catch (error) {
            if (this.config.debug) {
                console.error('Gateway fetch failed:', error);
            }
            throw error;
        }
    }
    
    /**
     * Validate widget configuration
     */
    validateWidgetConfig(options) {
        if (!options || typeof options !== 'object') {
            throw new Error('Widget options must be an object');
        }
        
        if (!options.container) {
            throw new Error('Container element or selector is required');
        }
        
        const defaults = {
            width: '420px',
            height: 'auto',
            theme: 'light',
            autoResize: true,
            showLoader: true,
            onLoad: null,
            onSuccess: null,
            onError: null,
            onPaymentCreated: null
        };
        
        return { ...defaults, ...options };
    }
    
    /**
     * Generate unique instance ID
     */
    generateInstanceId() {
        return 'eloh_widget_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }
    
    /**
     * Generate unique order ID
     */
    generateOrderId() {
        return 'SDK_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }
    
    /**
     * Get widget instance
     */
    getInstance(instanceId) {
        return this.instances.get(instanceId);
    }
    
    /**
     * Destroy widget instance
     */
    destroyWidget(instanceId) {
        const widget = this.instances.get(instanceId);
        if (widget) {
            widget.destroy();
            this.instances.delete(instanceId);
        }
    }
    
    /**
     * Destroy all widgets
     */
    destroyAll() {
        this.instances.forEach((widget, instanceId) => {
            widget.destroy();
        });
        this.instances.clear();
    }
}

/**
 * Payment Widget Class
 */
class ELOHPaymentWidget {
    constructor(instanceId, config, sdkConfig) {
        this.instanceId = instanceId;
        this.config = config;
        this.sdkConfig = sdkConfig;
        this.container = null;
        this.iframe = null;
        this.isLoaded = false;
        this.isDestroyed = false;
        
        this.init();
    }
    
    init() {
        this.container = this.getContainer();
        if (!this.container) {
            throw new Error('Container not found: ' + this.config.container);
        }
        
        this.createIframe();
        this.setupEventListeners();
        this.loadWidget();
    }
    
    getContainer() {
        if (typeof this.config.container === 'string') {
            return document.querySelector(this.config.container);
        } else if (this.config.container instanceof Element) {
            return this.config.container;
        }
        return null;
    }
    
    createIframe() {
        this.iframe = document.createElement('iframe');
        this.iframe.id = this.instanceId;
        this.iframe.style.width = this.config.width;
        this.iframe.style.height = this.config.height === 'auto' ? '600px' : this.config.height;
        this.iframe.style.border = 'none';
        this.iframe.style.borderRadius = '12px';
        this.iframe.style.boxShadow = '0 10px 40px rgba(0, 0, 0, 0.1)';
        this.iframe.style.transition = 'height 0.3s ease';
        this.iframe.setAttribute('allowtransparency', 'true');
        this.iframe.setAttribute('scrolling', 'no');
        
        // Security attributes
        this.iframe.setAttribute('sandbox', 'allow-scripts allow-forms allow-same-origin allow-top-navigation allow-popups');
        
        this.container.appendChild(this.iframe);
    }
    
    setupEventListeners() {
        const self = this;
        
        // Listen for messages from iframe
        window.addEventListener('message', function(event) {
            if (event.source !== self.iframe.contentWindow) {
                return;
            }
            
            self.handleMessage(event.data);
        });
        
        // Handle iframe load
        this.iframe.addEventListener('load', function() {
            self.isLoaded = true;
            self.hideLoader();
            
            if (typeof self.config.onLoad === 'function') {
                self.config.onLoad(self);
            }
        });
        
        // Handle iframe error
        this.iframe.addEventListener('error', function() {
            self.showError('Failed to load payment widget');
            
            if (typeof self.config.onError === 'function') {
                self.config.onError(self, 'Failed to load widget');
            }
        });
    }
    
    handleMessage(data) {
        if (!data || typeof data !== 'object') {
            return;
        }
        
        switch (data.type) {
            case 'eloh_widget_resize':
                if (this.config.autoResize && data.height) {
                    this.iframe.style.height = data.height + 'px';
                }
                break;
                
            case 'eloh_payment_success':
                if (typeof this.config.onSuccess === 'function') {
                    this.config.onSuccess(this, data.data);
                }
                break;
                
            case 'eloh_payment_error':
                if (typeof this.config.onError === 'function') {
                    this.config.onError(this, data.data);
                }
                break;
                
            case 'eloh_payment_created':
                if (typeof this.config.onPaymentCreated === 'function') {
                    this.config.onPaymentCreated(this, data.data);
                }
                break;
        }
    }
    
    loadWidget() {
        if (this.config.showLoader) {
            this.showLoader();
        }
        
        const params = new URLSearchParams();
        
        // Add configuration parameters
        if (this.config.amount) params.append('amount', this.config.amount);
        if (this.config.email) params.append('email', this.config.email);
        if (this.config.description) params.append('description', this.config.description);
        if (this.config.gateway) params.append('gateway', this.config.gateway);
        if (this.config.theme) params.append('theme', this.config.theme);
        
        const widgetUrl = this.sdkConfig.widgetUrl + '?' + params.toString();
        this.iframe.src = widgetUrl;
    }
    
    showLoader() {
        const loader = document.createElement('div');
        loader.id = this.instanceId + '_loader';
        loader.style.cssText = `
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            z-index: 1000;
            background: rgba(255, 255, 255, 0.95);
            padding: 24px;
            border-radius: 12px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
        `;
        
        loader.innerHTML = `
            <div style="
                width: 40px;
                height: 40px;
                border: 3px solid #e2e8f0;
                border-top: 3px solid #667eea;
                border-radius: 50%;
                animation: eloh-spin 1s linear infinite;
                margin: 0 auto 16px;
            "></div>
            <div style="color: #6b7280; font-size: 14px; font-family: Inter, sans-serif;">Loading payment widget...</div>
        `;
        
        // Add CSS animation if not already added
        if (!document.getElementById('eloh-widget-styles')) {
            const style = document.createElement('style');
            style.id = 'eloh-widget-styles';
            style.textContent = `
                @keyframes eloh-spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                }
            `;
            document.head.appendChild(style);
        }
        
        this.container.style.position = 'relative';
        this.container.appendChild(loader);
    }
    
    hideLoader() {
        const loader = document.getElementById(this.instanceId + '_loader');
        if (loader) {
            loader.remove();
        }
    }
    
    showError(message) {
        this.hideLoader();
        
        const error = document.createElement('div');
        error.style.cssText = `
            background: #fef2f2;
            color: #dc2626;
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            font-family: Inter, sans-serif;
            font-size: 14px;
            border: 1px solid #fecaca;
        `;
        error.textContent = message;
        
        this.container.innerHTML = '';
        this.container.appendChild(error);
    }
    
    updateConfig(newConfig) {
        Object.assign(this.config, newConfig);
        this.loadWidget();
    }
    
    destroy() {
        if (this.isDestroyed) {
            return;
        }
        
        this.isDestroyed = true;
        
        if (this.iframe && this.iframe.parentNode) {
            this.iframe.parentNode.removeChild(this.iframe);
        }
        
        this.hideLoader();
        
        // Clean up references
        this.container = null;
        this.iframe = null;
        this.config = null;
    }
}

// Export for different environments
if (typeof module !== 'undefined' && module.exports) {
    // Node.js
    module.exports = { ELOHPaymentSDK, ELOHPaymentWidget };
} else if (typeof window !== 'undefined') {
    // Browser
    window.ELOHPaymentSDK = ELOHPaymentSDK;
    window.ELOHPaymentWidget = ELOHPaymentWidget;
}

// Auto-initialize for data attributes
if (typeof document !== 'undefined') {
    document.addEventListener('DOMContentLoaded', function() {
        const autoWidgets = document.querySelectorAll('[data-eloh-widget]');
        
        if (autoWidgets.length > 0) {
            const sdk = new ELOHPaymentSDK();
            
            autoWidgets.forEach(function(element) {
                const config = {
                    container: element,
                    amount: element.getAttribute('data-amount'),
                    email: element.getAttribute('data-email'),
                    description: element.getAttribute('data-description'),
                    gateway: element.getAttribute('data-gateway'),
                    theme: element.getAttribute('data-theme') || 'light'
                };
                
                // Remove null/undefined values
                Object.keys(config).forEach(key => {
                    if (config[key] === null || config[key] === undefined) {
                        delete config[key];
                    }
                });
                
                try {
                    sdk.createWidget(config);
                } catch (error) {
                    console.error('Failed to auto-initialize ELOH widget:', error);
                }
            });
        }
    });
}
