<?php
require_once "includes/square-business-manager.php";
include "header.php";

$businessManager = new Square_Business_Manager();
$dashboardData = $businessManager->getDashboardData();
?>

<main>
  <section class="hero">
    <h1>Square Business Management Dashboard</h1>
  </section>

  <section class="section">
    <div style="max-width: 1200px; margin: 0 auto;">
      
      <!-- Status Overview -->
      <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin-bottom: 30px;">
        <h2>🎯 Square Integration Status</h2>
        <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 20px;">
          <div style="background: white; padding: 15px; border-radius: 8px; text-align: center;">
            <h3>💳 Payment Processing</h3>
            <p style="color: #28a745; font-weight: bold;">Ready for Activation</p>
            <small>Credit cards, digital wallets, ACH</small>
          </div>
          <div style="background: white; padding: 15px; border-radius: 8px; text-align: center;">
            <h3>📦 Product Management</h3>
            <p style="color: #0077cc; font-weight: bold;">Configured</p>
            <small>Services catalog ready</small>
          </div>
          <div style="background: white; padding: 15px; border-radius: 8px; text-align: center;">
            <h3>👥 Customer Management</h3>
            <p style="color: #0077cc; font-weight: bold;">Ready</p>
            <small>CRM and loyalty programs</small>
          </div>
        </div>
      </div>

      <!-- Services Management -->
      <div style="background: white; padding: 20px; border-radius: 10px; margin-bottom: 30px; border: 1px solid #ddd;">
        <h2>🔧 Services & Products</h2>
        
        <?php if ($dashboardData['services']['success']): ?>
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
          <?php foreach ($dashboardData['services']['services'] as $service): ?>
          <div style="background: #f8f9fa; padding: 15px; border-radius: 8px;">
            <h4><?php echo htmlspecialchars($service['name']); ?></h4>
            <p style="color: #666; margin: 10px 0;"><?php echo htmlspecialchars($service['description']); ?></p>
            <div style="display: flex; justify-content: space-between; align-items: center;">
              <span style="font-weight: bold; color: #28a745;">$<?php echo number_format($service['price'], 2); ?></span>
              <span style="background: #0077cc; color: white; padding: 4px 8px; border-radius: 4px; font-size: 0.8em;">
                <?php echo ucfirst($service['category']); ?>
              </span>
            </div>
          </div>
          <?php endforeach; ?>
        </div>
        <?php else: ?>
        <p style="color: #dc3545;">Error loading services: <?php echo htmlspecialchars($dashboardData['services']['error']); ?></p>
        <?php endif; ?>
        
        <div style="margin-top: 20px; text-align: center;">
          <button onclick="initializeServices()" class="cta-button" style="margin-right: 10px;">
            🚀 Initialize Services in Square
          </button>
          <button onclick="syncInventory()" class="cta-button" style="background: #28a745;">
            🔄 Sync Inventory
          </button>
        </div>
      </div>

      <!-- Square APIs Available -->
      <div style="background: white; padding: 20px; border-radius: 10px; margin-bottom: 30px; border: 1px solid #ddd;">
        <h2>🔌 Available Square APIs</h2>
        <div style="display: grid; grid-template-columns: 1fr 1fr 1fr 1fr; gap: 15px;">
          
          <!-- Payment APIs -->
          <div style="background: #e7f3ff; padding: 15px; border-radius: 8px;">
            <h4>💳 Payment APIs</h4>
            <ul style="font-size: 0.9em; margin: 10px 0; padding-left: 15px;">
              <li>Payments API</li>
              <li>Refunds API</li>
              <li>Cards API</li>
              <li>Disputes API</li>
            </ul>
          </div>
          
          <!-- Business APIs -->
          <div style="background: #f0f8ff; padding: 15px; border-radius: 8px;">
            <h4>🏢 Business APIs</h4>
            <ul style="font-size: 0.9em; margin: 10px 0; padding-left: 15px;">
              <li>Catalog API</li>
              <li>Inventory API</li>
              <li>Orders API</li>
              <li>Invoices API</li>
            </ul>
          </div>
          
          <!-- Customer APIs -->
          <div style="background: #f8f0ff; padding: 15px; border-radius: 8px;">
            <h4>👥 Customer APIs</h4>
            <ul style="font-size: 0.9em; margin: 10px 0; padding-left: 15px;">
              <li>Customers API</li>
              <li>Loyalty API</li>
              <li>Gift Cards API</li>
              <li>Subscriptions API</li>
            </ul>
          </div>
          
          <!-- Management APIs -->
          <div style="background: #fff8f0; padding: 15px; border-radius: 8px;">
            <h4>⚙️ Management APIs</h4>
            <ul style="font-size: 0.9em; margin: 10px 0; padding-left: 15px;">
              <li>Team API</li>
              <li>Labor API</li>
              <li>Bookings API</li>
              <li>Locations API</li>
            </ul>
          </div>
        </div>
      </div>

      <!-- Feature Configuration -->
      <div style="background: white; padding: 20px; border-radius: 10px; margin-bottom: 30px; border: 1px solid #ddd;">
        <h2>⚙️ Feature Configuration</h2>
        <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 20px;">
          
          <?php 
          $features = [
            'payment_processing' => ['icon' => '💳', 'name' => 'Payment Processing', 'status' => 'ready'],
            'inventory_tracking' => ['icon' => '📦', 'name' => 'Inventory Tracking', 'status' => 'ready'],
            'customer_management' => ['icon' => '👥', 'name' => 'Customer Management', 'status' => 'ready'],
            'loyalty_program' => ['icon' => '🎁', 'name' => 'Loyalty Program', 'status' => 'ready'],
            'appointment_booking' => ['icon' => '📅', 'name' => 'Appointment Booking', 'status' => 'ready'],
            'invoicing' => ['icon' => '📄', 'name' => 'Invoicing', 'status' => 'ready'],
            'staff_management' => ['icon' => '👨‍💼', 'name' => 'Staff Management', 'status' => 'ready'],
            'reporting' => ['icon' => '📊', 'name' => 'Analytics & Reporting', 'status' => 'ready'],
            'gift_cards' => ['icon' => '🎫', 'name' => 'Gift Cards', 'status' => 'ready']
          ];
          
          foreach ($features as $key => $feature): ?>
          <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; text-align: center;">
            <div style="font-size: 2em; margin-bottom: 10px;"><?php echo $feature['icon']; ?></div>
            <h4 style="margin: 10px 0;"><?php echo $feature['name']; ?></h4>
            <span style="background: #28a745; color: white; padding: 4px 12px; border-radius: 20px; font-size: 0.8em;">
              ✅ <?php echo ucfirst($feature['status']); ?>
            </span>
          </div>
          <?php endforeach; ?>
        </div>
      </div>

      <!-- Quick Actions -->
      <div style="background: white; padding: 20px; border-radius: 10px; margin-bottom: 30px; border: 1px solid #ddd;">
        <h2>⚡ Quick Actions</h2>
        <div style="display: grid; grid-template-columns: 1fr 1fr 1fr 1fr; gap: 15px;">
          <button onclick="createTestCustomer()" class="cta-button" style="padding: 15px; text-align: center;">
            👤 Create Test Customer
          </button>
          <button onclick="createTestOrder()" class="cta-button" style="padding: 15px; text-align: center; background: #28a745;">
            📦 Create Test Order
          </button>
          <button onclick="generateInvoice()" class="cta-button" style="padding: 15px; text-align: center; background: #6c757d;">
            📄 Generate Invoice
          </button>
          <button onclick="viewReports()" class="cta-button" style="padding: 15px; text-align: center; background: #17a2b8;">
            📊 View Reports
          </button>
        </div>
      </div>

      <!-- Integration Instructions -->
      <div style="background: #fff3cd; color: #856404; padding: 20px; border-radius: 10px; margin-bottom: 30px;">
        <h2>🚀 Activation Instructions</h2>
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
          <div>
            <h3>1. Get Square Credentials</h3>
            <ol style="margin: 10px 0; padding-left: 20px;">
              <li>Create Square Developer Account</li>
              <li>Generate Application ID</li>
              <li>Get Access Token</li>
              <li>Configure Webhook Signature Key</li>
            </ol>
          </div>
          <div>
            <h3>2. Install Square SDK</h3>
            <ol style="margin: 10px 0; padding-left: 20px;">
              <li>Run: <code>composer require square/square</code></li>
              <li>Update configuration files</li>
              <li>Test in sandbox environment</li>
              <li>Deploy to production</li>
            </ol>
          </div>
        </div>
      </div>

      <!-- Navigation -->
      <div style="text-align: center; margin: 30px 0;">
        <a href="multi-gateway-payment-form.php" class="cta-button" style="text-decoration: none; margin-right: 10px;">
          💳 Payment Form
        </a>
        <a href="square-product-manager.php" class="cta-button" style="text-decoration: none; margin-right: 10px; background: #28a745;">
          📦 Product Manager
        </a>
        <a href="square-customer-manager.php" class="cta-button" style="text-decoration: none; margin-right: 10px; background: #6c757d;">
          👥 Customer Manager
        </a>
        <a href="index.php" class="cta-button" style="text-decoration: none; background: #17a2b8;">
          🏠 Homepage
        </a>
      </div>
    </div>
  </section>
</main>

<script>
console.log('Square Admin Dashboard loaded');

// Quick action functions
function initializeServices() {
  if (confirm('Initialize ELOH Processing services in Square catalog?')) {
    // In a real implementation, this would make an AJAX call
    alert('Services would be initialized in Square catalog (requires Square SDK)');
  }
}

function syncInventory() {
  alert('Inventory sync would update Square catalog with current service availability');
}

function createTestCustomer() {
  alert('Test customer creation (requires Square SDK and credentials)');
}

function createTestOrder() {
  alert('Test order creation (requires Square SDK and credentials)');
}

function generateInvoice() {
  alert('Invoice generation (requires Square SDK and credentials)');
}

function viewReports() {
  alert('Analytics and reporting dashboard (requires Square SDK and data)');
}

// Add visual feedback for buttons
document.addEventListener('click', function(e) {
  if (e.target.tagName === 'BUTTON' && e.target.classList.contains('cta-button')) {
    e.target.style.transform = 'scale(0.95)';
    setTimeout(() => {
      e.target.style.transform = 'scale(1)';
    }, 150);
  }
});
</script>

<?php include "footer.php"; ?>
