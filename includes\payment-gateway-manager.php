<?php
/**
 * Payment Gateway Manager for ELOH Processing
 * Manages multiple payment gateways (BTCPay Server, NowPayments)
 */

require_once __DIR__ . '/btcpay-gateway.php';
require_once __DIR__ . '/nowpayments-gateway.php';
require_once __DIR__ . '/square-gateway.php';

class Payment_Gateway_Manager {

    private $availableGateways;

    public function __construct() {
        $this->availableGateways = [
            'btcpay' => [
                'name' => 'BTCPay Server',
                'description' => 'Self-hosted Bitcoin payments with Lightning Network support',
                'supported_currencies' => ['BTC'],
                'features' => ['Lightning Network', 'On-chain Bitcoin', 'Self-hosted', 'No fees'],
                'icon' => '⚡',
                'class' => 'BTCPay_Gateway'
            ],
            'nowpayments' => [
                'name' => 'NowPayments',
                'description' => 'Accept 300+ cryptocurrencies with low fees',
                'supported_currencies' => ['BTC', 'ETH', 'USDT', 'USDC', 'LTC', 'BCH', 'TRX', 'BNB', 'ADA', 'DOT', 'XRP', 'MATIC'],
                'features' => ['300+ Cryptocurrencies', 'Low fees (0.5-1.5%)', 'Easy integration', 'Global support'],
                'icon' => '🌐',
                'class' => 'NowPayments_Gateway'
            ],
            'square' => [
                'name' => 'Square',
                'description' => 'Credit cards, debit cards, and digital wallets',
                'supported_currencies' => ['USD'],
                'features' => ['Credit/Debit Cards', 'Apple Pay', 'Google Pay', 'ACH Transfers', 'PCI Compliant'],
                'icon' => '💳',
                'class' => 'Square_Gateway',
                'status' => 'Ready for activation when available in region'
            ]
        ];
    }

    /**
     * Get available payment gateways
     */
    public function getAvailableGateways() {
        return $this->availableGateways;
    }

    /**
     * Get gateway instance
     */
    public function getGateway($gatewayId) {
        if (!isset($this->availableGateways[$gatewayId])) {
            throw new Exception("Gateway '$gatewayId' not found");
        }

        $gatewayClass = $this->availableGateways[$gatewayId]['class'];

        switch ($gatewayId) {
            case 'btcpay':
                return new BTCPay_Gateway();
            case 'nowpayments':
                return new NowPayments_Gateway(false); // Live mode
            case 'square':
                return new Square_Gateway();
            default:
                throw new Exception("Gateway '$gatewayId' not implemented");
        }
    }

    /**
     * Create payment with specified gateway
     */
    public function createPayment($gatewayId, $amount, $currency, $orderId, $description = null, $customerEmail = null) {
        try {
            $gateway = $this->getGateway($gatewayId);

            switch ($gatewayId) {
                case 'btcpay':
                    return $gateway->createInvoice($amount, 'USD', $orderId, $customerEmail, $description);

                case 'nowpayments':
                    return $gateway->createPayment($amount, $currency, $orderId, $description, $customerEmail);

                case 'square':
                    // Square requires a payment token from the frontend
                    return [
                        'success' => false,
                        'error' => 'Square payments require frontend token generation',
                        'redirect_to_form' => true
                    ];

                default:
                    return [
                        'success' => false,
                        'error' => "Payment creation not implemented for gateway '$gatewayId'"
                    ];
            }

        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Get payment status from specified gateway
     */
    public function getPaymentStatus($gatewayId, $paymentId) {
        try {
            $gateway = $this->getGateway($gatewayId);

            switch ($gatewayId) {
                case 'btcpay':
                    return $gateway->getInvoice($paymentId);

                case 'nowpayments':
                    return $gateway->getPaymentStatus($paymentId);

                case 'square':
                    return $gateway->getPaymentStatus($paymentId);

                default:
                    return [
                        'success' => false,
                        'error' => "Status check not implemented for gateway '$gatewayId'"
                    ];
            }

        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Get supported currencies for a gateway
     */
    public function getSupportedCurrencies($gatewayId) {
        if (!isset($this->availableGateways[$gatewayId])) {
            return [];
        }

        return $this->availableGateways[$gatewayId]['supported_currencies'];
    }

    /**
     * Get all supported currencies across all gateways
     */
    public function getAllSupportedCurrencies() {
        $allCurrencies = [];

        foreach ($this->availableGateways as $gatewayId => $gateway) {
            foreach ($gateway['supported_currencies'] as $currency) {
                if (!in_array($currency, $allCurrencies)) {
                    $allCurrencies[] = $currency;
                }
            }
        }

        return $allCurrencies;
    }

    /**
     * Get recommended gateway for a currency
     */
    public function getRecommendedGateway($currency) {
        $currency = strtoupper($currency);

        // Square for USD (traditional payments)
        if ($currency === 'USD') {
            return 'square';
        }

        // BTCPay Server for Bitcoin (self-hosted, no fees)
        if ($currency === 'BTC') {
            return 'btcpay';
        }

        // NowPayments for other cryptocurrencies
        foreach ($this->availableGateways['nowpayments']['supported_currencies'] as $supportedCurrency) {
            if (strtoupper($supportedCurrency) === $currency) {
                return 'nowpayments';
            }
        }

        // Default to Square for fiat, BTCPay for crypto
        return $currency === 'USD' ? 'square' : 'btcpay';
    }

    /**
     * Get gateway comparison data
     */
    public function getGatewayComparison() {
        return [
            'btcpay' => [
                'pros' => [
                    'No transaction fees',
                    'Self-hosted (full control)',
                    'Lightning Network support',
                    'Privacy-focused',
                    'Open source'
                ],
                'cons' => [
                    'Bitcoin only',
                    'Requires technical setup',
                    'Self-managed'
                ],
                'best_for' => 'Bitcoin payments, privacy, no fees'
            ],
            'nowpayments' => [
                'pros' => [
                    '300+ cryptocurrencies',
                    'Low fees (0.5-1.5%)',
                    'Easy integration',
                    'Global support',
                    'No KYC required'
                ],
                'cons' => [
                    'Transaction fees apply',
                    'Third-party service',
                    'No sandbox for some regions'
                ],
                'best_for' => 'Multiple cryptocurrencies, global reach'
            ],
            'square' => [
                'pros' => [
                    'Credit/debit card support',
                    'Digital wallets (Apple Pay, Google Pay)',
                    'PCI compliance handled',
                    'Professional checkout',
                    'Trusted brand recognition',
                    'ACH bank transfers',
                    'Real-time processing'
                ],
                'cons' => [
                    'Transaction fees (2.6-2.9%)',
                    'USD only',
                    'Not available in all regions',
                    'Requires Square account'
                ],
                'best_for' => 'Traditional payments, credit cards, US customers'
            ]
        ];
    }

    /**
     * Validate gateway configuration
     */
    public function validateGatewayConfig($gatewayId) {
        try {
            $gateway = $this->getGateway($gatewayId);

            if (method_exists($gateway, 'getDebugInfo')) {
                return $gateway->getDebugInfo();
            }

            return ['status' => 'Gateway loaded successfully'];

        } catch (Exception $e) {
            return [
                'error' => $e->getMessage(),
                'status' => 'Configuration error'
            ];
        }
    }

    /**
     * Create widget payment with enhanced error handling
     */
    public function createWidgetPayment($gatewayId, $amount, $currency, $orderId, $description = null, $customerEmail = null, $widgetConfig = []) {
        try {
            // Validate gateway is enabled for widget
            if (!empty($widgetConfig['enabled_gateways']) && !in_array($gatewayId, $widgetConfig['enabled_gateways'])) {
                return [
                    'success' => false,
                    'error' => 'Gateway not enabled for this widget'
                ];
            }

            // Validate amount limits
            $minAmount = $widgetConfig['min_amount'] ?? 5.00;
            $maxAmount = $widgetConfig['max_amount'] ?? 10000.00;

            if ($amount < $minAmount) {
                return [
                    'success' => false,
                    'error' => "Minimum amount is $" . number_format($minAmount, 2)
                ];
            }

            if ($amount > $maxAmount) {
                return [
                    'success' => false,
                    'error' => "Maximum amount is $" . number_format($maxAmount, 2)
                ];
            }

            // Create payment using standard method
            $result = $this->createPayment($gatewayId, $amount, $currency, $orderId, $description, $customerEmail);

            // Add widget-specific data
            if ($result['success']) {
                $result['widget_order_id'] = $orderId;
                $result['widget_gateway'] = $gatewayId;
                $result['created_at'] = time();
            }

            return $result;

        } catch (Exception $e) {
            error_log("Widget payment creation error: " . $e->getMessage());
            return [
                'success' => false,
                'error' => 'Payment processing failed: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Get widget-compatible gateway information
     */
    public function getWidgetGatewayInfo($gatewayId) {
        $gateways = $this->getAvailableGateways();

        if (!isset($gateways[$gatewayId])) {
            return null;
        }

        $gateway = $gateways[$gatewayId];

        return [
            'id' => $gatewayId,
            'name' => $gateway['name'],
            'icon' => $gateway['icon'],
            'description' => $gateway['description'] ?? '',
            'supported_currencies' => $gateway['supported_currencies'],
            'min_amount' => $gateway['min_amount'] ?? 5.00,
            'max_amount' => $gateway['max_amount'] ?? 10000.00,
            'fees' => $gateway['fees'] ?? 'Variable',
            'processing_time' => $gateway['processing_time'] ?? 'Instant'
        ];
    }

    /**
     * Validate widget payment parameters
     */
    public function validateWidgetPayment($gatewayId, $amount, $currency, $email = null, $widgetConfig = []) {
        $errors = [];

        // Validate gateway
        if (!$this->isGatewayAvailable($gatewayId)) {
            $errors[] = 'Payment gateway not available';
        }

        // Validate amount
        if (!is_numeric($amount) || $amount <= 0) {
            $errors[] = 'Invalid payment amount';
        }

        $minAmount = $widgetConfig['min_amount'] ?? 5.00;
        $maxAmount = $widgetConfig['max_amount'] ?? 10000.00;

        if ($amount < $minAmount) {
            $errors[] = "Minimum amount is $" . number_format($minAmount, 2);
        }

        if ($amount > $maxAmount) {
            $errors[] = "Maximum amount is $" . number_format($maxAmount, 2);
        }

        // Validate currency
        $supportedCurrencies = $this->getSupportedCurrencies($gatewayId);
        if (!in_array(strtoupper($currency), array_map('strtoupper', $supportedCurrencies))) {
            $errors[] = 'Currency not supported by selected gateway';
        }

        // Validate email if required
        if (!empty($widgetConfig['require_email'])) {
            if (empty($email)) {
                $errors[] = 'Email address is required';
            } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                $errors[] = 'Invalid email address format';
            }
        }

        return $errors;
    }

    /**
     * Check if gateway is available
     */
    public function isGatewayAvailable($gatewayId) {
        return isset($this->availableGateways[$gatewayId]);
    }
}
