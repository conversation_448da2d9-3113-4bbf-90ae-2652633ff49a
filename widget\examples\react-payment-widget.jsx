/**
 * ELOH Processing React Payment Widget
 * Easy integration for React applications
 */

import React, { useEffect, useRef, useState, useCallback } from 'react';

const ELOHPaymentWidget = ({
  amount,
  email,
  description,
  gateway = 'btcpay',
  theme = 'light',
  width = '420px',
  height = 'auto',
  autoResize = true,
  onSuccess,
  onError,
  onPaymentCreated,
  onLoad,
  className,
  style
}) => {
  const containerRef = useRef(null);
  const widgetRef = useRef(null);
  const sdkRef = useRef(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  // Load ELOH SDK
  const loadSDK = useCallback(() => {
    return new Promise((resolve, reject) => {
      // Check if SDK is already loaded
      if (window.ELOHPaymentSDK) {
        resolve(window.ELOHPaymentSDK);
        return;
      }

      // Create script element
      const script = document.createElement('script');
      script.src = 'https://elohprocessing.infy.uk/widget/eloh-payment-sdk.js';
      script.async = true;
      
      script.onload = () => {
        if (window.ELOHPaymentSDK) {
          resolve(window.ELOHPaymentSDK);
        } else {
          reject(new Error('ELOH SDK failed to load'));
        }
      };
      
      script.onerror = () => {
        reject(new Error('Failed to load ELOH SDK script'));
      };

      document.head.appendChild(script);
    });
  }, []);

  // Initialize widget
  const initializeWidget = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Load SDK
      const ELOHPaymentSDK = await loadSDK();
      
      // Initialize SDK
      if (!sdkRef.current) {
        sdkRef.current = new ELOHPaymentSDK({
          debug: process.env.NODE_ENV === 'development'
        });
      }

      // Destroy existing widget
      if (widgetRef.current) {
        widgetRef.current.destroy();
      }

      // Create new widget
      widgetRef.current = sdkRef.current.createWidget({
        container: containerRef.current,
        amount: amount,
        email: email,
        description: description,
        gateway: gateway,
        theme: theme,
        width: width,
        height: height,
        autoResize: autoResize,
        
        onLoad: (widget) => {
          setIsLoading(false);
          if (onLoad) onLoad(widget);
        },
        
        onSuccess: (widget, data) => {
          if (onSuccess) onSuccess(data);
        },
        
        onError: (widget, error) => {
          setError(error);
          if (onError) onError(error);
        },
        
        onPaymentCreated: (widget, data) => {
          if (onPaymentCreated) onPaymentCreated(data);
        }
      });

    } catch (err) {
      setError(err.message);
      setIsLoading(false);
      if (onError) onError(err);
    }
  }, [amount, email, description, gateway, theme, width, height, autoResize, onLoad, onSuccess, onError, onPaymentCreated, loadSDK]);

  // Initialize on mount and when props change
  useEffect(() => {
    if (containerRef.current) {
      initializeWidget();
    }

    // Cleanup on unmount
    return () => {
      if (widgetRef.current) {
        widgetRef.current.destroy();
        widgetRef.current = null;
      }
    };
  }, [initializeWidget]);

  // Render loading state
  if (isLoading) {
    return (
      <div 
        className={className}
        style={{
          width: width,
          height: height === 'auto' ? '600px' : height,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          background: '#f8fafc',
          borderRadius: '12px',
          border: '1px solid #e2e8f0',
          ...style
        }}
      >
        <div style={{ textAlign: 'center' }}>
          <div style={{
            width: '40px',
            height: '40px',
            border: '3px solid #e2e8f0',
            borderTop: '3px solid #667eea',
            borderRadius: '50%',
            animation: 'spin 1s linear infinite',
            margin: '0 auto 16px'
          }} />
          <div style={{ color: '#6b7280', fontSize: '14px' }}>
            Loading payment widget...
          </div>
        </div>
      </div>
    );
  }

  // Render error state
  if (error) {
    return (
      <div 
        className={className}
        style={{
          width: width,
          padding: '24px',
          background: '#fef2f2',
          color: '#dc2626',
          borderRadius: '12px',
          border: '1px solid #fecaca',
          textAlign: 'center',
          ...style
        }}
      >
        <div style={{ fontSize: '18px', marginBottom: '8px' }}>⚠️</div>
        <div style={{ fontWeight: '600', marginBottom: '4px' }}>Payment Widget Error</div>
        <div style={{ fontSize: '14px' }}>{error}</div>
        <button
          onClick={initializeWidget}
          style={{
            marginTop: '16px',
            padding: '8px 16px',
            background: '#dc2626',
            color: 'white',
            border: 'none',
            borderRadius: '6px',
            cursor: 'pointer',
            fontSize: '14px'
          }}
        >
          Retry
        </button>
      </div>
    );
  }

  // Render widget container
  return (
    <div 
      ref={containerRef}
      className={className}
      style={{
        width: width,
        height: height,
        ...style
      }}
    />
  );
};

// Hook for programmatic payment creation
export const useELOHPayment = () => {
  const sdkRef = useRef(null);

  const initializeSDK = useCallback(async () => {
    if (sdkRef.current) return sdkRef.current;

    try {
      // Load SDK if not already loaded
      if (!window.ELOHPaymentSDK) {
        await new Promise((resolve, reject) => {
          const script = document.createElement('script');
          script.src = 'https://elohprocessing.infy.uk/widget/eloh-payment-sdk.js';
          script.onload = resolve;
          script.onerror = reject;
          document.head.appendChild(script);
        });
      }

      sdkRef.current = new window.ELOHPaymentSDK({
        debug: process.env.NODE_ENV === 'development'
      });

      return sdkRef.current;
    } catch (error) {
      throw new Error('Failed to initialize ELOH SDK: ' + error.message);
    }
  }, []);

  const createPayment = useCallback(async (paymentData) => {
    const sdk = await initializeSDK();
    return sdk.createPayment(paymentData);
  }, [initializeSDK]);

  const getPaymentStatus = useCallback(async (orderId) => {
    const sdk = await initializeSDK();
    return sdk.getPaymentStatus(orderId);
  }, [initializeSDK]);

  const getGateways = useCallback(async () => {
    const sdk = await initializeSDK();
    return sdk.getGateways();
  }, [initializeSDK]);

  return {
    createPayment,
    getPaymentStatus,
    getGateways
  };
};

// Example usage component
export const PaymentExample = () => {
  const [paymentData, setPaymentData] = useState({
    amount: 100.00,
    email: '<EMAIL>',
    description: 'Product Purchase'
  });

  const handlePaymentSuccess = (data) => {
    console.log('Payment successful!', data);
    alert('Payment completed successfully!');
  };

  const handlePaymentError = (error) => {
    console.error('Payment error:', error);
    alert('Payment failed: ' + error.message);
  };

  const handlePaymentCreated = (data) => {
    console.log('Payment created:', data);
    // User will be redirected to payment gateway
  };

  return (
    <div style={{ padding: '20px', maxWidth: '500px', margin: '0 auto' }}>
      <h2>ELOH Processing Payment</h2>
      
      <div style={{ marginBottom: '20px' }}>
        <label>
          Amount: $
          <input
            type="number"
            value={paymentData.amount}
            onChange={(e) => setPaymentData({
              ...paymentData,
              amount: parseFloat(e.target.value)
            })}
            style={{ marginLeft: '8px', padding: '4px' }}
          />
        </label>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <label>
          Email:
          <input
            type="email"
            value={paymentData.email}
            onChange={(e) => setPaymentData({
              ...paymentData,
              email: e.target.value
            })}
            style={{ marginLeft: '8px', padding: '4px', width: '200px' }}
          />
        </label>
      </div>

      <ELOHPaymentWidget
        amount={paymentData.amount}
        email={paymentData.email}
        description={paymentData.description}
        onSuccess={handlePaymentSuccess}
        onError={handlePaymentError}
        onPaymentCreated={handlePaymentCreated}
        style={{ margin: '20px 0' }}
      />
    </div>
  );
};

export default ELOHPaymentWidget;
