# 🚀 ELOH Processing Checkout Widget

An embeddable cryptocurrency payment widget that allows third-party websites to accept payments through ELOH Processing's multiple payment gateways.

## ✨ Features

- **🔒 Secure**: iframe-based embedding with domain validation
- **📱 Responsive**: Mobile-first design that works on all devices
- **🎨 Customizable**: Flexible theming and branding options
- **⚡ Fast**: Lightweight and optimized for performance
- **🔗 Multiple Gateways**: Support for BTCPay Server, NowPayments, and Square
- **🔔 Webhooks**: Real-time payment notifications
- **🌍 Multi-currency**: Support for 300+ cryptocurrencies
- **🎯 Easy Integration**: Multiple integration methods

## 🚀 Quick Start

### 1. JavaScript API (Recommended)

```html
<script src="https://elohprocessing.infy.uk/widget/widget-embed.js"></script>
<div id="payment-widget"></div>
<script>
  ELOHWidget.create({
    widgetId: 'your-widget-id',
    container: '#payment-widget',
    amount: 100.00,
    email: '<EMAIL>'
  });
</script>
```

### 2. Data Attributes

```html
<script src="https://elohprocessing.infy.uk/widget/widget-embed.js"></script>
<div 
  data-eloh-widget="your-widget-id"
  data-amount="100.00"
  data-email="<EMAIL>"
  data-theme="light">
</div>
```

### 3. Direct iframe

```html
<iframe
  src="https://elohprocessing.infy.uk/widget/checkout-widget.php?widget_id=your-widget-id&amount=100.00"
  width="400"
  height="600"
  frameborder="0">
</iframe>
```

## ⚙️ Configuration Options

| Option | Type | Default | Description |
|--------|------|---------|-------------|
| `widgetId` | string | **required** | Your unique widget ID |
| `container` | string/element | **required** | Container selector or DOM element |
| `amount` | number | - | Pre-fill payment amount |
| `email` | string | - | Pre-fill customer email |
| `description` | string | - | Payment description |
| `currency` | string | 'USD' | Currency code |
| `gateway` | string | - | Preferred payment gateway |
| `theme` | string | 'auto' | Theme: 'light', 'dark', 'auto' |
| `width` | string | '100%' | Widget width |
| `height` | string | 'auto' | Widget height |
| `autoResize` | boolean | true | Auto-resize iframe |
| `showLoader` | boolean | true | Show loading indicator |
| `timeout` | number | 30000 | Loading timeout (ms) |

## 🎯 Event Callbacks

```javascript
ELOHWidget.create({
  widgetId: 'your-widget-id',
  container: '#payment-widget',
  
  // Event callbacks
  onLoad: function(widget) {
    console.log('Widget loaded successfully');
  },
  
  onSuccess: function(widget, data) {
    console.log('Payment successful:', data);
    // Redirect to success page or show confirmation
  },
  
  onError: function(widget, error) {
    console.log('Payment error:', error);
    // Handle payment errors
  },
  
  onCancel: function(widget) {
    console.log('Payment cancelled by user');
    // Handle payment cancellation
  }
});
```

## 🔧 Widget Management

### Creating a Widget Instance

```javascript
const widget = ELOHWidget.create({
  widgetId: 'your-widget-id',
  container: '#payment-widget'
});
```

### Updating Widget Configuration

```javascript
widget.updateConfig({
  amount: 150.00,
  theme: 'dark'
});
```

### Destroying a Widget

```javascript
widget.destroy();
// or
ELOHWidget.destroy(widget.instanceId);
```

## 🎨 Customization

### Widget Configuration

Create a widget configuration by saving a JSON file in the `configs/` directory:

```json
{
  "theme": "light",
  "primary_color": "#667eea",
  "accent_color": "#764ba2",
  "border_radius": "12px",
  "font_family": "Inter, sans-serif",
  "show_logo": true,
  "company_name": "Your Company",
  "enabled_gateways": ["btcpay", "nowpayments"],
  "min_amount": 5.00,
  "max_amount": 10000.00,
  "require_email": true,
  "title": "Complete Your Payment",
  "description": "Secure payment processing",
  "button_text": "Pay Now"
}
```

### Custom CSS

Add custom styles to your widget configuration:

```json
{
  "custom_css": ".widget-container { border: 2px solid #667eea; }"
}
```

## 🔒 Security

### Domain Validation

Restrict widget usage to specific domains:

```json
{
  "allowed_domains": [
    "yourwebsite.com",
    "subdomain.yourwebsite.com"
  ]
}
```

### iframe Security

The widget uses iframe sandboxing with restricted permissions:
- `allow-scripts`: Required for widget functionality
- `allow-forms`: Required for payment forms
- `allow-same-origin`: Required for API calls
- `allow-top-navigation`: Required for payment redirects

## 🔔 Webhooks

Configure webhooks to receive real-time payment notifications:

```json
{
  "webhook_url": "https://yourwebsite.com/webhook/eloh-payments"
}
```

### Webhook Payload

```json
{
  "event": "payment_created",
  "order_id": "WIDGET_DEMO_1234567890_5678",
  "amount": 100.00,
  "currency": "BTC",
  "gateway": "btcpay",
  "email": "<EMAIL>",
  "description": "Payment description",
  "payment_data": {
    "invoice_id": "...",
    "checkout_link": "..."
  }
}
```

## 🌍 Supported Payment Gateways

### BTCPay Server
- **Currencies**: Bitcoin (BTC)
- **Features**: Lightning Network, Self-hosted, No fees
- **Best for**: Bitcoin-only payments

### NowPayments
- **Currencies**: 300+ cryptocurrencies
- **Features**: Multi-currency, Global support
- **Best for**: Maximum cryptocurrency variety

### Square
- **Currencies**: USD (credit/debit cards)
- **Features**: Traditional payments, PCI compliance
- **Best for**: Credit card payments

## 📱 Mobile Support

The widget is fully responsive and optimized for mobile devices:
- Touch-friendly interface
- Responsive design
- Mobile payment optimization
- Auto-resize functionality

## 🔧 Development

### File Structure

```
widget/
├── checkout-widget.php     # Main widget page
├── widget-embed.js         # JavaScript embed script
├── widget-config.php       # Configuration management
├── widget-api.php          # Payment processing API
├── widget-demo.html        # Demo and documentation
├── configs/                # Widget configurations
│   └── demo.json          # Demo widget config
├── payments/               # Payment data storage
└── README.md              # This documentation
```

### Testing

1. Open `widget-demo.html` in your browser
2. Configure payment parameters
3. Test different themes and options
4. Verify payment flow (demo mode)

### Production Deployment

1. Upload widget files to your server
2. Configure payment gateways in main ELOH Processing system
3. Create widget configurations for your clients
4. Test with real payment gateways
5. Provide integration code to clients

## 🆘 Support

For support and questions:
- **Email**: <EMAIL>
- **Documentation**: https://elohprocessing.infy.uk/widget/widget-demo.html
- **Demo**: https://elohprocessing.infy.uk/widget/widget-demo.html

## 📄 License

This widget is part of the ELOH Processing payment system. Contact us for licensing information.

---

**Powered by ELOH Processing** - Sustainable cryptocurrency payment solutions
