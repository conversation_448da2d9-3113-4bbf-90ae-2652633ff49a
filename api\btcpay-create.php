<?php
/**
 * BTCPay Server Payment Creation API
 * Creates live BTCPay Server invoices for the widget
 */

// CORS headers for cross-origin requests
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Accept');
header('Content-Type: application/json');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'error' => 'Method not allowed']);
    exit;
}

try {
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        throw new Exception('Invalid JSON input');
    }
    
    // Validate required fields
    $required_fields = ['amount', 'email', 'order_id'];
    foreach ($required_fields as $field) {
        if (empty($input[$field])) {
            throw new Exception("Missing required field: {$field}");
        }
    }
    
    $amount = floatval($input['amount']);
    $email = filter_var($input['email'], FILTER_VALIDATE_EMAIL);
    $order_id = sanitize_text_field($input['order_id']);
    $description = sanitize_text_field($input['description'] ?? 'Widget Payment');
    
    // Validate inputs
    if ($amount < 5 || $amount > 10000) {
        throw new Exception('Amount must be between $5.00 and $10,000.00');
    }
    
    if (!$email) {
        throw new Exception('Invalid email address');
    }
    
    // Load BTCPay configuration
    $config_file = __DIR__ . '/../config/btcpay-config.json';
    if (!file_exists($config_file)) {
        throw new Exception('BTCPay Server not configured');
    }
    
    $btcpay_config = json_decode(file_get_contents($config_file), true);
    if (!$btcpay_config || empty($btcpay_config['api_key']) || empty($btcpay_config['server_url'])) {
        throw new Exception('BTCPay Server configuration incomplete');
    }
    
    // Create BTCPay invoice
    $invoice_data = [
        'amount' => $amount,
        'currency' => 'USD',
        'orderId' => $order_id,
        'notificationEmail' => $email,
        'notificationURL' => 'https://elohprocessing.infy.uk/webhook/btcpay.php',
        'redirectURL' => 'https://elohprocessing.infy.uk/payment-success.php?order=' . urlencode($order_id),
        'metadata' => [
            'orderId' => $order_id,
            'itemDesc' => $description,
            'source' => 'widget'
        ]
    ];
    
    // Make API call to BTCPay Server
    $context = stream_context_create([
        'http' => [
            'method' => 'POST',
            'header' => [
                'Content-Type: application/json',
                'Authorization: token ' . $btcpay_config['api_key']
            ],
            'content' => json_encode($invoice_data),
            'timeout' => 30
        ]
    ]);
    
    $response = file_get_contents($btcpay_config['server_url'] . '/api/v1/invoices', false, $context);
    
    if ($response === false) {
        throw new Exception('Failed to connect to BTCPay Server');
    }
    
    $invoice = json_decode($response, true);
    
    if (!$invoice || !isset($invoice['id'])) {
        $error_message = 'Failed to create BTCPay invoice';
        if (isset($invoice['message'])) {
            $error_message .= ': ' . $invoice['message'];
        }
        throw new Exception($error_message);
    }
    
    // Log successful payment creation
    error_log("BTCPay invoice created: {$invoice['id']} for order: {$order_id}");
    
    // Store payment data
    $payment_data = [
        'order_id' => $order_id,
        'invoice_id' => $invoice['id'],
        'amount' => $amount,
        'email' => $email,
        'description' => $description,
        'gateway' => 'btcpay',
        'status' => 'pending',
        'created_at' => time(),
        'checkout_link' => $invoice['checkoutLink']
    ];
    
    $payments_dir = __DIR__ . '/../widget/payments';
    if (!is_dir($payments_dir)) {
        mkdir($payments_dir, 0755, true);
    }
    
    file_put_contents($payments_dir . "/{$order_id}.json", json_encode($payment_data, JSON_PRETTY_PRINT));
    
    // Return success response
    echo json_encode([
        'success' => true,
        'invoice_id' => $invoice['id'],
        'checkout_link' => $invoice['checkoutLink'],
        'payment_url' => $invoice['checkoutLink'],
        'order_id' => $order_id,
        'amount' => $amount,
        'currency' => 'USD',
        'gateway' => 'btcpay'
    ]);
    
} catch (Exception $e) {
    error_log("BTCPay API Error: " . $e->getMessage());
    
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}

function sanitize_text_field($value) {
    return htmlspecialchars(strip_tags(trim($value ?? '')));
}
?>
