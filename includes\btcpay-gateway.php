<?php
/**
 * BTCPay Server Payment Gateway for ELOH Processing
 * Using official BTCPay Server Greenfield API PHP client
 */

// For now, we'll create a manual implementation since composer isn't available
// In production, use: composer require btcpayserver/btcpayserver-greenfield-php

class BTCPay_Gateway {

    private $config;
    private $host;
    private $apiKey;
    private $storeId;

    public function __construct() {
        $this->config = require __DIR__ . '/btcpay-config.php';
        $this->host = rtrim(trim($this->config['host']), '/'); // Remove trailing spaces and slashes
        $this->apiKey = trim($this->config['api_key']);
        $this->storeId = trim($this->config['store_id']);
    }

    /**
     * Create an invoice using BTCPay Server API
     */
    public function createInvoice($amount, $currency, $orderId, $buyerEmail = null, $description = null) {
        // Validate configuration
        if (empty($this->host) || empty($this->storeId) || empty($this->apiKey)) {
            return [
                'success' => false,
                'error' => 'BTCPay configuration incomplete',
                'details' => [
                    'host_set' => !empty($this->host),
                    'store_id_set' => !empty($this->storeId),
                    'api_key_set' => !empty($this->apiKey)
                ]
            ];
        }

        // Properly encode the Store ID for URL
        $encodedStoreId = urlencode($this->storeId);
        $url = $this->host . '/api/v1/stores/' . $encodedStoreId . '/invoices';

        // Debug URL construction
        error_log("BTCPay URL Debug - Host: '" . $this->host . "'");
        error_log("BTCPay URL Debug - Store ID: '" . $this->storeId . "'");
        error_log("BTCPay URL Debug - Encoded Store ID: '" . $encodedStoreId . "'");
        error_log("BTCPay URL Debug - Final URL: '" . $url . "'");

        $invoiceData = [
            'amount' => (string)$amount,
            'currency' => $currency,
            'metadata' => [
                'orderId' => $orderId,
                'buyerEmail' => $buyerEmail,
                'itemDesc' => $description ?: 'ELOH Processing Payment'
            ],
            'checkout' => [
                'speedPolicy' => 'MediumSpeed',
                'paymentMethods' => ['BTC', 'BTC-LightningNetwork'],
                'expirationMinutes' => 60,
                'monitoringMinutes' => 1440,
                'paymentTolerance' => 0,
                'redirectURL' => $this->getReturnUrl($orderId),
                'redirectAutomatically' => false,
                'requiresRefundEmail' => false
            ]
        ];

        $response = $this->makeApiCall('POST', $url, $invoiceData);

        // Check if response contains an error
        if (isset($response['error'])) {
            return [
                'success' => false,
                'error' => 'BTCPay API Error: ' . $response['error'],
                'details' => $response
            ];
        }

        if ($response && isset($response['id'])) {
            return [
                'success' => true,
                'invoice_id' => $response['id'],
                'checkout_link' => $response['checkoutLink'],
                'amount' => $response['amount'],
                'currency' => $response['currency'],
                'status' => $response['status'],
                'created_time' => $response['createdTime'],
                'expiration_time' => $response['expirationTime']
            ];
        }

        return [
            'success' => false,
            'error' => 'Failed to create invoice - invalid response',
            'details' => $response
        ];
    }

    /**
     * Get invoice details
     */
    public function getInvoice($invoiceId) {
        $encodedStoreId = urlencode($this->storeId);
        $encodedInvoiceId = urlencode($invoiceId);
        $url = $this->host . '/api/v1/stores/' . $encodedStoreId . '/invoices/' . $encodedInvoiceId;

        $response = $this->makeApiCall('GET', $url);

        if ($response && isset($response['id'])) {
            return [
                'success' => true,
                'invoice' => $response
            ];
        }

        return [
            'success' => false,
            'error' => 'Invoice not found'
        ];
    }

    /**
     * Create or update webhook
     */
    public function setupWebhook() {
        $encodedStoreId = urlencode($this->storeId);
        $url = $this->host . '/api/v1/stores/' . $encodedStoreId . '/webhooks';

        $webhookData = [
            'url' => $this->config['webhook_url'],
            'authorizedEvents' => [
                'InvoiceCreated',
                'InvoiceReceivedPayment',
                'InvoicePaymentSettled',
                'InvoiceProcessing',
                'InvoiceExpired',
                'InvoiceSettled',
                'InvoiceInvalid'
            ],
            'secret' => $this->config['webhook_secret']
        ];

        $response = $this->makeApiCall('POST', $url, $webhookData);

        return $response ? ['success' => true, 'webhook' => $response] : ['success' => false];
    }

    /**
     * Validate webhook signature
     */
    public function validateWebhook($payload, $signature) {
        $expectedSignature = hash_hmac('sha256', $payload, $this->config['webhook_secret']);
        return hash_equals('sha256=' . $expectedSignature, $signature);
    }

    /**
     * Make API call to BTCPay Server
     */
    private function makeApiCall($method, $url, $data = null) {
        // Validate URL
        if (!filter_var($url, FILTER_VALIDATE_URL)) {
            error_log("BTCPay API Invalid URL: $url");
            return [
                'error' => 'Invalid URL: ' . $url,
                'http_code' => 0
            ];
        }

        $headers = [
            'Authorization: token ' . $this->apiKey,
            'Content-Type: application/json',
            'Accept: application/json'
        ];

        // Log the request for debugging
        error_log("BTCPay API Request: $method $url");
        if ($data) {
            error_log("BTCPay API Data: " . json_encode($data));
        }

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_USERAGENT, 'ELOH-Processing-BTCPay-Client/1.0');

        if ($method === 'POST') {
            curl_setopt($ch, CURLOPT_POST, true);
            if ($data) {
                curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
            }
        }

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        $info = curl_getinfo($ch);
        curl_close($ch);

        // Log the response for debugging
        error_log("BTCPay API Response: HTTP $httpCode - " . substr($response, 0, 500));

        if ($error) {
            error_log("BTCPay API cURL Error: " . $error);
            return [
                'error' => 'Connection error: ' . $error,
                'http_code' => 0
            ];
        }

        if ($httpCode >= 400) {
            error_log("BTCPay API HTTP Error: " . $httpCode . " - " . $response);
            return [
                'error' => 'HTTP Error ' . $httpCode,
                'http_code' => $httpCode,
                'response' => $response
            ];
        }

        $decoded = json_decode($response, true);
        if ($decoded === null && json_last_error() !== JSON_ERROR_NONE) {
            error_log("BTCPay API JSON Error: " . json_last_error_msg());
            return [
                'error' => 'Invalid JSON response: ' . json_last_error_msg(),
                'response' => $response
            ];
        }

        return $decoded;
    }

    /**
     * Get return URL after payment
     */
    private function getReturnUrl($orderId) {
        return 'https://elohprocessing.infy.uk/payment-success.php?order=' . $orderId;
    }

    /**
     * Get supported currencies
     */
    public function getSupportedCurrencies() {
        return ['USD', 'EUR', 'BTC', 'SAT'];
    }

    /**
     * Get debug information
     */
    public function getDebugInfo() {
        return [
            'host' => $this->host,
            'store_id' => $this->storeId,
            'api_key_set' => !empty($this->apiKey),
            'webhook_url' => $this->config['webhook_url']
        ];
    }
}
