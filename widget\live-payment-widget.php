<?php
/**
 * ELOH Processing Live Payment Widget
 * Processes REAL payments through BTCPay Server and NowPayments
 */

// Include existing payment infrastructure
require_once __DIR__ . '/../includes/payment-gateway-manager.php';
require_once __DIR__ . '/../includes/btcpay-gateway.php';
require_once __DIR__ . '/../includes/nowpayments-gateway.php';

// Get widget parameters
$amount = floatval($_GET['amount'] ?? 0);
$email = filter_var($_GET['email'] ?? '', FILTER_SANITIZE_EMAIL);
$description = htmlspecialchars($_GET['description'] ?? 'Widget Payment');
$gateway = htmlspecialchars($_GET['gateway'] ?? 'btcpay');
$theme = htmlspecialchars($_GET['theme'] ?? 'light');

// Handle payment processing
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // Get form data
        $amount = floatval($_POST['amount']);
        $email = filter_var($_POST['email'], FILTER_SANITIZE_EMAIL);
        $description = htmlspecialchars($_POST['description'] ?? 'Widget Payment');
        $gateway = htmlspecialchars($_POST['gateway']);
        
        // Validate inputs
        if ($amount < 5 || $amount > 10000) {
            throw new Exception('Amount must be between $5.00 and $10,000.00');
        }
        
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            throw new Exception('Valid email address is required');
        }
        
        // Generate order ID
        $orderId = 'WIDGET_' . time() . '_' . rand(1000, 9999);
        
        // Create payment gateway manager
        $gatewayManager = new Payment_Gateway_Manager();
        
        // Process payment based on gateway
        if ($gateway === 'btcpay') {
            // BTCPay Server payment
            $btcpayGateway = new BTCPay_Gateway();
            $result = $btcpayGateway->createInvoice($amount, 'USD', $orderId, $email, $description);
            
            if ($result['success']) {
                // Redirect to BTCPay checkout
                header('Location: ' . $result['checkout_link']);
                exit;
            } else {
                throw new Exception($result['error'] ?? 'BTCPay payment creation failed');
            }
            
        } elseif ($gateway === 'nowpayments') {
            // NowPayments payment
            $nowpaymentsGateway = new NowPayments_Gateway(false); // Live mode
            $result = $nowpaymentsGateway->createPayment($amount, 'btc', $orderId, $description, $email);
            
            if ($result['success']) {
                // Redirect to NowPayments checkout
                header('Location: ' . $result['payment_url']);
                exit;
            } else {
                throw new Exception($result['error'] ?? 'NowPayments payment creation failed');
            }
        } else {
            throw new Exception('Invalid payment gateway selected');
        }
        
    } catch (Exception $e) {
        $error_message = $e->getMessage();
        error_log("Widget payment error: " . $error_message);
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ELOH Processing - Live Payment</title>
    <style>
        :root {
            --primary: #667eea;
            --accent: #764ba2;
            --radius: 12px;
        }
        
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .widget-container {
            background: white;
            border-radius: var(--radius);
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
            padding: 32px;
            width: 100%;
            max-width: 420px;
            border: 1px solid #e2e8f0;
        }
        
        .widget-header {
            text-align: center;
            margin-bottom: 32px;
        }
        
        .widget-title {
            font-size: 1.75rem;
            font-weight: 700;
            color: #1a202c;
            margin-bottom: 8px;
        }
        
        .live-badge {
            background: linear-gradient(135deg, #ef4444, #dc2626);
            color: white;
            padding: 4px 8px;
            border-radius: 6px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            margin-left: 8px;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.8; }
        }
        
        .widget-description {
            color: #718096;
            font-size: 1rem;
        }
        
        .form-group {
            margin-bottom: 24px;
        }
        
        .form-label {
            display: block;
            font-weight: 600;
            color: #374151;
            margin-bottom: 8px;
            font-size: 0.95rem;
        }
        
        .form-input, .form-select {
            width: 100%;
            padding: 14px 16px;
            border: 2px solid #e5e7eb;
            border-radius: var(--radius);
            font-size: 1rem;
            transition: all 0.2s ease;
            background: white;
        }
        
        .form-input:focus, .form-select:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .gateway-selector {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
            margin-bottom: 24px;
        }
        
        .gateway-option {
            position: relative;
        }
        
        .gateway-option input[type="radio"] {
            position: absolute;
            opacity: 0;
        }
        
        .gateway-option label {
            display: block;
            padding: 20px 16px;
            border: 2px solid #e5e7eb;
            border-radius: var(--radius);
            text-align: center;
            cursor: pointer;
            transition: all 0.2s ease;
            background: white;
        }
        
        .gateway-option input[type="radio"]:checked + label {
            border-color: var(--primary);
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
        }
        
        .gateway-icon {
            font-size: 2rem;
            margin-bottom: 8px;
        }
        
        .gateway-name {
            font-size: 0.9rem;
            font-weight: 600;
            color: #374151;
        }
        
        .amount-input-group {
            position: relative;
        }
        
        .currency-symbol {
            position: absolute;
            left: 16px;
            top: 50%;
            transform: translateY(-50%);
            color: #6b7280;
            font-weight: 600;
            font-size: 1.1rem;
        }
        
        .amount-input-group .form-input {
            padding-left: 44px;
            font-size: 1.1rem;
            font-weight: 600;
        }
        
        .pay-button {
            width: 100%;
            background: linear-gradient(135deg, var(--primary) 0%, var(--accent) 100%);
            color: white;
            border: none;
            padding: 18px 24px;
            border-radius: var(--radius);
            font-size: 1.1rem;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.2s ease;
            margin-top: 16px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .pay-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 30px rgba(102, 126, 234, 0.4);
        }
        
        .powered-by {
            text-align: center;
            margin-top: 24px;
            font-size: 0.85rem;
            color: #9ca3af;
        }
        
        .powered-by a {
            color: var(--primary);
            text-decoration: none;
            font-weight: 600;
        }
        
        .error-message {
            background: #fef2f2;
            color: #dc2626;
            padding: 16px;
            border-radius: var(--radius);
            margin-bottom: 24px;
            font-size: 0.95rem;
            border: 1px solid #fecaca;
        }
        
        .security-note {
            background: #f0fdf4;
            color: #166534;
            padding: 12px 16px;
            border-radius: var(--radius);
            margin-bottom: 24px;
            font-size: 0.85rem;
            border: 1px solid #bbf7d0;
            text-align: center;
        }
        
        @media (max-width: 480px) {
            body {
                padding: 12px;
            }
            
            .widget-container {
                padding: 24px;
            }
            
            .gateway-selector {
                grid-template-columns: 1fr;
            }
        }
    </style>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="widget-container">
        <div class="widget-header">
            <h1 class="widget-title">
                Complete Your Payment
                <span class="live-badge">LIVE</span>
            </h1>
            <p class="widget-description">Secure cryptocurrency payment processing</p>
        </div>
        
        <div class="security-note">
            🔒 This processes REAL payments through live cryptocurrency gateways
        </div>
        
        <?php if (isset($error_message)): ?>
        <div class="error-message">
            ❌ <?php echo htmlspecialchars($error_message); ?>
        </div>
        <?php endif; ?>
        
        <form method="POST">
            <div class="form-group">
                <label class="form-label">Payment Method</label>
                <div class="gateway-selector">
                    <div class="gateway-option">
                        <input type="radio" name="gateway" value="btcpay" id="gateway_btcpay" 
                               <?php echo $gateway === 'btcpay' ? 'checked' : ''; ?>>
                        <label for="gateway_btcpay">
                            <div class="gateway-icon">⚡</div>
                            <div class="gateway-name">BTCPay Server</div>
                        </label>
                    </div>
                    <div class="gateway-option">
                        <input type="radio" name="gateway" value="nowpayments" id="gateway_nowpayments"
                               <?php echo $gateway === 'nowpayments' ? 'checked' : ''; ?>>
                        <label for="gateway_nowpayments">
                            <div class="gateway-icon">🌐</div>
                            <div class="gateway-name">NowPayments</div>
                        </label>
                    </div>
                </div>
            </div>
            
            <div class="form-group">
                <label for="amount" class="form-label">Amount</label>
                <div class="amount-input-group">
                    <span class="currency-symbol">$</span>
                    <input type="number" id="amount" name="amount" 
                           min="5" max="10000" step="0.01" required class="form-input"
                           value="<?php echo $amount > 0 ? number_format($amount, 2, '.', '') : ''; ?>"
                           placeholder="0.00">
                </div>
            </div>
            
            <div class="form-group">
                <label for="email" class="form-label">Email Address</label>
                <input type="email" id="email" name="email" required class="form-input"
                       value="<?php echo htmlspecialchars($email); ?>"
                       placeholder="<EMAIL>">
            </div>
            
            <div class="form-group">
                <label for="description" class="form-label">Description</label>
                <input type="text" id="description" name="description" class="form-input"
                       value="<?php echo htmlspecialchars($description); ?>"
                       placeholder="Payment description">
            </div>
            
            <button type="submit" class="pay-button">
                🚀 Process Live Payment
            </button>
        </form>
        
        <div class="powered-by">
            Powered by <a href="https://elohprocessing.infy.uk" target="_blank">ELOH Processing</a>
        </div>
    </div>
    
    <script>
        // Auto-resize for iframe embedding
        function sendResize() {
            if (window.parent && window.parent !== window) {
                const height = document.body.scrollHeight;
                window.parent.postMessage({
                    type: 'eloh_widget_resize',
                    height: height
                }, '*');
            }
        }
        
        // Send initial size
        setTimeout(sendResize, 100);
        
        // Send size on changes
        window.addEventListener('resize', sendResize);
        document.addEventListener('change', () => setTimeout(sendResize, 100));
        
        // Log widget initialization
        console.log('🔴 ELOH Processing LIVE Payment Widget loaded');
        console.log('⚡ BTCPay Server integration: ACTIVE');
        console.log('🌐 NowPayments integration: ACTIVE');
    </script>
</body>
</html>
