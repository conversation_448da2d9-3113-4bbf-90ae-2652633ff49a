const express = require('express');
const router = express.Router();
const { createInvoice, getInvoiceStatus } = require('../services/btcpay');
const { authenticate } = require('../middleware/auth');

// Create invoice endpoint
router.post('/', authenticate, async (req, res) => {
  try {
    const { amount, currency, orderId, redirectUrl } = req.body;
    // Validate currency is supported (BTC, ETH, TRX)
    if (!['BTC', 'ETH', 'TRX'].includes(currency)) {
      return res.status(400).json({ error: 'Unsupported currency' });
    }
    
    const invoice = await createInvoice(amount, currency, orderId, redirectUrl, req.merchant.id);
    res.json(invoice);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Get invoice status
router.get('/:invoiceId', authenticate, async (req, res) => {
  // Implementation...
});

module.exports = router;