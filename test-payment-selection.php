<?php
include "header.php";
?>

<main>
  <section class="hero">
    <h1>Test Payment Gateway Selection</h1>
  </section>

  <section class="section">
    <div style="max-width: 800px; margin: 0 auto;">
      <div style="background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin-bottom: 20px;">
        <h3>🧪 Testing Dynamic Payment Gateway Selection</h3>
        <p>This page tests if users can dynamically select between payment gateways and see currency options update.</p>
      </div>

      <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin-bottom: 20px;">
        <h3>Test Instructions:</h3>
        <ol>
          <li><strong>Click on BTCPay Server</strong> - Should show Bitcoin option and auto-select it</li>
          <li><strong>Click on NowPayments</strong> - Should show 20+ cryptocurrency options</li>
          <li><strong>Watch visual feedback</strong> - Selected gateway should highlight with checkmark</li>
          <li><strong>Check currency dropdown</strong> - Should update dynamically with different options</li>
        </ol>
      </div>

      <!-- Direct Testing Section -->
      <div style="background: #e7f3ff; padding: 20px; border-radius: 10px; margin-bottom: 20px;">
        <h3>🔧 Direct Testing (No iframe issues)</h3>
        <div style="display: flex; gap: 10px; margin-bottom: 15px;">
          <button onclick="openPaymentForm('btcpay')" class="cta-button" style="padding: 10px 20px;">Test BTCPay Selection</button>
          <button onclick="openPaymentForm('nowpayments')" class="cta-button" style="padding: 10px 20px; background: #28a745;">Test NowPayments Selection</button>
          <button onclick="openPaymentForm('full')" class="cta-button" style="padding: 10px 20px; background: #6c757d;">Open Full Form</button>
        </div>
        <div id="test-results" style="background: white; padding: 15px; border-radius: 5px; min-height: 50px; border: 1px solid #ddd;">
          <p><em>Click a test button above to see results...</em></p>
        </div>
      </div>

      <!-- Iframe Testing Section -->
      <div style="background: #fff3cd; padding: 15px; border-radius: 5px; margin-bottom: 20px;">
        <h3>📱 Iframe Testing</h3>
        <p><strong>Note:</strong> If the iframe doesn't load properly, use the direct testing buttons above or the "Open Full Form" link below.</p>
      </div>

      <!-- Test the actual payment form -->
      <iframe src="multi-gateway-payment-form.php?type=service&service=consulting&amount=150"
              style="width: 100%; height: 800px; border: 1px solid #ddd; border-radius: 10px;"
              onload="iframeLoaded()" onerror="iframeError()">
      </iframe>

      <div style="background: #fff3cd; color: #856404; padding: 15px; border-radius: 5px; margin-top: 20px;">
        <h3>Expected Behavior:</h3>
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
          <div>
            <h4>⚡ BTCPay Server Selection:</h4>
            <ul>
              <li>Blue border and background</li>
              <li>Checkmark in top-right corner</li>
              <li>Currency auto-selects Bitcoin</li>
              <li>Shows "Lightning & On-chain" text</li>
            </ul>
          </div>
          <div>
            <h4>🌐 NowPayments Selection:</h4>
            <ul>
              <li>Blue border and background</li>
              <li>Checkmark in top-right corner</li>
              <li>Shows 20+ cryptocurrency options</li>
              <li>Dropdown shows count of options</li>
            </ul>
          </div>
        </div>
      </div>

      <div style="text-align: center; margin: 30px 0;">
        <a href="multi-gateway-payment-form.php" class="cta-button">Test Full Payment Form</a>
        <a href="index.php" class="cta-button" style="background: #6c757d; margin-left: 10px;">Back to Homepage</a>
      </div>
    </div>
  </section>
</main>

<script>
// Add some debugging to the parent page
console.log('Payment selection test page loaded');

// Listen for messages from iframe (if needed)
window.addEventListener('message', function(event) {
  console.log('Message from payment form:', event.data);
});

// Direct testing functions
function openPaymentForm(testType) {
  const resultsDiv = document.getElementById('test-results');

  switch(testType) {
    case 'btcpay':
      resultsDiv.innerHTML = `
        <h4>🧪 Testing BTCPay Server Selection</h4>
        <p><strong>Opening:</strong> <a href="multi-gateway-payment-form.php?type=service&service=consulting&amount=150" target="_blank">Payment Form with Consulting Service</a></p>
        <p><strong>Expected:</strong> BTCPay Server should be pre-selected with blue highlight and checkmark</p>
        <p><strong>Currency:</strong> Should auto-select Bitcoin (Lightning & On-chain)</p>
        <div style="margin-top: 10px;">
          <button onclick="window.open('multi-gateway-payment-form.php?type=service&service=consulting&amount=150', '_blank')"
                  style="background: #0077cc; color: white; padding: 8px 16px; border: none; border-radius: 4px; cursor: pointer;">
            🚀 Open Payment Form
          </button>
        </div>
      `;
      break;

    case 'nowpayments':
      resultsDiv.innerHTML = `
        <h4>🧪 Testing NowPayments Selection</h4>
        <p><strong>Opening:</strong> <a href="multi-gateway-payment-form.php?type=donation&amount=100" target="_blank">Payment Form with Donation</a></p>
        <p><strong>Test Steps:</strong></p>
        <ol style="margin: 10px 0; padding-left: 20px;">
          <li>Click on NowPayments gateway option</li>
          <li>Verify blue highlight and checkmark appear</li>
          <li>Check currency dropdown shows 20+ options</li>
          <li>Try selecting different cryptocurrencies</li>
        </ol>
        <div style="margin-top: 10px;">
          <button onclick="window.open('multi-gateway-payment-form.php?type=donation&amount=100', '_blank')"
                  style="background: #28a745; color: white; padding: 8px 16px; border: none; border-radius: 4px; cursor: pointer;">
            🚀 Open Payment Form
          </button>
        </div>
      `;
      break;

    case 'full':
      resultsDiv.innerHTML = `
        <h4>🧪 Full Payment Form Testing</h4>
        <p><strong>Test different scenarios:</strong></p>
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin: 15px 0;">
          <div>
            <h5>Service Payments:</h5>
            <ul style="margin: 5px 0; padding-left: 15px; font-size: 0.9em;">
              <li><a href="multi-gateway-payment-form.php?type=service&service=consulting&amount=150" target="_blank">Consulting ($150)</a></li>
              <li><a href="multi-gateway-payment-form.php?type=service&service=mining-pool&amount=200" target="_blank">Mining Pool ($200)</a></li>
              <li><a href="multi-gateway-payment-form.php?type=service&service=mining-services&amount=500" target="_blank">Mining Services ($500)</a></li>
              <li><a href="multi-gateway-payment-form.php?type=service&service=analysis&amount=99" target="_blank">Analysis ($99)</a></li>
            </ul>
          </div>
          <div>
            <h5>Donations:</h5>
            <ul style="margin: 5px 0; padding-left: 15px; font-size: 0.9em;">
              <li><a href="multi-gateway-payment-form.php?type=donation&amount=100" target="_blank">$100 Donation</a></li>
              <li><a href="multi-gateway-payment-form.php?type=donation&amount=1000" target="_blank">$1,000 Donation</a></li>
              <li><a href="multi-gateway-payment-form.php?type=donation&amount=5000" target="_blank">$5,000 Donation</a></li>
              <li><a href="multi-gateway-payment-form.php?type=donation" target="_blank">Custom Donation</a></li>
            </ul>
          </div>
        </div>
      `;
      break;
  }
}

// Iframe event handlers
function iframeLoaded() {
  console.log('Iframe loaded successfully');
  const resultsDiv = document.getElementById('test-results');
  if (resultsDiv.innerHTML.includes('Click a test button')) {
    resultsDiv.innerHTML = `
      <p style="color: green;">✅ <strong>Iframe loaded successfully!</strong> You can test the payment gateway selection in the iframe below.</p>
      <p><em>Try clicking between BTCPay Server and NowPayments options to see the dynamic selection in action.</em></p>
    `;
  }
}

function iframeError() {
  console.log('Iframe failed to load');
  const resultsDiv = document.getElementById('test-results');
  resultsDiv.innerHTML = `
    <p style="color: red;">❌ <strong>Iframe failed to load.</strong> Use the direct testing buttons above instead.</p>
    <p>This might be due to browser security restrictions or server issues.</p>
  `;
}

// Check if iframe loads within 5 seconds
setTimeout(function() {
  const iframe = document.querySelector('iframe');
  if (iframe && !iframe.contentDocument) {
    console.log('Iframe loading timeout');
    const resultsDiv = document.getElementById('test-results');
    if (resultsDiv.innerHTML.includes('Click a test button')) {
      resultsDiv.innerHTML = `
        <p style="color: orange;">⚠️ <strong>Iframe taking too long to load.</strong> Use the direct testing buttons above for immediate testing.</p>
      `;
    }
  }
}, 5000);
</script>

<?php include "footer.php"; ?>
