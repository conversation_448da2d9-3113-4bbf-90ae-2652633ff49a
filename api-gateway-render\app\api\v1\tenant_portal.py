"""
Tenant portal API endpoints
"""

from fastapi import APIRouter, Request
from fastapi.responses import HTMLResponse, JSONResponse
from typing import Dict, Any
import logging

from ...core.adapter_registry import get_adapter_registry

router = APIRouter()
logger = logging.getLogger(__name__)


@router.get("/portal", response_class=HTMLResponse)
async def tenant_portal():
    """Tenant portal web interface"""
    html_content = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>ELOH Processing - Tenant Portal</title>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <style>
            body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
            .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            .header { text-align: center; margin-bottom: 30px; }
            .header h1 { color: #2c3e50; margin: 0; }
            .header p { color: #7f8c8d; margin: 10px 0 0 0; }
            .section { margin: 30px 0; }
            .section h2 { color: #34495e; border-bottom: 2px solid #3498db; padding-bottom: 10px; }
            .gateway-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin: 20px 0; }
            .gateway-card { border: 1px solid #ddd; border-radius: 8px; padding: 20px; background: #f9f9f9; }
            .gateway-card h3 { margin: 0 0 10px 0; color: #2c3e50; }
            .status-available { color: #27ae60; font-weight: bold; }
            .status-limited { color: #f39c12; font-weight: bold; }
            .status-unavailable { color: #e74c3c; font-weight: bold; }
            .api-section { background: #ecf0f1; padding: 20px; border-radius: 8px; margin: 20px 0; }
            .code { background: #2c3e50; color: #ecf0f1; padding: 15px; border-radius: 5px; font-family: monospace; overflow-x: auto; }
            .btn { background: #3498db; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; text-decoration: none; display: inline-block; }
            .btn:hover { background: #2980b9; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🌴 ELOH Processing Payment Gateway</h1>
                <p>Multi-tenant payment gateway optimized for the Caribbean</p>
            </div>
            
            <div class="section">
                <h2>🌍 Gateway Availability Status</h2>
                <div class="gateway-grid">
                    <div class="gateway-card">
                        <h3>🟢 BTCPay Server</h3>
                        <p class="status-available">✅ Available Globally</p>
                        <p>Self-hosted Bitcoin payment processor. Perfect for Dominica with no regional restrictions.</p>
                        <p><strong>Currencies:</strong> BTC, Lightning Network, USD</p>
                    </div>
                    
                    <div class="gateway-card">
                        <h3>🟢 NowPayments</h3>
                        <p class="status-available">✅ Available Globally</p>
                        <p>300+ cryptocurrency payment gateway. Excellent coverage for Caribbean region.</p>
                        <p><strong>Currencies:</strong> BTC, ETH, LTC, XMR, 300+ more</p>
                    </div>
                    
                    <div class="gateway-card">
                        <h3>🟡 Stripe</h3>
                        <p class="status-limited">⚠️ Limited in Dominica</p>
                        <p>Popular payment processor with limited Caribbean support.</p>
                        <p><strong>Status:</strong> Not officially supported in Dominica</p>
                    </div>
                    
                    <div class="gateway-card">
                        <h3>🔴 Square</h3>
                        <p class="status-unavailable">❌ Not Available</p>
                        <p>Payment processing platform not available in Dominica.</p>
                        <p><strong>Status:</strong> No Caribbean support</p>
                    </div>
                </div>
            </div>
            
            <div class="section">
                <h2>🚀 Quick Start</h2>
                <div class="api-section">
                    <h3>1. Create a Payment</h3>
                    <div class="code">
curl -X POST "https://your-app.onrender.com/v1/payments" \\
  -H "Authorization: Bearer your_api_key" \\
  -H "Content-Type: application/json" \\
  -d '{
    "amount": 100.00,
    "currency": "USD",
    "description": "Test payment",
    "email": "<EMAIL>"
  }'</div>
                </div>
                
                <div class="api-section">
                    <h3>2. Check Gateway Status</h3>
                    <div class="code">
curl "https://your-app.onrender.com/v1/portal/status"</div>
                </div>
            </div>
            
            <div class="section">
                <h2>📚 Resources</h2>
                <p>
                    <a href="/docs" class="btn">📖 API Documentation</a>
                    <a href="/health" class="btn">❤️ Health Check</a>
                    <a href="/v1/portal/status" class="btn">🌐 Gateway Status</a>
                </p>
            </div>
            
            <div class="section">
                <h2>💡 Recommendations for Dominica</h2>
                <p><strong>Primary Gateways:</strong></p>
                <ul>
                    <li><strong>BTCPay Server</strong> - Best for Bitcoin payments, no regional restrictions</li>
                    <li><strong>NowPayments</strong> - Excellent for cryptocurrency diversity</li>
                </ul>
                <p><strong>Why Crypto-First?</strong> Cryptocurrency gateways provide the best coverage for Caribbean businesses, avoiding traditional banking restrictions.</p>
            </div>
        </div>
    </body>
    </html>
    """
    return HTMLResponse(content=html_content)


@router.get("/portal/status")
async def gateway_status():
    """Get gateway availability status"""
    registry = get_adapter_registry()
    
    # Simulate regional availability for Dominica
    gateways_status = {
        "btcpay": {
            "status": "available",
            "message": "Available globally",
            "recommended": True
        },
        "nowpayments": {
            "status": "available", 
            "message": "Available globally",
            "recommended": True
        },
        "stripe": {
            "status": "limited",
            "message": "Not available in your region (Dominica)",
            "recommended": False
        },
        "square": {
            "status": "limited",
            "message": "Not available in your region (Dominica)",
            "recommended": False
        }
    }
    
    return {
        "region": "Dominica",
        "gateways": gateways_status,
        "recommendations": {
            "primary": ["btcpay", "nowpayments"],
            "secondary": [],
            "not_recommended": ["stripe", "square"]
        },
        "message": "Crypto-first approach recommended for Caribbean region"
    }
