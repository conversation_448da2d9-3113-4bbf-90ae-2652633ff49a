# Cryptocurrency Payment Minimum Amounts

## 🔍 **Issue Explanation:**

The error you encountered shows that NowPayments has minimum amounts for each cryptocurrency. When you tried to create a $1 USD payment in Bitcoin, it converted to approximately 0.00000931 BTC, which is below NowPayments' minimum threshold.

## 💰 **Typical Minimum Amounts:**

### **NowPayments Minimums (approximate):**
- **Bitcoin (BTC)**: ~$10-15 USD
- **Ethereum (ETH)**: ~$5-10 USD
- **Tether (USDT)**: ~$1-5 USD
- **USD Coin (USDC)**: ~$1-5 USD
- **Litecoin (LTC)**: ~$2-5 USD
- **Bitcoin Cash (BCH)**: ~$2-5 USD
- **Tron (TRX)**: ~$1-3 USD
- **Binance Coin (BNB)**: ~$5-10 USD

*Note: These amounts vary based on current exchange rates and network fees*

### **BTCPay Server:**
- **Bitcoin (BTC)**: No minimum (you control this)
- **Lightning Network**: Can handle very small amounts (satoshis)

## ✅ **Fixes Applied:**

### **1. Updated Test Page:**
- Changed test amount from $1 to $10+ 
- Added minimum amount checking
- Shows actual minimums before creating test payments

### **2. Updated Payment Form:**
- Increased minimum from $1 to $5
- Default amount set to $10
- Added warning about cryptocurrency minimums

### **3. Updated Validation:**
- Server-side validation for $5 minimum
- Better error messages for users

## 🎯 **Recommended Minimums for Your Business:**

### **For Services:**
- **Consulting**: $150/hour (already above minimums)
- **Mining Pool**: $200/year (already above minimums)
- **Mining Services**: $500/month (already above minimums)
- **Analysis Reports**: $99/report (already above minimums)

### **For Donations:**
- **Minimum**: $10 (safe for all cryptocurrencies)
- **Suggested**: $25, $50, $100, $500, $1000
- **Custom**: Allow users to enter amounts $10+

## 🔧 **Technical Implementation:**

### **Dynamic Minimum Checking:**
The system now checks minimum amounts in real-time:

```php
// Check minimum amount for selected cryptocurrency
$minAmount = $gateway->getMinimumAmount($currency);
if ($amount < $minAmount['min_amount']) {
    // Show error with actual minimum
}
```

### **User-Friendly Messages:**
- Clear error messages showing actual minimums
- Helpful suggestions for appropriate amounts
- Gateway-specific minimum information

## 💡 **Best Practices:**

### **For Your Customers:**
1. **Clear Minimums**: Display minimum amounts prominently
2. **Suggested Amounts**: Provide preset amounts above minimums
3. **Currency Info**: Explain why minimums exist (network fees)
4. **Alternative Options**: Offer BTCPay for smaller Bitcoin payments

### **For Your Business:**
1. **Service Pricing**: Keep services above typical minimums
2. **Donation Tiers**: Start at $10+ for donations
3. **Gateway Selection**: Use BTCPay for small Bitcoin payments
4. **User Education**: Explain cryptocurrency payment minimums

## 🚀 **Updated System Benefits:**

### **Smart Minimum Handling:**
- ✅ Real-time minimum amount checking
- ✅ Dynamic validation based on selected cryptocurrency
- ✅ Clear error messages with actual minimums
- ✅ Suggested amounts above minimums

### **Better User Experience:**
- ✅ Default $10 amount (safe for all cryptocurrencies)
- ✅ Clear minimum amount warnings
- ✅ Gateway-specific information
- ✅ Helpful error messages

### **Business Protection:**
- ✅ Prevents failed payments due to low amounts
- ✅ Reduces customer support issues
- ✅ Professional payment experience
- ✅ Higher average transaction values

## 📋 **Testing Recommendations:**

### **Test Amounts:**
- **$10**: Safe for all cryptocurrencies
- **$25**: Good for donation testing
- **$50**: Typical service payment
- **$100+**: Higher value testing

### **Test Cryptocurrencies:**
1. **Bitcoin (BTC)**: Test with both gateways
2. **Ethereum (ETH)**: Test NowPayments
3. **USDT/USDC**: Test stablecoins
4. **Popular Altcoins**: Test major cryptocurrencies

Your payment system now handles minimum amounts intelligently and provides a much better user experience! 🎉
