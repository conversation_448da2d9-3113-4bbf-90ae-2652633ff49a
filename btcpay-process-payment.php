<?php
// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once "includes/btcpay-gateway.php";

if ($_SERVER["REQUEST_METHOD"] !== "POST") {
    header("Location: index.php");
    exit;
}

// Get form data
$amount = floatval($_POST["amount"]);
$email = $_POST["email"];
$payment_type = $_POST["payment_type"];
$description = $_POST["description"] ?? "";

// Build description based on payment type
if ($payment_type === "donation") {
    $full_description = "Donation to ELOH Processing LLC";
    if ($description) {
        $full_description .= " - " . $description;
    }
    $orderId = "DONATION_" . time() . "_" . rand(1000, 9999);
} else {
    $service = $_POST["service"] ?? "Service";
    $full_description = "Payment for: " . $service;
    if ($description) {
        $full_description .= " - " . $description;
    }
    $orderId = "SERVICE_" . time() . "_" . rand(1000, 9999);
}

// Validate inputs
$errors = [];
if ($amount <= 0) $errors[] = "Invalid amount: $amount";
if (!filter_var($email, FILTER_VALIDATE_EMAIL)) $errors[] = "Invalid email: $email";

if (!empty($errors)) {
    $error_details = implode(", ", $errors);
    header("Location: btcpay-payment-form.php?error=invalid_data&details=" . urlencode($error_details));
    exit;
}

try {
    // Create BTCPay gateway instance
    $gateway = new BTCPay_Gateway();
    
    // Check configuration
    $debug = $gateway->getDebugInfo();
    if (!$debug['api_key_set']) {
        header("Location: btcpay-payment-form.php?error=configuration&details=" . urlencode("API key not configured"));
        exit;
    }
    
    // Create invoice
    $invoice = $gateway->createInvoice(
        $amount,
        'USD', // Currency
        $orderId,
        $email,
        $full_description
    );
    
    if ($invoice['success']) {
        // Store invoice data in session for tracking
        session_start();
        $_SESSION['btcpay_invoices'][$invoice['invoice_id']] = [
            'order_id' => $orderId,
            'amount' => $amount,
            'email' => $email,
            'description' => $full_description,
            'payment_type' => $payment_type,
            'created_at' => time()
        ];
        
        // Redirect to BTCPay Server checkout
        header("Location: " . $invoice['checkout_link']);
        exit;
        
    } else {
        $error_msg = isset($invoice['error']) ? $invoice['error'] : "Unknown error creating invoice";
        if (isset($invoice['details'])) {
            $error_msg .= " - " . json_encode($invoice['details']);
        }
        
        header("Location: btcpay-payment-form.php?error=btcpay_error&details=" . urlencode($error_msg));
        exit;
    }
    
} catch (Exception $e) {
    error_log("BTCPay payment processing error: " . $e->getMessage());
    header("Location: btcpay-payment-form.php?error=btcpay_error&details=" . urlencode($e->getMessage()));
    exit;
}
?>
