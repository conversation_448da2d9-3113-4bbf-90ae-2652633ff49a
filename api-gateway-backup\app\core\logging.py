"""
Logging configuration for ELOH Processing Payment Gateway API

This module sets up structured logging with support for both JSON and text formats,
appropriate for different environments and monitoring systems.
"""

import logging
import logging.config
import sys
from typing import Dict, Any
import json
from datetime import datetime

from .config import get_settings


class JSONFormatter(logging.Formatter):
    """
    Custom JSON formatter for structured logging.
    
    This formatter outputs log records as JSON objects, making them
    easier to parse and analyze in log aggregation systems.
    """
    
    def format(self, record: logging.LogRecord) -> str:
        """Format log record as JSON"""
        
        # Base log data
        log_data = {
            "timestamp": datetime.utcnow().isoformat() + "Z",
            "level": record.levelname,
            "logger": record.name,
            "message": record.getMessage(),
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno
        }
        
        # Add exception information if present
        if record.exc_info:
            log_data["exception"] = self.formatException(record.exc_info)
        
        # Add extra fields from the log record
        extra_fields = {}
        for key, value in record.__dict__.items():
            if key not in {
                'name', 'msg', 'args', 'levelname', 'levelno', 'pathname',
                'filename', 'module', 'lineno', 'funcName', 'created',
                'msecs', 'relativeCreated', 'thread', 'threadName',
                'processName', 'process', 'getMessage', 'exc_info',
                'exc_text', 'stack_info'
            }:
                extra_fields[key] = value
        
        if extra_fields:
            log_data["extra"] = extra_fields
        
        return json.dumps(log_data, default=str)


class ColoredFormatter(logging.Formatter):
    """
    Colored formatter for console output in development.
    
    This formatter adds colors to log levels for better readability
    during development and debugging.
    """
    
    # Color codes
    COLORS = {
        'DEBUG': '\033[36m',     # Cyan
        'INFO': '\033[32m',      # Green
        'WARNING': '\033[33m',   # Yellow
        'ERROR': '\033[31m',     # Red
        'CRITICAL': '\033[35m',  # Magenta
        'RESET': '\033[0m'       # Reset
    }
    
    def format(self, record: logging.LogRecord) -> str:
        """Format log record with colors"""
        
        # Add color to level name
        level_color = self.COLORS.get(record.levelname, self.COLORS['RESET'])
        record.levelname = f"{level_color}{record.levelname}{self.COLORS['RESET']}"
        
        # Format the message
        formatted = super().format(record)
        
        return formatted


def setup_logging() -> None:
    """
    Set up logging configuration based on environment settings.
    
    This function configures logging with appropriate formatters,
    handlers, and log levels based on the application configuration.
    """
    settings = get_settings()
    
    # Determine log level
    log_level = getattr(logging, settings.log_level.upper(), logging.INFO)
    
    # Configure root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(log_level)
    
    # Remove existing handlers
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # Create console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(log_level)
    
    # Choose formatter based on environment and format setting
    if settings.log_format.lower() == "json":
        formatter = JSONFormatter()
    else:
        if settings.environment == "development":
            formatter = ColoredFormatter(
                fmt='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                datefmt='%Y-%m-%d %H:%M:%S'
            )
        else:
            formatter = logging.Formatter(
                fmt='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                datefmt='%Y-%m-%d %H:%M:%S'
            )
    
    console_handler.setFormatter(formatter)
    root_logger.addHandler(console_handler)
    
    # Configure specific loggers
    configure_logger_levels(settings)
    
    # Log startup message
    logger = logging.getLogger(__name__)
    logger.info(
        "Logging configured",
        extra={
            "log_level": settings.log_level,
            "log_format": settings.log_format,
            "environment": settings.environment
        }
    )


def configure_logger_levels(settings) -> None:
    """Configure log levels for specific loggers"""
    
    # Application loggers
    logging.getLogger("app").setLevel(logging.DEBUG if settings.debug else logging.INFO)
    
    # Third-party library loggers
    logging.getLogger("uvicorn").setLevel(logging.INFO)
    logging.getLogger("uvicorn.access").setLevel(logging.WARNING if not settings.debug else logging.INFO)
    logging.getLogger("fastapi").setLevel(logging.INFO)
    
    # HTTP client loggers (reduce noise)
    logging.getLogger("httpx").setLevel(logging.WARNING)
    logging.getLogger("aiohttp").setLevel(logging.WARNING)
    logging.getLogger("urllib3").setLevel(logging.WARNING)
    
    # Database loggers
    logging.getLogger("sqlalchemy").setLevel(logging.WARNING)
    logging.getLogger("alembic").setLevel(logging.INFO)
    
    # Payment gateway SDK loggers
    logging.getLogger("stripe").setLevel(logging.WARNING)
    logging.getLogger("square").setLevel(logging.WARNING)


def get_logger(name: str) -> logging.Logger:
    """
    Get a logger instance with the specified name.
    
    Args:
        name: Logger name (typically __name__)
        
    Returns:
        logging.Logger: Configured logger instance
    """
    return logging.getLogger(name)


def log_payment_event(
    logger: logging.Logger,
    event_type: str,
    payment_id: str,
    gateway: str,
    amount: float,
    currency: str,
    status: str,
    **kwargs
) -> None:
    """
    Log a payment-related event with structured data.
    
    Args:
        logger: Logger instance
        event_type: Type of payment event (created, completed, failed, etc.)
        payment_id: Payment identifier
        gateway: Payment gateway used
        amount: Payment amount
        currency: Payment currency
        status: Payment status
        **kwargs: Additional event data
    """
    logger.info(
        f"Payment {event_type}",
        extra={
            "event_type": event_type,
            "payment_id": payment_id,
            "gateway": gateway,
            "amount": amount,
            "currency": currency,
            "status": status,
            **kwargs
        }
    )


def log_gateway_event(
    logger: logging.Logger,
    event_type: str,
    gateway: str,
    operation: str,
    success: bool,
    duration_ms: float,
    **kwargs
) -> None:
    """
    Log a gateway operation event with performance metrics.
    
    Args:
        logger: Logger instance
        event_type: Type of gateway event
        gateway: Gateway identifier
        operation: Operation performed (create_payment, refund, etc.)
        success: Whether the operation was successful
        duration_ms: Operation duration in milliseconds
        **kwargs: Additional event data
    """
    level = logging.INFO if success else logging.ERROR
    
    logger.log(
        level,
        f"Gateway {operation} {'succeeded' if success else 'failed'}",
        extra={
            "event_type": event_type,
            "gateway": gateway,
            "operation": operation,
            "success": success,
            "duration_ms": duration_ms,
            **kwargs
        }
    )


def log_routing_decision(
    logger: logging.Logger,
    payment_id: str,
    selected_gateway: str,
    available_gateways: list,
    routing_strategy: str,
    decision_factors: dict,
    **kwargs
) -> None:
    """
    Log a routing decision with the factors that influenced it.
    
    Args:
        logger: Logger instance
        payment_id: Payment identifier
        selected_gateway: Gateway selected by routing
        available_gateways: List of available gateways
        routing_strategy: Strategy used (rule_based, ai_driven, etc.)
        decision_factors: Factors that influenced the decision
        **kwargs: Additional routing data
    """
    logger.info(
        f"Payment routed to {selected_gateway}",
        extra={
            "event_type": "routing_decision",
            "payment_id": payment_id,
            "selected_gateway": selected_gateway,
            "available_gateways": available_gateways,
            "routing_strategy": routing_strategy,
            "decision_factors": decision_factors,
            **kwargs
        }
    )
