<?php
/**
 * CoinGate Configuration for ELOH Processing
 */

return [
    // API URLs
    'sandbox_url' => 'https://api-sandbox.coingate.com/v2',
    'live_url' => 'https://api.coingate.com/v2',
    
    // API Keys (Get from sandbox.coingate.com for testing)
    'sandbox_api_key' => 'your_sandbox_api_key_here', // Replace with sandbox API key
    'live_api_key' => 'your_live_api_key_here', // Replace with live API key
    
    // Default Settings
    'default_currency' => 'BTC',
    'environment' => 'sandbox', // 'sandbox' or 'live'
    
    // Supported currencies for your business
    'supported_currencies' => [
        'BTC' => [
            'name' => 'Bitcoin',
            'symbol' => 'BTC',
            'icon' => '₿',
            'min_amount' => 0.0001
        ],
        'ETH' => [
            'name' => 'Ethereum',
            'symbol' => 'ETH',
            'icon' => 'Ξ',
            'min_amount' => 0.001
        ],
        'LTC' => [
            'name' => 'Litecoin',
            'symbol' => 'LTC',
            'icon' => 'Ł',
            'min_amount' => 0.01
        ],
        'BCH' => [
            'name' => 'Bitcoin Cash',
            'symbol' => 'BCH',
            'icon' => '₿',
            'min_amount' => 0.001
        ],
        'XRP' => [
            'name' => 'Ripple',
            'symbol' => 'XRP',
            'icon' => 'X',
            'min_amount' => 1
        ],
        'ADA' => [
            'name' => 'Cardano',
            'symbol' => 'ADA',
            'icon' => '₳',
            'min_amount' => 1
        ],
        'DOT' => [
            'name' => 'Polkadot',
            'symbol' => 'DOT',
            'icon' => '●',
            'min_amount' => 0.1
        ],
        'USDT' => [
            'name' => 'Tether',
            'symbol' => 'USDT',
            'icon' => '₮',
            'min_amount' => 1
        ],
        'USDC' => [
            'name' => 'USD Coin',
            'symbol' => 'USDC',
            'icon' => '$',
            'min_amount' => 1
        ],
        'BNB' => [
            'name' => 'Binance Coin',
            'symbol' => 'BNB',
            'icon' => 'B',
            'min_amount' => 0.01
        ],
        'MATIC' => [
            'name' => 'Polygon',
            'symbol' => 'MATIC',
            'icon' => 'M',
            'min_amount' => 1
        ],
        'TRX' => [
            'name' => 'Tron',
            'symbol' => 'TRX',
            'icon' => 'T',
            'min_amount' => 10
        ]
    ]
];
